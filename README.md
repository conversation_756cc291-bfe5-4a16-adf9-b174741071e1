# :gear: Resumo Técnico do Projeto

---

## :video_game: Loop Central de Gameplay

### Zona Central de Mineração Pública

- Sistema de spawn controlado por servidor.
- Ores com níveis de HP escalonados → solo (comum) e boss ores (colaborativos).
- Eventos periódicos alteram a taxa/categorias de spawn.

### <PERSON><PERSON><PERSON>a <PERSON>oal (por jogador)

- Cada jogador tem sua unidade de processamento (plot).
- Função: transformar ores em ResultItems usando RNG.
- RNG modificado por buffs, nível da máquina e raridade/qualidade do insumo.

### Resultados (ResultItems)

- Entidades persistentes, únicas por jogador.
- Podem ser armazenados, vendidos, fundidos ou trocados.
- Cada item tem:
  - Raridade.
  - Valor econômico (coins por segundo).
  - Possíveis modificadores (buffs de luck, variantes elétrico/corrompido/etc.).

### :arrows_counterclockwise: Sistema <PERSON>são (Merge)

- Tabela de combinações (CombinationRules) define: insumos, chance base, resultado.

- Chance de falha crescente em tiers altos: Rare→Epic (~80–90%) até Legendary→Mythic (~10–15%).

- Falhas sempre geram:
    - Item corrompido.
    - Recompensa secundária (Shards, coin refund parcial).

- Retry Ladder:
    - Cada falha pode ser retentada via Robux ou Shards.
    - Cada retry aumenta chance de sucesso ±15–20% até cap em ~95–99%.
    - Custos progressivos (25R$ → 200R$) ou (50 Shards → 800 Shards).

---

## :jigsaw: Progressão / Upgrades

### Máquina de Fusão com Tiers:

- Tier 1: só fusões até Epic.
- Tier 2: libera Legendary.
- Tier 4+: libera Mythic.
- Cada tier adiciona bônus de chance de sucesso e desbloqueia variantes (Glitched, Rainbow).

### Upgrades de jogador:

- Tool/pickaxe tier (mineração mais rápida).
- Workers/auto-miners (automatizam coleta).
- Buff slots (quantos ResultItems ativos aplicam bônus).

---

## :moneybag: Economia

### Coins:


- Obtidos em mineração, drops e renda passiva dos ResultItems.
- Usados em upgrades básicos e compra de workers.

### Shards (moeda premium grindável):

- Ganhados em eventos, boss ores, falhas de fusão e login diário.
- Gastos em retrys, boosters e passes premium.

### Robux:

- Conversão direta em:
  - Pacotes de Shards.
  - Retries imediatos.
  - Gamepasses (tempo infinito, auto-coleta, VIP, etc).

---

## :satellite: Colaboração & Social

- Ore Bosses:
  - HP global por servidor, todos contribuem.
  - Loot tabelado com distribuição justa (participação mínima requerida).

- Buffs de Grupo:
    - Alguns modifiers (ex: Elétrico) aplicam efeitos no mesmo ore para todos participantes → cria valor social.


- Trade / Doação:
    - Seguro (com confirmação dupla).
    - Animação pública de destaque no servidor (flex social + incentivo).


- Leaderboards:
    - Ordenados por coins, shards acumulados, maior rarity alcançada, maior doador.

---

## :date: Ciclo de Vida do Conteúdo

- Events:
    - Repetição de eventos regulares (Ore Rain, Machine Overcharge).
    - Eventos sazonais (Natal, Halloween) → simples troca de skins/modelos.

- Template reskin pipeline:
    - Core loop é genérico → trocar assets (modelos, sons, nomes) gera novo jogo em poucos dias.
    - Exemplos: “Ore → Toilet → Food → Meme”.

---

## :white_check_mark: Pontos Críticos

- O retry ladder é o principal motor de monetização.
- O sistema de buffs e modifiers garante variação e longevidade.
- O loop cooperativo no centro mantém o servidor vivo e dá palco para flex.
- O design modular/template permite reusar a estrutura para novas tendências sem refazer back-end.

## Development

Dependencies:

- [Rokit](https://github.com/rojo-rbx/rokit)
- [Rojo](https://github.com/rojo-rbx/rojo)
  - `rokit add rojo --global`

Run `npm install`

Run `npm run watch`

and on another terminal run `rojo serve`
