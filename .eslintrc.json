{"parser": "@typescript-eslint/parser", "parserOptions": {"jsx": true, "useJSXTextNode": true, "ecmaVersion": 2018, "sourceType": "module", "project": "./tsconfig.json"}, "ignorePatterns": ["out/**", "**/*.luau", "**/*.lua"], "plugins": ["@typescript-eslint"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended"], "rules": {"prefer-const": "off", "array-bracket-newline": "off", "array-element-newline": "off", "linebreak-style": "warn", "comma-style": "warn", "camelcase": "warn"}}