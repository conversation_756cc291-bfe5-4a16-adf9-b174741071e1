{"name": "dungeon-game", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "rbxtsc", "watch": "rbxtsc -w"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@eslint/js": "^9.27.0", "@rbxts/compiler-types": "^3.0.0-types.0", "@rbxts/types": "^1.0.881", "@typescript-eslint/eslint-plugin": "^6.7.5", "@typescript-eslint/parser": "^6.7.5", "eslint": "^8.57.0", "eslint-config-prettier": "^8.3.0", "eslint-plugin-prettier": "^3.4.0", "eslint-plugin-roblox-ts": "^0.0.30", "eslint-plugin-roblox-ts-x": "^1.0.2", "globals": "^16.1.0", "prettier": "^2.3.2", "rbxts-transformer-flamework": "^1.3.2", "roblox-ts": "^3.0.0", "typescript": "5.5.3"}, "dependencies": {"@flamework/components": "^1.3.2", "@flamework/core": "^1.3.2", "@flamework/networking": "^1.3.2", "@rbxts/ceive-im-gizmo": "^3.2.0-ts.0", "@rbxts/centurion": "^1.0.1", "@rbxts/centurion-ui": "^1.0.8", "@rbxts/cmdr": "^1.12.1", "@rbxts/depot": "^1.8.1", "@rbxts/destroyable": "^1.0.9", "@rbxts/fuzzy-search": "^1.0.4", "@rbxts/gizmo": "^2.0.5", "@rbxts/input-actions": "^0.5.0", "@rbxts/ip-data": "^1.0.9", "@rbxts/iris": "^2.2.0-ts.0", "@rbxts/lemon-signal": "^1.10.0-ts.5", "@rbxts/log": "^0.6.3", "@rbxts/maid": "^1.1.0", "@rbxts/number-manipulator": "^1.1.3", "@rbxts/object-utils": "^1.0.4", "@rbxts/octree": "^1.0.1", "@rbxts/profileservice": "^1.4.2", "@rbxts/reflex": "^4.3.1", "@rbxts/refx": "^1.0.0-rc3", "@rbxts/roact": "^1.4.4-ts.0", "@rbxts/simplepath": "^2.2.1-ts.2", "@rbxts/spr": "^2.1.1", "@rbxts/suphi-datastore": "^1.0.0", "@rbxts/t": "^3.1.1", "@rbxts/tableutil": "^2.0.0", "@rbxts/tally-store": "^1.0.3", "@rbxts/ui-labs": "^2.3.8", "@rbxts/vide": "^0.5.5", "@rbxts/vide-charm": "^0.2.0", "@rbxts/viewportmodel": "^1.0.2", "@rbxts/visualize": "^1.1.1", "@rbxts/wcs": "^2.4.1", "@rbxts/zircon": "^1.0.9", "@rbxts/zone-plus": "^3.2.0", "io-serve": "^1.1.2"}}