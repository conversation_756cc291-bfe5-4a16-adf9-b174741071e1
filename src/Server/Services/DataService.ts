import { OnInit, Service } from "@flamework/core";
import { OnPlayerAdded } from "../../Shared/Functions/OnPlayerAdded";
import ProfileSync from "../../Shared/InkLabs/Data";
import { DefaultPlayerData } from "Shared/Data/Defaults/DefaultPlayerData";

@Service()
export class DataService implements OnInit {
  onInit() {
    warn(`Starting ${script.Name}`);

    ProfileSync.Init({
      Structure: DefaultPlayerData,
      Version: 1,
      Debug: true,
      FilterType: "Blacklist",
      FilterStrictness: "Light",
    });

    OnPlayerAdded((player) => {
      warn(`Loading player data for ${player.Name}...`);
      const data = ProfileSync.GetProfile(player, 20);
      warn(`Player data loaded for ${player.Name}.`, data);
    });
  }
}
