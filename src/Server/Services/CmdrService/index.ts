import {OnStart, Service} from "@flamework/core";
import {Centurion} from "@rbxts/centurion";
import {ReplicatedStorage} from "@rbxts/services";

@Service()
export class CmdrService implements OnStart {
    onStart() {
        warn(`Starting ${script.Name}.`);
        const server = Centurion.server({
            registerBuiltInTypes: true
        });

        const commandContainer = script.WaitForChild("Commands");
        server.registry.load(commandContainer, true);

        const typesContainer = ReplicatedStorage
            .WaitForChild("TS")
            .WaitForChild("Components")
            .WaitForChild("Centurion")
            .WaitForChild("Types");

        server.registry.load(typesContainer);
        server.start();
    }
}