import {Dependency} from "@flamework/core";
import {CenturionType, Command, CommandContext, Guard, Register} from "@rbxts/centurion";
import {CreateItemFromEquipment} from "Shared/Resources/Equipment";
import {InventoryService} from "../../../InventoryService";
import {IsAdminGuard} from "../../Guards/AdminPermissionValidation";

@Register()
export class InventoryCommands {
    @Command({
        name: "giveItem",
        description: "Gives an item to a player.",
        arguments: [
            {
                name: "itemId",
                description: "Id of the item to give.",
                type: "itemId"
            },
            {
                name: "amount",
                description: "Amount of the item.",
                type: CenturionType.Integer,
                optional: true
            },
            {
                name: "player",
                description: "The player to give the item to.",
                type: CenturionType.Player,
                optional: true
            }
        ],
        aliases: ["gi", "give"]
    })
    @Guard(IsAdminGuard)
    giveItem(ctx: CommandContext, itemId: string, amount?: number, player?: Player) {
        amount = math.clamp(amount ?? 1, 1, 10);
        player ??= ctx.executor;

        for (let i = 0; i < amount; i++) {
            const item = CreateItemFromEquipment(itemId);
            if (item === undefined) {
                ctx.error("Invalid itemId");
                return;
            }

            const inventoryService = Dependency<InventoryService>();
            inventoryService.GiveItem(player, item);
        }
        ctx.reply(`'${amount}' copies of '${itemId}' have been given to '${player.Name}'.`);
    }
}