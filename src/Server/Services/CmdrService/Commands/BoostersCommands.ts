import {Dependency} from "@flamework/core";
import {CenturionType, Command, CommandContext, Guard, Register} from "@rbxts/centurion";
import {BoosterId} from "../../../../Shared/Resources/Boosters";
import {BoostersService} from "../../BoostersService";
import {IsAdminGuard} from "../Guards/AdminPermissionValidation";

@Register()
export class BoostersCommands {
    @Command({
        name: "booster",
        description: "Gives a given duration of a booster to a player.",
        arguments: [
            {
                name: "boosterId",
                description: "Id of the booster.",
                type: "boosterId"
            },
            {
                name: "duration",
                description: "Booster duration to give.",
                type: CenturionType.Integer,
                optional: true
            },
            {
                name: "player",
                description: "The player to give the item to.",
                type: CenturionType.Player,
                optional: true
            }
        ],
        aliases: ["bos", "boost"]
    })
    @Guard(IsAdminGuard)
    booster(ctx: CommandContext, boosterId: BoosterId, duration?: number, player?: Player) {
        duration = math.clamp(duration ?? 1, 1, 1000000);
        player ??= ctx.executor;

        const boosterService = Dependency<BoostersService>();
        boosterService.GiveBoosterToPlayer(player, boosterId, duration);
        ctx.reply(`Booster '${boosterId}' was increased by '${duration}' seconds to the player '${player}'.`);
    }
}