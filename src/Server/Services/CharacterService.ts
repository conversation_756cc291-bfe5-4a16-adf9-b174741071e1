import { OnStart, Service } from "@flamework/core";
import { RegisterCollisionGroup } from "../../Shared/Components/PhysicsManager";
import { InitRagdollVelocityTracking } from "../../Shared/Functions/InitRagdollVelocityTracking";
import { InitializeRagdollService } from "../../Shared/InkLabs/Ragdoll";

@Service()
export class CharacterService implements OnStart {
  onStart() {
    InitializeRagdollService();
    InitRagdollVelocityTracking();
    RegisterCollisionGroup("Character", ["Character", "Debris"]);
    RegisterCollisionGroup("Debris", ["Character"]);
  }
}
