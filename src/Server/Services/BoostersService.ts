import { OnStart, Service } from "@flamework/core";
import { Jan<PERSON> } from "@rbxts/janitor";
import Shared from "../../Shared/Cmdr";
import { OnPlayerAdded } from "../../Shared/Functions/OnPlayerAdded";
import ProfileSync from "../../Shared/InkLabs/Data";
import { BoosterId, Boosters } from "../../Shared/Resources/Boosters";

@Service()
export class BoostersService implements OnStart {
  onStart() {
    OnPlayerAdded((player) => {
      const janitor = new Janitor();
      const boosterTimeUpdateLoopTask = task.spawn(() => {
        while (task.wait(1)) {
          const playerBoosters = ProfileSync.GetData(player, "Boosters");
          if (playerBoosters === undefined) continue;

          playerBoosters.forEach((duration, boosterId) => {
            if (duration === 1) {
              ProfileSync.DictionaryRemove(player, "Boosters", boosterId);
              return;
            }

            const newDuration = duration - 1;
            ProfileSync.DictionaryInsert(
              player,
              "Boosters",
              newDuration,
              boosterId
            );
          });
        }
      });

      janitor.Add(boosterTimeUpdateLoopTask);
      player.Destroying.Once(() => janitor.Destroy());

      if (Shared.Admins.includes(player.UserId))
        Boosters.forEach((id) => {
          this.GiveBoosterToPlayer(player, id, 11720);
        });
    });
  }

  public PlayerHasBooster(player: Player, boosterId: BoosterId) {
    return ProfileSync.GetData(player, "Boosters")?.has(boosterId);
  }

  public GiveBoosterToPlayer(
    player: Player,
    boosterId: BoosterId,
    durationInSeconds: number
  ) {
    const playerBoosters = ProfileSync.GetData(player, "Boosters", 20);
    if (playerBoosters === undefined) return;

    const duration = (playerBoosters.get(boosterId) ?? 0) + durationInSeconds;
    ProfileSync.DictionaryInsert(player, "Boosters", duration, boosterId);
  }
}
