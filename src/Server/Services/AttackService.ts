import { OnStart, Service } from "@flamework/core";
import { CreateServer } from "@rbxts/wcs";
import { WCSDirectories } from "Shared/Libraries/Directories";

import { GetMouseHit } from "../../Shared/InkLabs/Mouse";

@Service()
export class AttackService implements OnStart {
  private readonly wcs = CreateServer();
  private readonly _initMouse = GetMouseHit();

  onStart() {
    this.wcs.RegisterDirectory(WCSDirectories.Skills);
    this.wcs.RegisterDirectory(WCSDirectories.StatusEffects);
    this.wcs.Start();
  }
}
