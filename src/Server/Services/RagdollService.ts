import { OnStart, Service } from "@flamework/core";
import { CollectionService } from "@rbxts/services";
import { Ragdoll, Unragdoll } from "../../Shared/InkLabs/Ragdoll";
import { RagdollTag } from "../../Shared/Resources/Tags";

@Service()
export class RagdollService implements OnStart {
  onStart(): void {
    CollectionService.GetInstanceAddedSignal(RagdollTag).Connect(
      (x) => x.IsA("Model") && Ragdoll(x)
    );
    CollectionService.GetInstanceRemovedSignal(RagdollTag).Connect(
      (x) => x.IsA("Model") && Unragdoll(x)
    );
  }
}
