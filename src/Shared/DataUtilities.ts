import { Janitor } from "@rbxts/janitor";
import { OnPlayerAdded } from "./Functions/OnPlayerAdded";
import ProfileSync from "./InkLabs/Data";
import {
  TPlayerDataField,
  TPlayerDataValue,
} from "./Data/Abstractions/IPlayerData";

export type TNumericPlayerDataField = TPlayerDataField &
  TPlayerDataValue<TPlayerDataField> extends number
  ? TPlayerDataField
  : never;

export function GlobalDataBind<
  T extends TPlayerDataField,
  U extends TPlayerDataValue<T>
>(dataName: T, callback: (player: Player, value: U) => void) {
  const janitor = new Janitor();
  const onAddedConnection = OnPlayerAdded((player) => {
    const bindDisconnect = ProfileSync.Bind(player, dataName, (value) =>
      callback(player, value as U)
    );
    janitor.Add(() => bindDisconnect());
    warn(`Bound ${dataName} to ${player.Name}`);
  });
  janitor.Add(() => onAddedConnection());
  return () => janitor.Destroy();
}
