import {CollectionService, Workspace} from "@rbxts/services";

import {CharacterModel} from "../Components/CharacterModel";
import {IsAlly} from "../Resources/Attributes";
import {GetDebrisFolder, GetMapFolder} from "./Directories";

export const CreateCFrameFromPositionAndNormal = (position: Vector3, normal: Vector3) => {
    const axisToSub = math.abs(normal.Z) === 1 ? Vector3.xAxis : Vector3.zAxis;
    return CFrame.lookAt(position, position.sub(axisToSub), normal);
};

export const GetRandomNumber = (min: number, max: number) => {
    const rng = new Random();
    return rng.NextNumber(min, max);
};

export const GetRandomAngle = (min: number, max: number) =>
    math.rad(GetRandomNumber(min, max));

export const GetAlliedCharacters = (character: Model, range?: number) => {
    const characters = new Array<Model>();
    characters.push(character)
    CollectionService.GetTagged("Character").forEach((x) => {
        const char = x as Model;

        if (range !== undefined && (char.GetPivot().Position.sub(character.GetPivot().Position)).Magnitude > range)
            return;

        if (IsAlly(character, char)) {
            characters.push(char);
        }
    });

    return characters;
};

export const GetEnemyCharacters = (character: CharacterModel, range?: number) => {
    const characters = new Array<CharacterModel>();
    CollectionService.GetTagged("Character").forEach((x) => {
        if (x === character) return;
        const char = x as CharacterModel;

        if (range !== undefined && (char.GetPivot().Position.sub(character.GetPivot().Position)).Magnitude > range)
            return;

        if (!IsAlly(character, char)) {
            characters.push(char);
        }
    });

    return characters;
};

export const GetMapPartsInRadius = (position: Vector3 | CFrame, radius: number | Vector3) => {
    const parts = new Array<BasePart>();
    const params = new OverlapParams();

    params.FilterDescendantsInstances = [GetMapFolder()];
    params.FilterType = Enum.RaycastFilterType.Include;

    const partsInBox = Workspace.GetPartBoundsInBox(
        typeIs(position, "Vector3") ? new CFrame(position) : position,
        typeIs(radius, "number") ? new Vector3(radius, radius, radius) : radius,
        params
    );
    partsInBox.forEach((part) => {
        if (part.IsDescendantOf(GetDebrisFolder())) return;
        parts.push(part);
    });

    return parts;
};

export const GetCharactersInRadius = (
    position: Vector3 | CFrame, radius: number | Vector3, ignoreList?: Instance[]) => {
    const characters = new Array<CharacterModel>();
    if (typeIs(radius, "number")) {
        CollectionService.GetTagged("Character").forEach((x) => {
            const char = x as CharacterModel;
            if (ignoreList && ignoreList.includes(char)) {
                return;
            }

            if (char.PrimaryPart && (char.PrimaryPart.Position.sub(typeIs(position, "Vector3")
                ? position
                : position.Position).Magnitude <= radius)) {
                characters.push(char);
            }
        });
    } else {
        const params = new OverlapParams();
        params.FilterDescendantsInstances = CollectionService.GetTagged("Character")
            .filter(x => !ignoreList || !ignoreList.includes(x));
        params.FilterType = Enum.RaycastFilterType.Include;

        const partsInBox = Workspace.GetPartBoundsInBox(
            typeIs(position, "Vector3") ? new CFrame(position) : position,
            radius,
            params
        );
        partsInBox.forEach((part) => {
            const char = part.Parent as CharacterModel;
            if (char && CollectionService.HasTag(char, "Character") && char.PrimaryPart) {
                characters.push(char as CharacterModel);
            }
        });
    }

    return characters;
};

export const GetMapRaycastParams = () => {
    const params = new RaycastParams();
    params.FilterType = Enum.RaycastFilterType.Include;
    params.FilterDescendantsInstances = [GetMapFolder()];
    return params;
};

export const GetMapExcludeRaycastParams = () => {
    const params = new RaycastParams();
    params.FilterType = Enum.RaycastFilterType.Exclude;
    params.FilterDescendantsInstances = [GetMapFolder()];
    return params;
};

export const GetMapOverlapParams = () => {
    const params = new OverlapParams();
    params.FilterType = Enum.RaycastFilterType.Include;
    params.FilterDescendantsInstances = [GetMapFolder()];
    return params;
};

export const CastReflectingRayOnMap = (
    origin: Vector3, direction: Vector3, distance?: number, maxReflections?: number) => {
    const params = GetMapRaycastParams();

    let rayDirection = direction.mul((distance ?? 1));
    let initialRaycast = Workspace.Raycast(origin, rayDirection, params);

    const points: Vector3[] = [];
    const maxPoints = maxReflections ?? 3;

    const getNextPoint = (previousPoint: Vector3, previousLookVector: Vector3) => {
        let newRay = CastRayOnMap(previousPoint, previousLookVector, distance);
        if (!newRay) return;
        points.push(newRay.Position);

        if (points.size() >= maxPoints) {
            return;
        }

        const reflectedVector = previousLookVector.sub(newRay.Normal.mul(2 * previousLookVector.Dot(newRay.Normal)));
        getNextPoint(newRay.Position, reflectedVector);
    };

    if (initialRaycast) {
        points.push(initialRaycast.Position);
        getNextPoint(initialRaycast.Position, origin.sub(initialRaycast.Position).Unit);
    }

    return points;
};

export const CastRayOnMap = (origin: Vector3, direction: Vector3, distance?: number) => {
    const params = GetMapRaycastParams();
    let rayDirection = direction.mul((distance ?? 1));
    return Workspace.Raycast(origin, rayDirection, params);
};