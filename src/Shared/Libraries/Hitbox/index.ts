import { Janitor } from "@rbxts/janitor";
import { HttpService } from "@rbxts/services";
import { Character } from "@rbxts/wcs";
import { GetCharactersInRadius } from "../SpatialLib";
import {
  IsInAlliedFaction,
  TFactionId,
} from "Shared/Resources/Combat/Factions";
import { GetCharacterFactions } from "Shared/Resources/Attributes";

interface IHitboxDamageProps {
  BaseDamage: number;
  CriticalChance: number;
  CriticalMultiplier: number;
  Knockback: number;
  KnockbackType: "Directional" | "Radial";
  Stun: number;
  StunFilter: "All" | "IgnorePlayers";
  Ragdoll?: number;
  BlockBreak?: boolean;
  LimitHitCount?: boolean;
  IgnoreTags?: string[];
}

interface IHitboxConstructorProps {
  Id?: string;
  Actor?: Character;
  Size: Vector3;
  CFrame: CFrame;
  Damage: IHitboxDamageProps;
}

interface IHitboxObjectConstructorProps {
  CFrame: CFrame;
  Size: Vector3;
}

export class HitboxObject {
  public CFrame: CFrame;
  public Size: Vector3;
  public CharactersHit: Character[] = [];

  constructor(props: IHitboxObjectConstructorProps) {
    this.CFrame = props.CFrame;
    this.Size = props.Size;
    this.CharactersHit = [];
  }

  public ClearCharactersHit() {
    this.CharactersHit = [];
  }

  public HasCharacterBeenHit(character: Character): boolean {
    return this.CharactersHit.includes(character);
  }

  public AddCharacterHit(character: Character) {
    if (this.HasCharacterBeenHit(character)) {
      return;
    }
    this.CharactersHit.push(character);
  }

  public GetCharactersInBounds(): Character[] {
    return GetCharactersInRadius(
      this.CFrame,
      this.Size,
      this.CharactersHit.map((x) => x.Instance)
    )
      .filter(
        (x) =>
          Character.GetCharacterFromInstance(x) !== undefined &&
          this.isCharacterAlive(x)
      )
      .map((x) => Character.GetCharacterFromInstance(x)!);
  }

  private isCharacterAlive(characterModel: Model) {
    const humanoid = characterModel.FindFirstChild("Humanoid") as
      | Humanoid
      | undefined;
    return humanoid !== undefined && humanoid.Health > 0;
  }
}

export class Hitbox {
  public readonly Id: string;
  protected readonly Janitor: Janitor;
  protected readonly Actor: Character | undefined;
  protected readonly HitboxObject: HitboxObject;
  protected readonly Damage: IHitboxDamageProps;

  constructor(props: IHitboxConstructorProps) {
    this.Id = props.Id ?? HttpService.GenerateGUID(false);
    this.Janitor = new Janitor();
    this.Actor = props.Actor;
    this.HitboxObject = new HitboxObject({
      CFrame: props.CFrame,
      Size: props.Size,
    });
    this.Damage = props.Damage;
  }

  protected CanDamageCharacter(character: Character): boolean {
    const characterFactions = this.GetCharacterFactions(character);
    const actorFactions = this.GetCharacterFactions();

    if (IsInAlliedFaction(characterFactions, actorFactions)) return false;

    return true;
  }

  protected IsActorPlayer(): boolean {
    return this.Actor?.Player !== undefined;
  }

  protected IsActorAnNPC(): boolean {
    return !this.IsActorPlayer();
  }

  public GetCharactersInBounds(): Character[] {
    return this.HitboxObject.GetCharactersInBounds();
  }

  public ClearCharactersHit() {
    this.HitboxObject.ClearCharactersHit();
  }

  public HasCharacterBeenHit(character: Character): boolean {
    return this.HitboxObject.HasCharacterBeenHit(character);
  }

  public AddCharacterHit(character: Character) {
    this.HitboxObject.AddCharacterHit(character);
  }

  public GetCFrame(): CFrame {
    return this.HitboxObject.CFrame;
  }

  public GetSize(): Vector3 {
    return this.HitboxObject.Size;
  }

  public GetCharactersHit(): Character[] {
    return this.HitboxObject.CharactersHit;
  }

  public GetCharactersHitCount(): number {
    return math.max(this.HitboxObject.CharactersHit.size() - 1, 0);
  }

  public GetCharacterFactions(character?: Character): TFactionId[] {
    character ??= this.Actor;
    if (character === undefined) return [];

    return GetCharacterFactions(character.Instance);
  }
}
