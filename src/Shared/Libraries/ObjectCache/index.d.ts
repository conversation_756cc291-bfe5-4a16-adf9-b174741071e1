declare class ObjectCache {
    constructor(template: BasePart, cacheSize?: number, cachesContainer?: Folder);

    GetPart(partCFrame?: CFrame): BasePart;

    ReturnPart(partToReturn: BasePart): void;

    IsInUse(partFromCache: BasePart): boolean;

    Update(): void;

    ExpandCache(amount: number): void;

    SetExpandAmount(amount: number): void;

    Destroy(): void;
}

export default ObjectCache;