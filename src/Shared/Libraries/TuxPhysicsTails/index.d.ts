// Initializes the physics calculations, if they are not initialized yet.
export declare function ensureReady(): void;

export declare const setUpPhysicsTail = {
    simple: (tailPart: BasePart, rootPart: BasePart, offset?: Attachment | CFrame) => void;
    onWeld: {
        part1AsTheTailPart: (weld: Weld, character: Model, pivot?: Attachment | CFrame) => void;
    };
    createWeldForMe: {
        fromOffsetAndPivot: (tailPart: BasePart, rootPart: BasePart, offset?: Attachment | CFrame, pivot?: Attachment | CFrame, character?: Model) => void;
    }
}