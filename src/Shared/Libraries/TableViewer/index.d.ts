declare class TableViewer<TData extends unknown[] | Map<unknown, unknown> | unknown> {
    Data: TData;
    Options: {
        Title: string;
        AppendWrenchEmoji: boolean;
        ShowTableValue: boolean;
        FilterOnType: boolean;
        CaseInsensitive: boolean;
        OrderTablesBasedOffNumberOfElements: boolean;
        ShowExtraAnimationTrackData: boolean;
        Settings: {
            StartingSize: Vector2;
            MinSize: Vector2;
            MaxSize: Vector2;
            Expanded: boolean;
            NumberPrecision: number;
            Font: Enum.Font;
            TextTruncateType: Enum.TextTruncate;
            TypeOrdering: Record<"boolean" | "number" | "string" | "table" | "function" | "Instance" | "Other", number>;
            SyntaxHighlighting: Record<"Default" | "boolean" | "number" | "string" | "table" | "function" | "Instance" | "Other", Color3>;
        };
    };
    Update: (this: TableViewer<TData>, newData: TData) => void;
    constructor(data: TData);
}

export = TableViewer;