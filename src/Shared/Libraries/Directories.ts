import {ReplicatedStorage, Workspace} from "@rbxts/services";

function getOrCreateFolder(name: string, parent: Instance) {
    const folder = parent.FindFirstChild(name);
    if (folder && folder.IsA("Folder")) return folder;

    const newFolder = new Instance("Folder");
    newFolder.Name = name;
    newFolder.Parent = parent;
    return newFolder;
}

export const GetDirectory = (name: string, parent: Instance) =>
    getOrCreateFolder(name, parent);
export const GetNPCsFolder = () => GetDirectory("NPCs", Workspace);
export const GetPlayerCharactersFolder = () =>
    GetDirectory("Characters", Workspace);
export const GetDebrisFolder = () => GetDirectory("Debris", Workspace);
export const GetMapFolder = () => GetDirectory("Map", Workspace);
export const AssetsFolder = GetDirectory("Assets", ReplicatedStorage);
export const SkillEffectsStorage = GetDirectory("Skills", AssetsFolder);
export const MiscEffectsStorage = GetDirectory("Misc", AssetsFolder);
export const ToolsFolder = GetDirectory("Tools", AssetsFolder);
export const GetSound = (soundName: string) => GetDirectory("Sounds", AssetsFolder)
    .FindFirstChild(soundName) as Sound | undefined;
export const EffectsFolder = GetDirectory("FX", AssetsFolder);
export const GenericEffectsFolder = GetDirectory("Generic", EffectsFolder);
export const GuiAssetsFolder = GetDirectory("GUI", AssetsFolder);

export const ProjectilesFolder = GetDirectory("Projectiles", AssetsFolder);

export const SkillEffectsDirectory = {
    Fire: GetDirectory("Fire", SkillEffectsStorage),
    General: GetDirectory("General", SkillEffectsStorage),
    Plasma: GetDirectory("Plasma", SkillEffectsStorage)
};

const WCSRoot = ReplicatedStorage.WaitForChild("TS").WaitForChild("Combat");
export const WCSDirectories = {
    Visuals: GetDirectory("Visuals", WCSRoot),
    Skills: GetDirectory("Skills", WCSRoot),
    Movesets: GetDirectory("Movesets", WCSRoot),
    StatusEffects: GetDirectory("StatusEffects", WCSRoot)
};