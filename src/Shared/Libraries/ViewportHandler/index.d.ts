import {CharacterModel} from "../../Components/CharacterModel";

declare class ObjectHandler {
    Destroy: (this: ObjectHandler) => void;
    SetFPS: (this: ObjectHandler, fps: number) => void;
    Pause: (this: ObjectHandler) => void;
    Resume: (this: ObjectHandler) => void;
    Hide: (this: ObjectHandler) => void;
    Show: (this: ObjectHandler) => void;
    Refresh: (this: ObjectHandler) => void;
}

declare class ViewportHandler {
    Refresh: (this: ViewportHandler) => void;
    Pause: (this: ViewportHandler) => void;
    Resume: (this: ViewportHandler) => void;
    Hide: (this: ViewportHandler) => void;
    Show: (this: ViewportHandler) => void;
    RenderObject: (this: ViewportHandler, object: BasePart, fps: number, parent: Frame) => ObjectHandler;
    RenderHumanoid: (this: ViewportHandler, character: CharacterModel, fps: number, parent: Frame) => ObjectHandler;
    Destroy: (this: <PERSON>portHandler) => void;
    constructor(frame: Frame);
}

export = ViewportHandler;