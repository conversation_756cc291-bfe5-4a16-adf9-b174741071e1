import {RandomColorHSV} from "../InkLabs/Random";
import Collector from "./Collector";
import {GetDebrisFolder, SkillEffectsDirectory} from "./Directories";
import {CreateCFrameFromPositionAndNormal} from "./SpatialLib";
import {HitTag, StaticTag} from "./TagLib";

/**
 * Union type representing all visual effect types that can be manipulated.
 */
type TEffect = ParticleEmitter | Beam | Trail;

/**
 * Creates a hitmark effect at the position of a raycast hit.
 * The hitmark is oriented based on the surface normal and automatically cleaned up.
 * @param raycastResult - The raycast result containing position and normal data
 * @param color - Optional color to apply to the hitmark particles
 */
export const CreateHitmarkFromRaycastResult = (
    raycastResult: RaycastResult,
    color?: Color3
) => {
    const effectsFolder = SkillEffectsDirectory.General;
    const hitmark = effectsFolder.WaitForChild("Hitmark").Clone() as BasePart;
    hitmark.CFrame = CreateCFrameFromPositionAndNormal(
        raycastResult.Position,
        raycastResult.Normal
    );
    hitmark.Parent = GetDebrisFolder();
    if (color !== undefined) {
        hitmark.GetChildren().forEach((x) =>
            ChangeEffectColor(x as ParticleEmitter, color)
        );
    }

    EmitChildren(hitmark);
    Collector.AddItem(hitmark, 5);
};

/**
 * Emits particles from a ParticleEmitter with support for custom timing attributes.
 * Supports EmitCount, EmitDelay, and EmitDuration attributes for advanced control.
 * @param emitter - The ParticleEmitter to emit from
 * @returns Total lifetime of the emission including delays and particle lifetime
 */
export const EmitParticleEmitter = (emitter: ParticleEmitter) => {
    const emitCount = emitter.GetAttribute("EmitCount") as number | undefined ??
        1;
    const emitDelay = emitter.GetAttribute("EmitDelay") as number | undefined ??
        0;
    const emitDuration =
        emitter.GetAttribute("EmitDuration") as number | undefined ?? 0;

    const emitParticle = () => {
        if (emitDuration <= 0) {
            emitter.Emit(emitCount);
        } else {
            emitter.Enabled = true;
            task.delay(emitDuration, () => {
                if (!emitter) return;
                emitter.Enabled = false;
            });
        }
    };

    if (emitDelay <= 0) {
        emitParticle();
    } else {
        task.delay(emitDelay, emitParticle);
    }

    return emitDuration + emitDelay + emitter.Lifetime.Max;
};

/**
 * Calculates the maximum lifetime across a group of particle emitters.
 * Takes into account emit delays, durations, and particle lifetimes.
 * @param emitters - Array of ParticleEmitters to analyze
 * @returns Maximum total lifetime across all emitters
 */
export const GetEmitterGroupMaxLifetime = (emitters: ParticleEmitter[]) => {
    let maxLifetime = 0;
    emitters.forEach((emitter) => {
        const emitDelay =
            emitter.GetAttribute("EmitDelay") as number | undefined ?? 0;
        const emitDuration =
            emitter.GetAttribute("EmitDuration") as number | undefined ?? 0;
        const emitterLifetime = emitDuration + emitDelay + emitter.Lifetime.Max;
        maxLifetime = (emitterLifetime > maxLifetime)
            ? emitterLifetime
            : maxLifetime;
    });

    return maxLifetime;
};

/**
 * Emits or plays a group of effects (ParticleEmitters and Sounds).
 * For ParticleEmitters, calculates and returns the maximum lifetime.
 * @param emitters - Array of ParticleEmitters or Sounds to activate
 * @returns Maximum lifetime of all particle effects in the group
 */
export const EmitEffectGroup = (
    emitters: ParticleEmitter[] | Sound[]
): number => {
    let maxLifetime = 0;
    emitters.forEach((effect) => {
        if (effect.IsA("ParticleEmitter")) {
            const emitterLifetime = EmitParticleEmitter(effect);
            maxLifetime = (emitterLifetime > maxLifetime)
                ? emitterLifetime
                : maxLifetime;
        } else {
            effect.Play();
        }
    });

    return maxLifetime;
};

/**
 * Toggles the enabled state of visual effects with a specific tag.
 * Affects ParticleEmitters, Beams, and Trails in descendants.
 * @param instance - The instance to search for effects
 * @param toggle - Whether to enable (true) or disable (false) the effects
 * @param tag - The tag to filter effects by
 */
export const ToggleEffectsWithTag = (
    instance: Instance,
    toggle: boolean,
    tag: string
) => instance !== undefined &&
    instance.GetDescendants().forEach((x) =>
        (x.IsA("ParticleEmitter") || x.IsA("Beam") || x.IsA("Trail")) &&
        x.HasTag(tag)
            ? x.Enabled = toggle
            : undefined
    );

/**
 * Emits all ParticleEmitters with a specific tag in the instance's descendants.
 * @param instance - The instance to search for particle emitters
 * @param tag - The tag to filter particle emitters by
 */
export const EmitEffectsWithTag = (instance: Instance, tag: string) =>
    instance !== undefined &&
    instance.GetDescendants().forEach((x) =>
        x.IsA("ParticleEmitter") && x.HasTag(tag)
            ? EmitParticleEmitter(x)
            : undefined
    );

/**
 * Emits all ParticleEmitter children of an instance.
 * @param instance - The instance whose children to emit
 * @returns Total maximum lifetime of all emitted particles
 */
export const EmitChildren = (instance: Instance) => {
    const emitters = instance.GetChildren().filter((x) =>
        x.IsA("ParticleEmitter")
    );

    instance.GetChildren().filter((x) =>
        x.IsA("Sound")
    ).forEach((x) => {
        x.PlaybackSpeed *= new Random().NextNumber(0.95, 1.05);
        x.Play();
    });

    const maxLifetime = emitters.reduce((t, x) => t + x.Lifetime.Max, 0);
    emitters.forEach(EmitParticleEmitter);
    return maxLifetime;
};

/**
 * Emits all ParticleEmitter descendants of an instance.
 * @param instance - The instance whose descendants to emit
 */
export const EmitDescendants = (instance: Instance) =>
    instance !== undefined &&
    instance.GetDescendants().forEach((x) =>
        x.IsA("ParticleEmitter") &&
        EmitParticleEmitter(x)
    );

/**
 * Toggles the enabled state of all visual effect children.
 * Affects ParticleEmitters, Beams, and Trails.
 * @param instance - The instance whose children to toggle
 * @param toggle - Whether to enable (true) or disable (false) the effects
 */
export const ToggleChildren = (instance: Instance, toggle: boolean) =>
    instance !== undefined &&
    instance.GetChildren().forEach((x) =>
        x.IsA("ParticleEmitter") || x.IsA("Beam") || x.IsA("Trail")
            ? x.Enabled = toggle
            : undefined
    );

/**
 * Toggles the enabled state of all visual effect descendants.
 * Affects ParticleEmitters, Beams, and Trails.
 * @param instance - The instance whose descendants to toggle
 * @param toggle - Whether to enable (true) or disable (false) the effects
 */
export const ToggleDescendants = (instance: Instance, toggle: boolean) =>
    instance !== undefined &&
    instance.GetDescendants().forEach((x) =>
        x.IsA("ParticleEmitter") || x.IsA("Beam") || x.IsA("Trail")
            ? x.Enabled = toggle
            : undefined
    );

/**
 * Toggles static emitters (effects with StaticTag) in an instance.
 * @param instance - The instance to search for static emitters
 * @param toggle - Whether to enable (true) or disable (false) the effects
 */
export const ToggleStaticEmitters = (instance: Instance, toggle: boolean) =>
    ToggleEffectsWithTag(instance, toggle, StaticTag);

/**
 * Emits hit emitters (effects with HitTag) in an instance.
 * @param instance - The instance to search for hit emitters
 */
export const EmitHitEmitters = (instance: Instance) =>
    EmitEffectsWithTag(instance, HitTag);

/**
 * Changes the color of a visual effect based on its color tags.
 * Supports different color patterns: Color, ColorBlack, BlackColor, ColorColor.
 * @param effect - The visual effect to change color for
 * @param color1 - Primary color to apply
 * @param color2 - Secondary color for gradient effects (optional)
 */
export const ChangeEffectColor = (
    effect: TEffect,
    color1: Color3,
    color2?: Color3
) => {
    if (effect.HasTag("Color")) {
        effect.Color = new ColorSequence(color1);
    }
    if (effect.HasTag("ColorBlack")) {
        effect.Color = new ColorSequence(color1, new Color3());
    }
    if (effect.HasTag("BlackColor")) {
        effect.Color = new ColorSequence(new Color3(), color1);
    }
    if (effect.HasTag("ColorColor")) {
        effect.Color = new ColorSequence(color1, color2 ?? color1);
    }
};

/**
 * Generates a random Color3 with predefined HSV ranges for consistent aesthetics.
 * @returns A randomly generated Color3 with good saturation and brightness
 */
export const GetRandomColor3 = () => RandomColorHSV(0, 1, 0.6, 1, 0.65, 1);

/**
 * Plays all Sound descendants of an instance.
 * @param instance - The instance to search for sounds
 */
export const PlayDescendantSounds = (instance: Instance) =>
    instance.GetDescendants().forEach((x) => x.IsA("Sound") && x.Play());

/**
 * Plays all Sound children of an instance.
 * @param instance - The instance whose children to play
 */
export const PlayChildrenSounds = (instance: Instance) =>
    instance.GetChildren().forEach((x) => x.IsA("Sound") && x.Play());

/**
 * Plays all Sound children with a specific tag.
 * @param instance - The instance whose children to search
 * @param tag - The tag to filter sounds by
 */
export const PlayChildrenSoundsWithTag = (instance: Instance, tag: string) =>
    instance.GetChildren().forEach((x) =>
        x.IsA("Sound") && x.HasTag(tag) && x.Play()
    );

/**
 * Plays all Sound descendants with a specific tag.
 * @param instance - The instance whose descendants to search
 * @param tag - The tag to filter sounds by
 */
export const PlayDescendantSoundsWithTag = (instance: Instance, tag: string) =>
    instance.GetDescendants().forEach((x) =>
        x.IsA("Sound") && x.HasTag(tag) && x.Play()
    );

/**
 * Stops all Sound descendants of an instance.
 * @param instance - The instance to search for sounds
 */
export const StopDescendantSounds = (instance: Instance) =>
    instance.GetDescendants().forEach((x) => x.IsA("Sound") && x.Stop());

/**
 * Stops all Sound children of an instance.
 * @param instance - The instance whose children to stop
 */
export const StopChildrenSounds = (instance: Instance) =>
    instance.GetChildren().forEach((x) => x.IsA("Sound") && x.Stop());

/**
 * Stops all Sound children with a specific tag.
 * @param instance - The instance whose children to search
 * @param tag - The tag to filter sounds by
 */
export const StopChildrenSoundsWithTag = (instance: Instance, tag: string) =>
    instance.GetChildren().forEach((x) =>
        x.IsA("Sound") && x.HasTag(tag) && x.Stop()
    );

/**
 * Stops all Sound descendants with a specific tag.
 * @param instance - The instance whose descendants to search
 * @param tag - The tag to filter sounds by
 */
export const StopDescendantSoundsWithTag = (instance: Instance, tag: string) =>
    instance.GetDescendants().forEach((x) =>
        x.IsA("Sound") && x.HasTag(tag) && x.Stop()
    );