declare class Component3D {
    enabled: boolean;
    component2D?: GuiObject;
    surfaceGui?: SurfaceGui;
    parent2D?: GuiObject;
    screen3D: Screen3D;
    parent3D?: Component3D;
    offset: CFrame;
    viewportSize: Vector2;
    conn?: RBXScriptConnection;
    Enable: (this: Component3D) => Component3D;
    Disable: (this: Component3D) => Component3D;
    GetViewportSize: (this: Component3D) => Vector2;
    UDim2ToCFrame: (this: Component3D, position2d: UDim2) => CFrame;
    GetStudsScreenSize: (this: Component3D, viewportSize: Vector2) => Vector3;
    RecomputeParent: (this: Component3D) => Component3D;
    ReadWorldCFrame: (this: Component3D) => CFrame;
}

declare class Screen3D {
    GetRealCanvasSize: (this: Screen3D) => Vector2;
    GetInset: (this: Screen3D) => Vector2;
    GetInsetCanvasSize: (this: Screen3D) => Vector2;
    GetIntendedCanvasSize: (this: Screen3D) => Vector2;
    GetComponent3D: (this: Screen3D, guiObject: GuiObject) => Component3D;
    constructor(screenGui: ScreenGui, displayDistance: number);
}

export = Screen3D;