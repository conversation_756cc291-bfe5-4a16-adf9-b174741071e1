import {HttpService, ReplicatedStorage} from "@rbxts/services";
import {GetDirectory} from "./Directories";

/** Folder where animation instances are cached for reuse */
const AnimationCacheFolder = GetDirectory("AnimationCache", ReplicatedStorage);
/** Global container storing all animation containers by their unique IDs */
const GlobalContainer = new Map<string, AnimationContainer>();
/** Attribute name used to store animation container ID on character models */
const AttributeName = "AnimationContainerId";

/**
 * Retrieves or creates an Animation instance for the given animation ID.
 * Caches animation instances to avoid creating duplicates.
 * @param animationId - The Roblox asset ID or full rbxassetid URL
 * @returns Animation instance ready to be loaded by an Animator
 */
function GetAnimationInstanceFromId(animationId: string) {
    let animationInstance = AnimationCacheFolder.FindFirstChild(animationId) as
        | Animation
        | undefined;
    if (animationInstance !== undefined) {
        return animationInstance;
    }

    animationInstance = new Instance("Animation");
    animationInstance.Name = animationId;
    animationInstance.AnimationId = animationId;
    animationInstance.Parent = AnimationCacheFolder;
    return animationInstance;
}

/**
 * Represents a registered animation with its track and metadata.
 */
export type TRegisteredAnimation = {
    /** The loaded animation track that can be played */
    Track: AnimationTrack;
    /** Context group this animation belongs to (e.g., "Combat", "Movement") */
    Context: string;
    /** Priority level for animation conflicts (higher = more important) */
    Priority: number;
};

/**
 * Manages animations for a single character model.
 * Handles loading, playing, stopping, and priority management of animations.
 */
class AnimationContainer {
    /** Unique identifier for this animation container */
    private readonly id: string;
    /** The character's Animator component */
    private readonly animator: Animator;
    /** Map of animation names to their registered animation data */
    private readonly tracks: Map<string, TRegisteredAnimation> = new Map();

    /**
     * Creates a new animation container for the specified character.
     * @param character - The character model to manage animations for
     */
    constructor(character: Model) {
        this.id = HttpService.GenerateGUID(false);
        this.animator = character.FindFirstChild("Animator", true) as Animator;
        this.animator ??= character.WaitForChild("Humanoid", 10)
            ?.WaitForChild("Animator", 10) as Animator;

        if (this.animator === undefined) {
            error(`Could not load animator for character '${character.Name}'.`);
        }

        character.SetAttribute(AttributeName, this.id);
        GlobalContainer.set(this.id, this);
        character.Destroying.Once(() => {
            GlobalContainer.delete(this.id);
        });
    }

    /**
     * Retrieves a loaded animation by name.
     * @param animationName - The name of the animation to get
     * @returns The registered animation data if found, undefined otherwise
     */
    public GetLoadedAnimation(animationName: string) {
        return this.tracks.get(animationName);
    }

    /**
     * Stops a playing animation by name.
     * @param animationName - The name of the animation to stop
     * @param fadeTime - Time in seconds to fade out the animation (optional)
     */
    public StopAnimation(animationName: string, fadeTime?: number): void {
        if (this.GetLoadedAnimation(animationName)?.Track.IsPlaying === true)
            this.GetLoadedAnimation(animationName)?.Track.Stop(fadeTime);
    }

    /**
     * Removes an animation from the container, stopping it first.
     * @param animationName - The name of the animation to remove
     * @param fadeTime - Time in seconds to fade out before removal (optional)
     */
    public RemoveAnimation(animationName: string, fadeTime?: number) {
        this.StopAnimation(animationName, fadeTime ?? 0);
        if (fadeTime !== undefined) {
            task.delay(
                fadeTime,
                () => this.DeleteAnimationTrack(animationName)
            );
        } else {
            this.DeleteAnimationTrack(animationName);
        }
    }

    /**
     * Loads an animation into the container for later playback.
     * If an animation with the same name exists, it will be replaced.
     * @param animationName - Unique name to identify this animation
     * @param animationId - Roblox asset ID (string or number)
     * @param context - Context group for priority management (default: "Global")
     * @param priority - Priority level for animation conflicts (default: 10)
     * @returns The registered animation data
     */
    public LoadAnimation(
        animationName: string,
        animationId: string | number,
        context: string = "Global",
        priority: number = 10,
        animationPriority?: Enum.AnimationPriority
    ) {
        const id = typeIs(animationId, "string")
            ? animationId
            : "rbxassetid://" + tostring(animationId);
        if (this.tracks.get(animationName) !== undefined) {
            if (
                this.tracks.get(animationName)!.Track.Animation!.AnimationId ===
                id
            ) {
                return;
            }
            this.RemoveAnimation(animationName);
        }

        const animationInstance = GetAnimationInstanceFromId(id);
        const track = this.animator.LoadAnimation(animationInstance);
        this.tracks.set(animationName, {
            Track: track,
            Priority: priority,
            Context: context
        });

        if (animationPriority !== undefined)
            track.Priority = animationPriority;

        return this.tracks.get(animationName)!;
    }

    /**
     * Plays a loaded animation with optional properties.
     * Handles priority conflicts by stopping lower priority animations in the same context.
     * @param animationName - The name of the animation to play
     * @param props - Optional playback properties
     */
    public PlayAnimation(
        animationName: string,
        props?: {
            /** Time in seconds to fade in the animation */
            FadeTime?: number;
            /** Weight/strength of the animation (0-1) */
            Weight?: number;
            /** Playback speed multiplier */
            Speed?: number;
        }
    ) {
        if (this.animator.Parent?.ClassName === "Humanoid" && (this.animator.Parent as Humanoid).Health <= 0)
            return;

        const animation = this.GetLoadedAnimation(animationName)!;
        if (animation === undefined) {
            warn(
                `Animation ${animationName} not loaded! Make sure to load an animation before playing it!`
            );
            return;
        }

        if (animation.Track.IsPlaying && animation.Track.Looped) {
            return;
        }

        for (const [name, otherAnimation] of this.tracks) {
            if (name === animationName || !otherAnimation.Track.IsPlaying) {
                continue;
            }

            if (otherAnimation.Context !== animation.Context) {
                continue;
            }

            if (otherAnimation.Priority > animation.Priority) {
                return;
            }

            if (otherAnimation.Context === animation.Context) {
                otherAnimation.Track.Stop(0.1);
            }
        }

        animation.Track.Play(props?.FadeTime, props?.Weight, props?.Speed);
        return animation;
    }

    /**
     * Destroys and removes an animation track from the container.
     * @param animationName - The name of the animation to delete
     */
    private DeleteAnimationTrack(animationName: string) {
        this.tracks.get(animationName)?.Track.Destroy();
        this.tracks.delete(animationName);
    }
}

/**
 * Gets or creates an animation container for the specified character.
 * @param character - The character model to get the container for
 * @returns The animation container for the character
 */
export function GetAnimationContainerFromCharacter(character: Model) {
    const id = character.GetAttribute(AttributeName) as string | undefined;
    return id !== undefined
        ? GlobalContainer.get(id) ?? new AnimationContainer(character)
        : new AnimationContainer(character);
}

/**
 * Removes an animation from a character, stopping it first.
 * @param character - The character model
 * @param animationName - The name of the animation to remove
 * @param fadeTime - Time in seconds to fade out before removal (optional)
 */
export function RemoveAnimation(
    character: Model,
    animationName: string,
    fadeTime?: number
) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.RemoveAnimation(animationName, fadeTime);
}

/**
 * Stops a playing animation on a character.
 * @param character - The character model
 * @param animationName - The name of the animation to stop
 * @param fadeTime - Time in seconds to fade out the animation (optional)
 */
export function StopAnimation(
    character: Model,
    animationName: string,
    fadeTime?: number
) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.StopAnimation(animationName, fadeTime);
}

/**
 * Loads an animation for a character.
 * @param character - The character model
 * @param animationName - Unique name to identify this animation
 * @param animationId - Roblox asset ID (string or number)
 * @param context - Context group for priority management (default: "Global")
 * @param priority - Priority level for animation conflicts (default: 10)
 * @returns The registered animation data
 */
export function LoadAnimation(
    character: Model,
    animationName: string,
    animationId: string | number,
    context: string = "Global",
    priority: number = 10,
    animationPriority?: Enum.AnimationPriority
) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.LoadAnimation(
        animationName,
        animationId,
        context,
        priority,
        animationPriority
    );
}

/**
 * Retrieves a loaded animation from a character.
 * @param character - The character model
 * @param animationName - The name of the animation to get
 * @returns The registered animation data if found, undefined otherwise
 */
export function GetLoadedAnimation(character: Model, animationName: string) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.GetLoadedAnimation(animationName);
}

/**
 * Plays a loaded animation on a character with optional properties.
 * @param character - The character model
 * @param animationName - The name of the animation to play
 * @param props - Optional playback properties
 */
export function PlayAnimation(character: Model, animationName: string, props?: {
    /** Time in seconds to fade in the animation */
    FadeTime?: number;
    /** Weight/strength of the animation (0-1) */
    Weight?: number;
    /** Playback speed multiplier */
    Speed?: number;
}) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.PlayAnimation(animationName, props);
}

export function IsPlayingAnimation(character: Model, animationName: string) {
    const container = GetAnimationContainerFromCharacter(character);
    return container.GetLoadedAnimation(animationName)?.Track.IsPlaying === true;
}