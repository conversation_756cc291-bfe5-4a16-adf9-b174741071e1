export const StaticTag = "Static";
export const HitTag = "Hit";
export const CastTag = "Cast";

export function GetChildrenWithTag(parent: Instance, tag: string) {
    return parent.GetChildren().filter(x => x.HasTag(tag));
}

export function GetDescendantsWithTag(parent: Instance, tag: string) {
    return parent.GetDescendants().filter(x => x.HasTag(tag));
}

export function AddTagWithLifetime(instance: Instance, tag: string, lifetime: number) {
    instance.AddTag(tag);
    task.delay(lifetime, () => instance?.RemoveTag(tag));
}