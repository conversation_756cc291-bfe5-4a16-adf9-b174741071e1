import {Signal} from "@rbxts/lemon-signal";

type PickerMode = "Circle" | "Square" | "Triangle";

// 1: HSV | 2: RBG
type ColorSpace = 1 | 2;

type ColorPickerParameters = {
    Parent?: Instance | string;
    Position?: UDim2;
    ZIndex?: number;
    Scale?: number;
    PickerMode: PickerMode;
    ColorSpace: ColorSpace;
    EnableTransparency?: boolean;
    RealTimeEventCalling?: boolean;
}

declare class ColorPicker {
    GetHSV: (this: ColorPicker) => [number, number, number];
    GetColor: (this: ColorPicker) => Color3;
    SetColor: (this: ColorPicker, color: Color3) => void;
    GetTransparency: (this: ColorPicker) => number | undefined;
    SetTransparency: (this: ColorPicker, transparency: number) => void;
    SetColorSpace: (this: ColorPicker, space: ColorSpace) => void;
    SetPickerMode: (this: ColorPicker, mode: PickerMode) => void;
    GetMousePos: (this: ColorPicker) => Vector2;
    WriteHistory: (this: ColorPicker) => void;
    IncrementHistory: (this: ColorPicker) => void;
    Destroy: (this: ColorPicker) => void;
    ColorChanged: Signal<[color: Color3, transparency?: number]>;
    constructor(parameters?: ColorPickerParameters);
}

export = ColorPicker;