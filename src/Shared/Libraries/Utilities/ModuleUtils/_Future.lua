--!strict
local ReplicatedStorage = game:GetService("ReplicatedStorage")

local _game = require("../FunctionUtils/_game")
local spawn = _game.spawn

export type Future<T...> = {
	ValueList: { any }?,
	AfterList: { (T...) -> () },
	YieldList: { thread },
	Cancelled: boolean,

	IsComplete: typeof(
		-- Returns a boolean indicating whether the Future is complete.
		function(self: Future<T...>): boolean
			return nil :: any
		end
	),
	IsPending: typeof(
		-- Returns a boolean indicating whether the Future is pending.
		function(self: Future<T...>): boolean
			return nil :: any
		end
	),

	Expect: typeof(
		-- Returns the values of the Future if complete, otherwise it errors with the provided message
		function(self: Future<T...>, Message: string): T...
			
		end
	),
	Unwrap: typeof(
		-- Returns the values of the Future if complete, otherwise it errors.
		function(self: Future<T...>): T...
			
		end
	),
	UnwrapOr: typeof(
		-- Returns the values of the Future if complete, otherwise it returns the provided default values.
		function(self: Future<T...>, ...: T...): T...
			
		end
	),
	UnwrapOrElse: typeof(
		-- Returns the values of the Future if complete, otherwise it calls the provided function and returns the values of the Future returned by the function.
		function(self: Future<T...>, Else: () -> T...): T...
			
		end
	),

	After: typeof(
		-- Calls a function with the values a Future completes with after the future is completed.
		function(self: Future<T...>, Callback: (T...) -> ()): ()
			
		end
	),
	Await: typeof(
		-- Returns the values of the Future if complete, otherwise it yields the current thread until the Future is completed.
		function(self: Future<T...>): T...
			
		end
	),
	Cancel: typeof(
		-- Attempts to cancel the Future. This will not work if the Future is already complete.
		function(self: Future<T...>)
			
		end
	)
}

local function IsComplete<T...>(self: Future<T...>): boolean
	return self.ValueList ~= nil
end

local function IsPending<T...>(self: Future<T...>): boolean
	return self.ValueList == nil
end

local function Expect<T...>(self: Future<T...>, Message: string): T...
	assert(self.ValueList, Message)

	return table.unpack(self.ValueList)
end

local function Unwrap<T...>(self: Future<T...>): T...
	return self:Expect("Attempt to unwrap pending future!")
end

local function UnwrapOr<T...>(self: Future<T...>, ...): T...
	if self.ValueList then
		return table.unpack(self.ValueList)
	else
		return ...
	end
end

local function UnwrapOrElse<T...>(self: Future<T...>, Else: () -> T...): T...
	if self.ValueList then
		return table.unpack(self.ValueList)
	else
		return Else()
	end
end

local function After<T...>(self: Future<T...>, Callback: (T...) -> ())
	if self.Cancelled then
		return
	end
	
	if self.ValueList then
		spawn(Callback, table.unpack(self.ValueList))
	else
		table.insert(self.AfterList, Callback)
	end
end

local function Await<T...>(self: Future<T...>): T...
	if self.Cancelled then
		error(`Cannot :Await() a cancelled Future`, 2)
	end
	
	if self.ValueList then
		return table.unpack(self.ValueList)
	else
		table.insert(self.YieldList, coroutine.running())

		return coroutine.yield()
	end
end

local function Cancel<T...>(self: Future<T...>)
	if self.ValueList then
		-- Already resolved, cannot cancel
		return
	end

	self.Cancelled = true
	
	-- Cancel any threads waiting on this Future
	for _, thread in ipairs(self.YieldList) do
		if coroutine.status(thread) == "suspended" then
			task.cancel(thread)
		end
	end
	
	self.AfterList = {} -- Clear callbacks
	self.YieldList = {} -- Clear yielding threads
end

-- The given function is called in a new thread, and the Future is completed with the return values of the function.
local function Future<T..., A...>(callback: (A...) -> T..., ...: A...): Future<T...>
	local self: Future<T...> = {
		ValueList = nil,
		AfterList = {},
		YieldList = {},

		IsComplete = IsComplete,
		IsPending = IsPending,

		Expect = Expect,
		Unwrap = Unwrap,
		UnwrapOr = UnwrapOr,
		UnwrapOrElse = UnwrapOrElse,
		Cancel = Cancel,
		
		After = After,
		Await = Await,
	} :: any

	spawn(function(self: Future<T...>, callback: (A...) -> T..., ...: A...)
		if self.Cancelled then
			return
		end
		
		local ValueList = { callback(...) }
		self.ValueList = ValueList

		for _, Thread in self.YieldList do
			task.spawn(Thread, table.unpack(ValueList))
		end

		for _, Callback in self.AfterList do
			spawn(Callback, table.unpack(ValueList))
		end
	end, self, callback, ...)

	return self
end

-- This constructor wraps the given function and arguments in a pcall.
-- The returned Future will be completed with the return values of the pcall, including the success boolean.
local function Try<T..., A...>(callback: (A...) -> T..., ...: A...): Future<(boolean, T...)>
	return Future(pcall, callback, ...)
end

-- Waits for all futures to complete and returns a future that resolves with an array of their results.
-- Returns an immediately resolved Future if the future array is empty.
local function All(futures: { Future<...any> }): Future<{ { any } }>
	if #futures == 0 then
		return Future(function()
			return {}
		end)
	end
	return Future(function()
		local results = table.create(#futures)
		local completedCount = 0

		local currentThread = coroutine.running()
		local completed = false

		for i, future in ipairs(futures) do
			future:After(function(...)
				if completed then
					return
				end

				results[i] = { ... }

				completedCount += 1
				if completedCount == #futures then
					completed = true
					if coroutine.status(currentThread) == "suspended" then
						task.spawn(currentThread, results)
					end
				end
			end)
		end
		
		if completed then
			return results
		end
		return coroutine.yield()
	end)
end

return {
	new = Future,
	try = Try,
	all = All
}