-- fewkz/typed-luau-promise 2023
-- Generated by generate-promise.ts
type PromiseStatus = "Started" | "Resolved" | "Rejected" | "Cancelled"
type PromiseLike<T...> = { expect: (self: PromiseLike<T...>) -> T..., [any]: any }
-- stylua: ignore start
type PromiseExhausted = {
	andThen: <T2...>(
		self: PromiseLike<...any>,
		successHandler: ((...any) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> PromiseExhausted,
	andThenCall: <A..., T2...>(
		self: PromiseLike<...any>,
		callback: (A...) -> T2...,
		A...
	) -> PromiseExhausted,
	andThenReturn: <T2...>(self: PromiseLike<...any>, T2...) -> PromiseExhausted,
	cancel: (self: PromiseLike<...any>) -> (),
	catch: <T2...>(
		self: PromiseLike<...any>,
		failureHandler: (...any) -> T2...
	) -> PromiseExhausted,
	expect: (self: PromiseLike<...any>) -> ...any,
	finally: <T2...>(self: PromiseLike<...any>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> PromiseExhausted,
	getStatus: (self: PromiseLike<...any>) -> PromiseStatus,
	now: (self: PromiseLike<...any>, rejectionValue: any?) -> PromiseExhausted,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise8<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> PromiseExhausted,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> PromiseExhausted,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> PromiseExhausted,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> PromiseExhausted,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> PromiseExhausted,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise8<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise7<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise8<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise8<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise8<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise8<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise8<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise7<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise6<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise7<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise7<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise7<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise7<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise7<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise6<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise5<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise6<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise6<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise6<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise6<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise6<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise5<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise4<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise5<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise5<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise5<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise5<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise5<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise4<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type Promise3<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise4<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise4<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise4<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise4<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise4<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise3<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, ...any)
}
type Promise2<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise3<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise3<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise3<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise3<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise3<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise2<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
export type Promise<T...> = {
	andThen: <T2...>(
		self: PromiseLike<T...>,
		successHandler: ((T...) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> Promise2<T2...>,
	andThenCall: <A..., T2...>(
		self: PromiseLike<T...>,
		callback: (A...) -> T2...,
		A...
	) -> Promise2<T2...>,
	andThenReturn: <T2...>(self: PromiseLike<T...>, T2...) -> Promise2<T2...>,
	cancel: (self: PromiseLike<T...>) -> (),
	catch: <T2...>(
		self: PromiseLike<T...>,
		failureHandler: (...any) -> T2...
	) -> Promise2<T2...>,
	expect: (self: PromiseLike<T...>) -> T...,
	finally: <T2...>(self: PromiseLike<T...>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> Promise2<T2...>,
	getStatus: (self: PromiseLike<T...>) -> PromiseStatus,
	now: (self: PromiseLike<T...>, rejectionValue: any?) -> Promise<T...>,
	await: (self: PromiseLike<T...>) -> (boolean, T...)
}
type _PromiseAny = {
	andThen: <T2...>(
		self: PromiseLike<...any>,
		successHandler: ((...any) -> T2...)?,
		failureHandler: ((...any) -> T2...)?
	) -> any,
	andThenCall: <A..., T2...>(
		self: PromiseLike<...any>,
		callback: (A...) -> T2...,
		A...
	) -> any,
	andThenReturn: <T2...>(self: PromiseLike<...any>, T2...) -> any,
	cancel: (self: PromiseLike<...any>) -> (),
	catch: <T2...>(
		self: PromiseLike<...any>,
		failureHandler: (...any) -> T2...
	) -> any,
	expect: (self: PromiseLike<...any>) -> ...any,
	finally: <T2...>(self: PromiseLike<...any>, (status: "Resolved" | "Rejected" | "Cancelled") -> T2...) -> any,
	getStatus: (self: PromiseLike<...any>) -> PromiseStatus,
	now: (self: PromiseLike<...any>, rejectionValue: any?) -> _PromiseAny,
	await: (self: PromiseLike<...any>) -> (boolean, ...any)
}
export type PromiseAny = _PromiseAny
type PromiseLib = {
	Status: {
		Started: "Started",
		Resolved: "Resolved",
		Rejected: "Rejected",
		Cancelled: "Cancelled",
	},

	all: <T>(promises: { PromiseLike<T> }) -> Promise<{ T }>,
	delay: (seconds: number) -> Promise<number>,
	fromEvent: <T...>(event: RBXScriptSignal<T...>, predicate: ((T...) -> boolean)?) -> Promise<T...>,
	new: <T...>((
		resolve: (T...) -> (),
		reject: (...any) -> (),
		cancel: ((callback: (() -> ())?) -> boolean) -> ()
		) -> ()) -> Promise<T...>,
	promisify: <T..., R...>(callback: (T...) -> (R...)) -> ( (T...) -> Promise<R...> ),
	resolve: <T...>(T...) -> Promise<T...>,
	try: <T..., A...>(callback: (A...) -> T..., A...) -> Promise<T...>,
}
-- stylua: ignore end

return require(script._UntypedPromise) :: PromiseLib