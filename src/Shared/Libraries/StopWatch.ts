import {Janitor} from "@rbxts/janitor";

interface ITimerCallbacks {
    OnStart: () => void;
    OnEnd: () => void;
    OnInterval?: () => void;
}

export class StopWatch {
    private readonly totalDuration: number;
    private currentDuration: number;
    private readonly janitor = new Janitor();
    private readonly onEndCallback: () => void;
    private readonly onStartCallback: () => void;
    private readonly onIntervalCallback: (() => void) | undefined;
    private readonly interval: number;
    private isActive: boolean = false;
    private isPaused: boolean = false;
    private readonly destroyOnEnd: boolean = false;

    public constructor(
        duration: number, callbacks: ITimerCallbacks, interval: number = 1, destroyOnEnd: boolean = false) {
        if (interval <= 0)
            error("The interval must be greater than 0.");
        this.onEndCallback = callbacks.OnEnd;
        this.totalDuration = duration;
        this.currentDuration = duration;
        this.onStartCallback = callbacks.OnStart;
        this.onIntervalCallback = callbacks.OnInterval;
        this.interval = interval;
        this.destroyOnEnd = destroyOnEnd;
    }

    public Start(ignoreCallback?: boolean) {
        if (this.isActive && this.currentDuration > 0)
            return this;

        this.isActive = true;
        if (ignoreCallback !== true)
            this.onStartCallback();

        const timerTask = task.defer(() => {
            while (this.isActive && this.currentDuration > 0) {
                if (this.onIntervalCallback !== undefined && !this.isPaused)
                    this.onIntervalCallback();

                task.wait(this.interval);
                if (!this.isPaused)
                    this.currentDuration -= this.interval;
            }

            this.End();
        });

        this.janitor.Add(() => task.cancel(timerTask));
        return this;
    }

    public GetTotalTime() {
        return this.totalDuration;
    }

    public GetRemainingTime() {
        return this.currentDuration;
    }

    public GetElapsedTime() {
        return this.GetTotalTime() - this.GetRemainingTime();
    }

    public IsActive(): boolean {
        return !this.isPaused && this.isActive;
    }

    public HasEnded(): boolean {
        return this.currentDuration <= 0;
    }

    public Pause() {
        if (this.isPaused)
            return;
        this.isPaused = true;
        return this;
    }

    public Resume() {
        if (!this.isPaused)
            return;

        this.isPaused = false;
        return this;
    }

    public Restart() {
        this.currentDuration = this.totalDuration;
        this.Start();
        return this;
    }

    public ResetDuration() {
        this.currentDuration = this.totalDuration;
        this.Start(false);
        return this;
    }

    public End() {
        if (!this.isActive)
            return this;

        this.currentDuration = 0;
        this.isActive = false;
        this.onEndCallback();
        if (this.destroyOnEnd)
            this.Destroy();
        return this;
    }

    public Destroy() {
        if (this.isActive)
            this.End();
        this.janitor.Destroy();
    }
}