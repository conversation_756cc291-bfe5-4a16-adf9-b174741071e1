import Maid from "@rbxts/maid";

import {Signal} from "../../InkLabs/Network";

export abstract class BaseHelperEffect {
    protected readonly Maid: Maid;
    protected readonly Ended: Signal<void>;

    protected constructor() {
        this.Maid = new Maid();
        this.Ended = new Signal();
        this.Maid.GiveTask(() => this.Ended);
        this.Ended.Connect(() => this.Destroy());
    }

    protected abstract Play(): Promise<void>;

    protected Destroy() {
        this.Maid.Destroy();
    }
}