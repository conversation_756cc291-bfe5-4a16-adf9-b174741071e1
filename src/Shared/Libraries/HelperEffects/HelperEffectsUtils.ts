import {GetDebrisFolder} from "../Directories";

type TInstancingProps = {
    Position?: Vector3;
    Size?: Vector3;
    Color?: Color3;
    Transparency?: number;
    Anchored?: boolean;
    CanCollide?: boolean;
    CanTouch?: boolean;
    CanQuery?: boolean;
    Material?: Enum.Material;
    Shape?: Enum.PartType;
}

const INSTANCING_DEFAULTS = {
    Position: new Vector3(),
    Size: new Vector3(1, 1, 1),
    Color: new Color3(),
    Transparency: 0,
    Anchored: true,
    CanCollide: false,
    CanTouch: false,
    CanQuery: false
};

export const GetNeonSphere = ({Color, Size, Transparency, Position}: TInstancingProps) => {
    const sphere = new Instance("Part");
    sphere.Anchored = INSTANCING_DEFAULTS.Anchored;
    sphere.Size = Size ?? INSTANCING_DEFAULTS.Size;
    sphere.Color = Color ?? INSTANCING_DEFAULTS.Color;
    sphere.Transparency = Transparency ?? INSTANCING_DEFAULTS.Transparency;
    sphere.Shape = Enum.PartType.Ball;
    sphere.Material = Enum.Material.Neon;
    sphere.CanCollide = INSTANCING_DEFAULTS.CanCollide;
    sphere.CanQuery = INSTANCING_DEFAULTS.CanQuery;
    sphere.CanTouch = INSTANCING_DEFAULTS.CanTouch;
    sphere.Position = Position ?? INSTANCING_DEFAULTS.Position;
    sphere.Parent = GetDebrisFolder();

    return sphere;
};

export const GetBasePart = (props: TInstancingProps) => {
    const part = new Instance("Part");
    part.Anchored = props.Anchored ?? INSTANCING_DEFAULTS.Anchored;
    part.Size = props.Size ?? INSTANCING_DEFAULTS.Size;
    part.Color = props.Color ?? INSTANCING_DEFAULTS.Color;
    part.CanCollide = props.CanCollide ?? INSTANCING_DEFAULTS.CanCollide;
    part.CanQuery = props.CanQuery ?? INSTANCING_DEFAULTS.CanQuery;
    part.CanTouch = props.CanTouch ?? INSTANCING_DEFAULTS.CanTouch;
    part.Position = props.Position ?? INSTANCING_DEFAULTS.Position;
    part.Material = props.Material ?? Enum.Material.Neon;
    part.Shape = props.Shape ?? Enum.PartType.Block;
    part.Massless = true;
    part.Parent = GetDebrisFolder();

    return part;
};

export const GetRockyPart = () => {
    return GetBasePart({
        Material: Enum.Material.Slate,
        Color: Color3.fromRGB(100, 100, 100)
    });
};

export const MakeVector3OfRadius = (radius: number) =>
    new Vector3(radius, radius, radius);