import {Workspace} from "@rbxts/services";
import spr from "@rbxts/spr";

import {GenerateCrater} from "../../InkLabs/ArklightUtils";
import PartCache from "../../InkLabs/PartCache";
import TitanUtils from "../../InkLabs/TitanUtils";
import {GetDebrisFolder} from "../Directories";
import {CastRayOnMap} from "../SpatialLib";
import {GetRockyPart} from "./HelperEffectsUtils";

function GetRandomNumber(min?: number, max?: number) {
    return new Random().NextNumber(min ?? 0.8, max ?? 1.2);
}

const CacheFolder = new Instance("Folder");
CacheFolder.Name = "CraterCache";
CacheFolder.Parent = GetDebrisFolder();

const RockCache = new PartCache(GetRockyPart(), 100, CacheFolder);

type TSimpleCraterProps = {
    Center: CFrame;
    Radius: number;
    Amount: number;

    BaseLength?: number;
    BaseHeight?: number;
    FadeInTime?: number;
    FadeOutTime?: number;
    Duration?: number;
    RemoveRandomness?: boolean;
    RaycastDistance?: number;
}

type TAnimatedCraterProps = {
    Center: Vector3 | CFrame;

    Lifetime?: number;
    Amount?: number;
    FadeInTime?: number;
    FadeOutTime?: number;
    Angle?: number;
    RayLength?: number;

    Radius?: NumberRange;
    BlockSize?: NumberRange;

    FadeInStyle?: Enum.EasingStyle;
    FadeOutStyle?: Enum.EasingStyle;

    FadeInDirection?: Enum.EasingDirection;
    FadeOutDirection?: Enum.EasingDirection;
}

type TSideRocksCraterProps = {
    Origin: CFrame,
    Lifetime?: number,
    RayLenght?: number,
    Size?: Vector3,
    Distance?: number
}

export class SimpleCrater {
    constructor(props: TSimpleCraterProps) {
        if (Workspace.CurrentCamera!.CFrame.Position.sub(props.Center.Position).Magnitude > 100)
            return;
        TitanUtils.Effects.Crater(props);
    }
}

export class AnimatedCrater {
    constructor(props: TAnimatedCraterProps) {
        GenerateCrater(props);
    }
}

export class SideRocksCrater {
    private readonly DefaultProps: TSideRocksCraterProps = {
        Origin: new CFrame(),
        Lifetime: 4,
        RayLenght: 15,
        Size: Vector3.one,
        Distance: 2
    };

    constructor({Origin, Lifetime, RayLenght, Size, Distance}: TSideRocksCraterProps) {
        if (Workspace.CurrentCamera!.CFrame.Position.sub(Origin.Position).Magnitude > 100)
            return;

        const distance = Distance ?? this.DefaultProps.Distance as number;
        const size = Size ?? this.DefaultProps.Size as Vector3;
        const rayLength = RayLenght ?? this.DefaultProps.RayLenght as number;
        const lifetime = Lifetime ?? this.DefaultProps.Lifetime as number;

        const rock1Origin = Origin.mul(new CFrame(distance * new Random().NextNumber(0.35, 0.65), 0, 0));
        const rock2Origin = Origin.mul(new CFrame(distance * new Random().NextNumber(-0.65, -0.35), 0, 0));

        const rock1Cast = CastRayOnMap(
            rock1Origin.Position,
            rock1Origin.UpVector.mul(-1),
            rayLength
        );

        const rock2Cast = CastRayOnMap(
            rock2Origin.Position,
            rock2Origin.UpVector.mul(-1),
            rayLength
        );

        const rock1Position = rock1Cast?.Position;
        const rock2Position = rock2Cast?.Position;

        const rock1CFrame = rock1Position && new CFrame(rock1Position)
            .mul(new CFrame(distance * 0.5, 0, 0))
            .mul(Origin.Rotation)
            .mul(this.GetRandomRockRotation());

        const rock2CFrame = rock2Position && new CFrame(rock2Position)
            .mul(new CFrame(distance * -0.5, 0, 0))
            .mul(Origin.Rotation)
            .mul(this.GetRandomRockRotation());

        rock1CFrame && this.SpawnRock(rock1CFrame, lifetime, size, rock1Cast);
        rock2CFrame && this.SpawnRock(rock2CFrame, lifetime, size, rock2Cast);
    }

    private GetRandomRotationDegree(min: number, max: number) {
        return math.rad(new Random().NextNumber(min, max));
    }

    private GetRandomRockRotation() {
        return CFrame.Angles(
            this.GetRandomRotationDegree(-15, 15),
            this.GetRandomRotationDegree(-7, 7),
            this.GetRandomRotationDegree(-10, 10)
        );
    }

    private SpawnRock(cframe: CFrame, lifetime: number, size: Vector3, cast: RaycastResult) {
        const material = cast.Material;
        const color = cast.Instance.Color;

        const part = RockCache.GetPart();
        part.Size = Vector3.zero;
        part.Color = color;
        part.Material = material;
        part.CFrame = cframe;

        spr.target(part, 0.8, 3, {
            Size: size.mul(new Random().NextNumber(0.6, 1.4))
        });

        task.delay(lifetime, () => {
            spr.target(part, 1.4, 4, {
                Size: Vector3.zero
            });

            spr.completed(part, () =>
                RockCache.ReturnPart(part));
        });
    }
}