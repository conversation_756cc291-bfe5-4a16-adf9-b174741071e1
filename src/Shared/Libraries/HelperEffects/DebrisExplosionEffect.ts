import {Debris} from "@rbxts/services";
import {KnockbackToDirection} from "../../Components/Knockback";
import GetRandomPointOnPartialSphereSurface from "../../Functions/GetRandomPointOnPartialSphereSurface";
import {GenericEffectsFolder, GetDebrisFolder} from "../Directories";
import {EmitChildren} from "../FXUtilities";

export default function(center: Vector3, scale: number = 1, amount: NumberRange, mainColor?: Color3) {
    const debrisAmount = math.random(amount.Min, amount.Max);
    for (let i = 0; i < debrisAmount; i++) {
        const origin = GetRandomPointOnPartialSphereSurface(center, 5 * scale, new Vector2(2.5 * scale, 4.75 * scale));
        const direction = origin.sub(center).Unit;
        const force = math.random(0.1, 3) * scale;

        const part = GenericEffectsFolder.FindFirstChild("DebrisPart")?.Clone() as BasePart & {
            Attachment: Attachment & {
                ColorTrail: Trail;
                SmokeTrail: Trail;
                Attachment0: Attachment;
                Attachment1: Attachment;
                Color: ParticleEmitter;
                Smoke: ParticleEmitter;
            };
        };

        if (part === undefined)
            return;

        part.Size = part.Size.mul(new Random().NextNumber(0.7 * (scale < 1 ? 1 + scale : scale), 1.3 * scale));
        part.Attachment.Attachment0.Position = new Vector3(0, part.Size.Y, 0);
        part.Attachment.Attachment1.Position = new Vector3(0, -part.Size.Y, 0);

        part.Attachment.SmokeTrail.Enabled = true;
        part.Attachment.SmokeTrail.TextureLength = new Random().NextNumber(7, 10) * scale;
        part.CFrame = CFrame.lookAlong(origin, direction);

        if (mainColor === undefined) {
            part.Attachment.ColorTrail.Enabled = false;
            part.Attachment.Color.Destroy();
        } else {
            part.Attachment.ColorTrail.Color = new ColorSequence(mainColor);
            part.Attachment.ColorTrail.TextureLength = part.Attachment.SmokeTrail.TextureLength;
            part.Attachment.Color.Color = part.Attachment.ColorTrail.Color;
        }

        const touchedConnection = part.Touched.Once(() => {
            part.Anchored = true;
            part.Transparency = 1;
            part.Attachment.SmokeTrail.Enabled = false;
            part.Attachment.ColorTrail.Enabled = false;
            EmitChildren(part.Attachment);
            Debris.AddItem(part, part.Attachment.SmokeTrail.Lifetime + 1);
        });

        part.Destroying.Once(() => touchedConnection.Disconnect());
        part.Parent = GetDebrisFolder();
        part.Anchored = false;
        KnockbackToDirection(part, direction, force, 0.2);
    }
}