import {Debris, TweenService} from "@rbxts/services";

import {GetDebrisFolder, MiscEffectsStorage} from "../Directories";

type TRingSphereProps = {
    Position: Vector3,
    Radius?: NumberRange,
    Speed?: number,
    Amount?: number,
    Delay?: number,
    Color?: Color3,
    RingHeight?: number,
}

export class RingSphereEffect {
    constructor(props: TRingSphereProps) {
        const radius = props.Radius ?? new NumberRange(0.1, 10);
        const position = props.Position.sub(new Vector3(0, radius.Max * 0.5, 0));
        const speed = props.Speed ?? 0.45;
        const amount = props.Amount ?? 1;
        const delay = props.Delay ?? 0.1;
        const color = props.Color ?? Color3.fromRGB(255, 255, 255);
        const ringHeight = props.RingHeight ?? 1;

        function doEffect(shouldWaitDelay?: boolean) {
            let ring = MiscEffectsStorage.FindFirstChild("NeonRingMesh") as MeshPart;
            if (!ring) return;

            ring = ring.Clone();
            ring.Size = new Vector3(radius.Min, 0, radius.Min);
            ring.Position = position;
            ring.Color = color;
            ring.Parent = GetDebrisFolder();

            let positionTween = TweenService.Create(
                ring,
                new TweenInfo(speed, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut),
                {
                    Position: position.add(new Vector3(0, radius.Max, 0))
                }
            );

            let sizeTween = TweenService.Create(
                ring,
                new TweenInfo(speed / 2, Enum.EasingStyle.Quad, Enum.EasingDirection.Out, 0, true),
                {
                    Size: new Vector3(radius.Max, ringHeight, radius.Max)
                }
            );

            positionTween.Play();
            sizeTween.Play();
            Debris.AddItem(ring, speed + 0.1);
            if (shouldWaitDelay)
                task.wait(delay);
        }

        task.spawn(() => {
            if (amount === 1) {
                doEffect();
                return;
            }

            for (let i = 1; i <= amount; i++) {
                doEffect(i !== 0);
            }
        });
    }
}