import {TweenService} from "@rbxts/services";

import {KnockbackFromOrigin} from "../../Components/Knockback";
import {AddPartToCollisionGroup} from "../../Components/PhysicsManager";
import PartCache from "../../InkLabs/PartCache";
import {GetDebrisFolder} from "../Directories";
import {CastRayOnMap} from "../SpatialLib";
import {GetRockyPart} from "./HelperEffectsUtils";

type BlockExplosionEffectParams = {
    position: Vector3;
    size?: {
        X?: NumberRange;
        Y?: NumberRange;
        Z?: NumberRange;
    };
    amount?: NumberRange;
    rayDistance?: number;
    duration?: number;
    power?: number;
    centerVariation?: number;
};

const DEFAULT_MIN_SIZE = 1;
const DEFAULT_MAX_SIZE = 3;
const DEFAULT_MIN_AMOUNT = 3;
const DEFAULT_MAX_AMOUNT = 6;
const DEFAULT_RAY_DISTANCE = 15;
const DEFAULT_DURATION = 4;
const DEFAULT_POWER = 1;
const DEFAULT_CENTER_VARIATION = 0.5;

const DEFAULT_ROCK = GetRockyPart();
DEFAULT_ROCK.CanCollide = true;
DEFAULT_ROCK.Name = "ExplosionRockPart";
AddPartToCollisionGroup(DEFAULT_ROCK, "Debris");

const ROCK_CACHE = new PartCache(DEFAULT_ROCK, 1000, GetDebrisFolder());

export class BlockExplosionEffect {
    constructor(props: BlockExplosionEffectParams) {
        const position = props.position;

        const amount = math.random(
            props.amount?.Min ?? DEFAULT_MIN_AMOUNT,
            props.amount?.Max ?? DEFAULT_MAX_AMOUNT
        );
        const rayDistance = props.rayDistance ?? DEFAULT_RAY_DISTANCE;
        const minX = props.size?.X?.Min ?? DEFAULT_MIN_SIZE;
        const maxX = props.size?.X?.Max ?? DEFAULT_MAX_SIZE;
        const minY = props.size?.Y?.Min ?? DEFAULT_MIN_SIZE;
        const maxY = props.size?.Y?.Max ?? DEFAULT_MAX_SIZE;
        const minZ = props.size?.Z?.Min ?? DEFAULT_MIN_SIZE;
        const maxZ = props.size?.Z?.Max ?? DEFAULT_MAX_SIZE;
        const centerVariation = props.centerVariation ?? DEFAULT_CENTER_VARIATION;
        const duration = props.duration ?? DEFAULT_DURATION;
        const power = props.power ?? DEFAULT_POWER;

        for (let i = 0; i < amount; i++) {
            let rayOrigin = position.add(new Vector3(
                new Random().NextNumber(-centerVariation, centerVariation),
                1,
                new Random().NextNumber(-centerVariation, centerVariation)
            ));

            let rayDirection = new CFrame(rayOrigin).UpVector.mul(-1);
            let raycastResult = CastRayOnMap(rayOrigin, rayDirection, rayDistance);
            if (!raycastResult) {
                continue;
            }

            let hitPart = raycastResult.Instance;
            let hitMaterial = hitPart.Material;
            let hitColor = hitPart.Color;
            let hitPosition = raycastResult.Position;

            let blockSize = new Vector3(
                new Random().NextNumber(minX, maxX),
                new Random().NextNumber(minY, maxY),
                new Random().NextNumber(minZ, maxZ)
            );

            let block = ROCK_CACHE.GetPart();
            block.Size = Vector3.zero;
            block.Position = hitPosition.add(new Vector3(0, (blockSize.Y / 2) + 3, 0));
            block.Material = hitMaterial;
            block.Color = hitColor;
            block.Transparency = hitPart.Transparency;
            block.Anchored = false;

            TweenService.Create(block, new TweenInfo(0.35, Enum.EasingStyle.Exponential, Enum.EasingDirection.Out), {
                Size: blockSize
            }).Play();

            KnockbackFromOrigin(block, position, power * new Random().NextNumber(0.7, 1.3));

            task.delay(duration, () => {
                if (!block) return;
                const tween = TweenService.Create(
                    block,
                    new TweenInfo(0.8, Enum.EasingStyle.Exponential, Enum.EasingDirection.In),
                    {
                        Size: Vector3.zero
                    }
                );
                tween.Play();

                task.wait(0.8);
                ROCK_CACHE.ReturnPart(block);
            });
        }
    }
}