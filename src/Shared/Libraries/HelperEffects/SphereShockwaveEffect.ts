import ISuperTween from "../../InkLabs/SuperTween";
import {BaseHelperEffect} from "./BaseHelperEffect";
import {GetNeonSphere, MakeVector3OfRadius} from "./HelperEffectsUtils";

type TSphereShockwaveEffectProps = {
    Position: Vector3;
    Radius: number;
    Speed: number;
    Color: Color3;

    InitialTransparency?: number;
    InitialRadius?: number;
    InitialColor?: Color3;
};

export class SphereShockwaveEffect extends BaseHelperEffect {
    private readonly props: TSphereShockwaveEffectProps;

    constructor(props: TSphereShockwaveEffectProps) {
        super();
        this.props = props;
    }

    public async Play() {
        const sphere = GetNeonSphere({
            Color: this.props.InitialColor ?? this.props.Color,
            Size: MakeVector3OfRadius(this.props.InitialRadius ?? 0),
            Position: this.props.Position,
            Transparency: this.props.InitialTransparency ?? 0
        });

        const tweenIn = new ISuperTween(
            sphere,
            new TweenInfo(this.props.Speed, Enum.EasingStyle.Exponential, Enum.EasingDirection.Out),
            {
                Size: MakeVector3OfRadius(this.props.Radius),
                Transparency: 0,
                Color: this.props.Color
            }
        );

        const tweenOut = new ISuperTween(
            sphere,
            new TweenInfo(this.props.Speed * 0.35, Enum.EasingStyle.Quad, Enum.EasingDirection.Out),
            {
                Transparency: 1,
                Color: this.props.InitialColor ?? this.props.Color
            }
        );

        tweenIn.Play();
        task.wait(this.props.Speed * 0.2);
        tweenOut.Play();
        task.delay(this.props.Speed * 0.35, () => {
            sphere.Destroy();
            this.Ended.Fire();
        });
    }
}