import { IId } from "../GlobalTypes";
import { TRarityType } from "../Rarity";
import { TItemType } from "./Common/ItemTypes";

export interface IInventoryItem extends IId {
  Id: string;
  Name: string;
  ItemType: TItemType;
  Locked: boolean;
  Tradeable: boolean;
  Rarity: TRarityType;
  /** Whether the item is hidden from the player, which means it is not visible in the inventory. */
  Hidden: boolean;
  /** Whether the item is unique and can only have one per player. */
  Unique: boolean;
  Data: {
    Id: string;
    Modifiers: string[];
    ObtainedAt: number;
    ObtainedBy: string;
    ObtainedFrom: string;
  };
}
