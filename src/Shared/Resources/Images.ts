import {Players} from "@rbxts/services";

export default {
    WizzyThumbsUp: "rbxassetid://17091156418",
    GetPlayerImage: (player: Player | number) => {
        const pcallResult = pcall(() => {
            const userId = typeIs(player, "number") ? player : player.UserId;
            return Players.GetUserThumbnailAsync(userId, Enum.ThumbnailType.HeadShot, Enum.ThumbnailSize.Size420x420);
        });

        return pcallResult[0] ? pcallResult[1] : "rbxassetid://0";
    }
};