export const EntityTag = "Entity";
export const StunTag = "Stunned";
export const AttackingTag = "Attacking";
export const IFramesTag = "IFrames";
export const RagdollTag = "Ragdoll";
export const CharacterTag = "Character";
export const NPCTag = "NPC";
export const HoldingSkillTag = "HoldingSkill";
export const BlockingTag = "Blocking";
export const PerfectBlockingTag = "PerfectBlocking";
export const InCombatTag = "InCombat";
export const SafeZoneTag = "SafeZone";
export const SwimmingTag = "Swimming";
export const WaterTag = "Water";
export const DevilFruitUserTag = "DevilFruitUser";
export const InvisibleCharacterTag = "InvisibleCharacter";
export const NPCJobInteractionWaypointTag = "NPCJobInteractionWaypoint";
export const NPCJobWaypointTag = "NPCJobWaypoint";
export const WanderingWaypointTag = "WanderingWaypoint";
export const RotatingModelViewportFrameTag = "RotatingModelViewportFrame";
export const CustomizationSpotTag = "CustomizationSpot";
export const CustomizationEntranceTag = "CustomizationEntrance";
export const AttackTrailTag = "AttackTrail";
export const UnableToAttackTag = "UnableToAttack";

const CooldownTagPrefix = "CD_";

export const AddSkillCooldownTagToCharacter = (character: Instance, skillId: string, duration: number) => {
    character.AddTag(CooldownTagPrefix + skillId);
    task.delay(duration, () => {
        character?.RemoveTag(CooldownTagPrefix + skillId);
    });
};

export const IsSkillOnCooldown = (character: Instance, skillId: string) => {
    return character.HasTag(CooldownTagPrefix + skillId);
};

export const AddHoldingSkillTagToCharacter = (character: Instance) => {
    character.AddTag(HoldingSkillTag);
};

export const RemoveHoldingSkillTagFromCharacter = (character: Instance) => {
    character.RemoveTag(HoldingSkillTag);
};

export const IsHoldingSkill = (character: Instance) => {
    return character.HasTag(HoldingSkillTag);
};