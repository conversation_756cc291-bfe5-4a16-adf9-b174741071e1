import {Players} from "@rbxts/services";

import {CharacterModel} from "../Components/CharacterModel";
import {GetMapFolder} from "../Libraries/Directories";

type TAttacker = {
    Id: string;
    Damage: number;
}

const DEFAULT_APPEARANCE = new Instance("HumanoidDescription");

export const GetCharacterAttackers = (character: CharacterModel) => {
    let attackers: TAttacker[] = [];
    let attackersFolder = character.FindFirstChild("ATTACKERS") as Folder;
    if (!attackersFolder) return attackers;

    attackersFolder.GetChildren().forEach((attacker) => {
        let attackerValue = attacker as NumberValue;
        attackers.push({
            Id: attackerValue.Name,
            Damage: attackerValue.Value
        });
    });
};

export const AddCharacterAttacker = (character: CharacterModel, attackerId: string, damage: number) => {
    let attackersFolder = character.FindFirstChild("ATTACKERS") as Folder;
    if (!attackersFolder) {
        attackersFolder = new Instance("Folder");
        attackersFolder.Name = "ATTACKERS";
        attackersFolder.Parent = character;
    }

    let attackerValue = attackersFolder.FindFirstChild(attackerId) as NumberValue || undefined;
    if (attackerValue) {
        attackerValue.Value += damage;
        return;
    }

    attackerValue = new Instance("NumberValue");
    attackerValue.Name = attackerId;
    attackerValue.Value = damage;
    attackerValue.Parent = attackersFolder;
};

export function GetHumanoidDescriptionFromUserId(userId: number) {
    let [
        success,
        result
    ] = pcall(() => {
        return Players.GetHumanoidDescriptionFromUserId(userId);
    });

    return success ? result as HumanoidDescription : DEFAULT_APPEARANCE;
}

export function GetValidRandomUserId(): LuaTuple<[number, string]> {
    let userId = math.random(1000, 1000000000);

    let [
        success,
        result
    ] = pcall(() => {
        return Players.GetNameFromUserIdAsync(userId);
    });

    if (success)
        return $tuple(userId, result as string);

    return GetValidRandomUserId();
}

export function GetCharacterFromHumanoidDescription(humanoidDescription: HumanoidDescription) {
    let [
        success,
        result
    ] = pcall(() => {
        return Players.CreateHumanoidModelFromDescription(humanoidDescription, Enum.HumanoidRigType.R6);
    });

    return success ? result as CharacterModel : undefined;
}

export const GetRandomPlayerInfo = () => {
    let [
        id,
        name
    ] = GetValidRandomUserId();
    let humanoidAppeareance = GetHumanoidDescriptionFromUserId(id);
    let charModel = GetCharacterFromHumanoidDescription(humanoidAppeareance);

    return {
        Id: id,
        Name: name,
        CharModel: charModel
    };
};

export function GetRandomNPCSpawnpoint() {
    let spawnpoints = GetMapFolder().WaitForChild("Spawn").WaitForChild("NPC").GetChildren();
    let spawnPart = spawnpoints[math.random(0, spawnpoints.size() - 1)] as BasePart;
    return spawnPart.Position;
}