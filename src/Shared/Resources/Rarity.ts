/**
 * Available rarity types for equipment, ordered from most common to rarest.
 */
export const Rarities = [
  "Common",
  "Uncommon",
  "Rare",
  "Epic",
  "Legendary",
  "Mythical",
  "Secret",
] as const;

/**
 * Type representing all possible equipment rarity levels.
 */
export type TRarityType = (typeof Rarities)[number];

/**
 * Base weights for each rarity (higher = more common)
 * These get normalized to percentages automatically
 */
export const BaseWeights: Record<TRarityType, number> = {
  Common: 500,
  Uncommon: 300,
  Rare: 100,
  Epic: 50,
  Legendary: 10,
  Mythical: 5,
  Secret: 1,
};

export const LuckInfluencePerRarity: Record<TRarityType, number> = {
  Common: 0,
  Uncommon: 0.05,
  Rare: 0.15,
  Epic: 0.3,
  Legendary: 0.4,
  Mythical: 0.6,
  Secret: 0.8,
};

export const RarityGradient: Record<TRarityType, ColorSequence> = {
  Common: new ColorSequence(Color3.fromRGB(255, 255, 255)),
  Uncommon: new ColorSequence([
    new ColorSequenceKeypoint(0, new Color3(1, 1, 1)),
    new ColorSequenceKeypoint(1, Color3.fromRGB(170, 255, 127)),
  ]),
  Rare: new ColorSequence([
    new ColorSequenceKeypoint(0, new Color3(1, 1, 1)),
    new ColorSequenceKeypoint(1, Color3.fromRGB(142, 246, 255)),
  ]),
  Epic: new ColorSequence([
    new ColorSequenceKeypoint(0, Color3.fromRGB(192, 184, 255)),
    new ColorSequenceKeypoint(1, Color3.fromRGB(149, 57, 255)),
  ]),
  Legendary: new ColorSequence([
    new ColorSequenceKeypoint(0, Color3.fromRGB(140, 255, 176)),
    new ColorSequenceKeypoint(0.250432, Color3.fromRGB(254, 255, 194)),
    new ColorSequenceKeypoint(0.5, Color3.fromRGB(152, 235, 223)),
    new ColorSequenceKeypoint(0.75, Color3.fromRGB(218, 193, 255)),
    new ColorSequenceKeypoint(1, Color3.fromRGB(255, 134, 134)),
  ]),
  Mythical: new ColorSequence([
    new ColorSequenceKeypoint(0, new Color3(1, 1, 1)),
    new ColorSequenceKeypoint(0.5, Color3.fromRGB(157, 224, 234)),
    new ColorSequenceKeypoint(0.75, Color3.fromRGB(145, 140, 221)),
    new ColorSequenceKeypoint(1, Color3.fromRGB(255, 170, 255)),
  ]),
  Secret: new ColorSequence([
    new ColorSequenceKeypoint(0, new Color3(1, 1, 1)),
    new ColorSequenceKeypoint(0.33, new Color3()),
    new ColorSequenceKeypoint(0.66, new Color3(1, 1, 1)),
    new ColorSequenceKeypoint(1, new Color3()),
  ]),
};

export const RarityColors: Record<TRarityType, Color3> = {
  Common: Color3.fromRGB(255, 255, 255),
  Uncommon: Color3.fromRGB(170, 255, 127),
  Rare: Color3.fromRGB(142, 246, 255),
  Epic: Color3.fromRGB(149, 57, 255),
  Legendary: Color3.fromRGB(252, 255, 99),
  Mythical: Color3.fromRGB(255, 170, 255),
  Secret: Color3.fromRGB(0, 0, 0),
};

/**
 * Order of the rarity types, from most common to rarest.
 * Used for sorting the items in the inventory screen.
 * Lower is better.
 */
export const RarityOrder: Record<TRarityType, number> = {
  Common: 7,
  Uncommon: 6,
  Rare: 5,
  Epic: 4,
  Legendary: 3,
  Mythical: 2,
  Secret: 1,
};

/**
 * Calculate effective weights with luck modifiers applied
 */
function getEffectiveWeights(
  luckMultiplier: number = 0
): Record<TRarityType, number> {
  const luck = math.max(luckMultiplier, 0);
  const weights: Record<TRarityType, number> = { ...BaseWeights };

  if (luck > 0) {
    // Apply luck multipliers to each rarity
    for (const rarity of Rarities) {
      const influence = LuckInfluencePerRarity[rarity];
      const multiplier = 1 + luck * influence;
      weights[rarity] *= multiplier;
    }
  }

  return weights;
}

/**
 * Get normalized percentages for each rarity (always sums to 1.0)
 */
export function GetRarityChances(
  luckMultiplier: number = 0
): Record<TRarityType, number> {
  const weights = getEffectiveWeights(luckMultiplier);
  const totalWeight = Rarities.reduce(
    (sum, rarity) => sum + weights[rarity],
    0
  );

  const chances: Record<TRarityType, number> = {} as Record<
    TRarityType,
    number
  >;
  for (const rarity of Rarities) {
    chances[rarity] = weights[rarity] / totalWeight;
  }

  return chances;
}

/**
 * Roll for a random rarity based on current weights
 */
export function RollRarity(luckMultiplier: number = 0): TRarityType {
  const weights = getEffectiveWeights(luckMultiplier);
  const totalWeight = Rarities.reduce(
    (sum, rarity) => sum + weights[rarity],
    0
  );
  const random = math.random() * totalWeight;

  let cumulative = 0;
  for (const rarity of Rarities) {
    cumulative += weights[rarity];
    if (random <= cumulative) {
      return rarity;
    }
  }

  // Fallback (should never reach here)
  return "Common";
}

export function GetChancePerItem(
  items: Map<string, TRarityType>,
  luckMultiplier: number = 0
): Map<string, number> {
  // Get current rarity chances with luck applied
  const rarityChances = GetRarityChances(luckMultiplier);

  // Count items per rarity
  const itemsPerRarity = new Map<TRarityType, number>();
  items.forEach((rarity) => {
    itemsPerRarity.set(rarity, (itemsPerRarity.get(rarity) ?? 0) + 1);
  });

  // Calculate chance per individual item
  const chancePerItem = new Map<string, number>();
  items.forEach((rarity, itemId) => {
    const rarityChance = rarityChances[rarity];
    const itemsInRarity = itemsPerRarity.get(rarity) ?? 1;
    chancePerItem.set(itemId, rarityChance / itemsInRarity);
  });

  return chancePerItem;
}

export function GetRandomItem<T extends defined>(
  items: Map<T, TRarityType>,
  luckMultiplier: number = 0,
  dropChance?: TRarityType
) {
  // Handle drop chance first
  if (dropChance !== undefined) {
    const shouldGetItem = GetRandomItem(
      new Map<boolean, TRarityType>([
        [true, dropChance],
        [false, "Common"],
      ]),
      luckMultiplier
    );
    if (!shouldGetItem) return;
  }

  // Roll for rarity using the existing, working logic
  const targetRarity = RollRarity(luckMultiplier);

  // Get all items of the rolled rarity
  const itemsOfRarity: T[] = [];
  items.forEach((itemRarity, item) => {
    if (itemRarity === targetRarity) {
      itemsOfRarity.push(item);
    }
  });

  // If we have items of the target rarity, pick one randomly
  if (itemsOfRarity.size() > 0) {
    const randomIndex = math.floor(math.random() * itemsOfRarity.size());
    return itemsOfRarity[randomIndex];
  }

  // Fallback: find the most common item available
  let mostCommonItem: T | undefined;
  let highestOrder = 0; // Higher order = more common

  items.forEach((itemRarity, itemId) => {
    const order = RarityOrder[itemRarity];
    if (order > highestOrder) {
      highestOrder = order;
      mostCommonItem = itemId;
    }
  });

  return mostCommonItem;
}
