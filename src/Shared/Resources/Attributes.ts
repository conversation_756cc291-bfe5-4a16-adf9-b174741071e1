import { HttpService, Players } from "@rbxts/services";

export const NPCHostilityAttributeName = "Hostile";
export const NPCTypeAttributeName = "NPCType";
export const MouseCFrameAttributeName = "Mouse";
export const FactionsAttributeName = "Factions";
export const IdAttributeName = "Id";
export const OwnerIdAttributeName = "Owner";
export const IgnorePlayersAttributeName = "IgnorePlayers";
export const BlockCapacityAttributeName = "BlockCapacity";
export const MaxBlockCapacityAttributeName = "MaxBlockCapacity";
export const PartyIdAttributeName = "PartyId";
export const PartyLeaderIdAttributeName = "PartyLeaderId";
export const EquippedToolAttributeName = "EquippedTool";
export const StaminaAttributeName = "Stamina";
export const MaxStaminaAttributeName = "MaxStamina";
export const CriticalChanceAttributeName = "CriticalChance";
export const CriticalMultiplierAttributeName = "CriticalMultiplier";
export const OxygenAttributeName = "Oxygen";
export const TitleAttributeName = "Title";
export const CustomizationEntranceLocationAttributeName =
  "CustomizationEntranceLocation";
export const LuckAttributeName = "Luck";

export const GetCharacterId = (character: Model) =>
  Players.GetPlayerFromCharacter(character) !== undefined
    ? GetPlayerId(Players.GetPlayerFromCharacter(character)!)
    : (character?.GetAttribute(IdAttributeName) as string) ??
      HttpService.GenerateGUID(false);

export const GetPlayerId = (player: Player) =>
  player.Character !== undefined &&
  player.Character.GetAttribute(IdAttributeName)
    ? (player.Character.GetAttribute(IdAttributeName) as string)
    : tostring(player.UserId);

export const GetEquippedToolName = (character: Instance) =>
  character.GetAttribute(EquippedToolAttributeName) as string | undefined;

export function GetCharacterTitle(character: Model) {
  return character.GetAttribute(TitleAttributeName) as string | undefined;
}

export function SetCharacterTitle(character: Model, title: string) {
  character.SetAttribute(TitleAttributeName, title);
}

export function GetPlayerLuck(player: Player) {
  return (
    ((player.Character?.GetAttribute("Luck") as number | undefined) ?? 0) +
    ((player.GetAttribute("Luck") as number | undefined) ?? 0)
  );
}
