import ByteNet from "../InkLabs/ByteNet";

export const Packets = {
    Replicators: ByteNet.defineNamespace("Replicators", () => {
        return {
            RockEffect: ByteNet.definePacket({
                value: ByteNet.vec3,
                reliabilityType: "unreliable"
            }),
            MousePosition: ByteNet.definePacket({
                value: ByteNet.vec3,
                reliabilityType: "unreliable"
            })
        };
    })
};