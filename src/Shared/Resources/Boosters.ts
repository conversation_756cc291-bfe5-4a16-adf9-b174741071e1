export const Boosters = [
    "DoubleExp",
    "DevilFruitNotifier",
    "DoubleDropRate"
] as const;

export type BoosterId = typeof Boosters[number];

const BoosterVisuals = {
    DoubleExp: {
        Color: new ColorSequence([
            new ColorSequenceKeypoint(0, Color3.fromRGB(170, 85, 255)),
            new ColorSequenceKeypoint(1, Color3.fromRGB(255, 255, 255))
        ]),
        Prefix: "2x XP"
    },
    DevilFruitNotifier: {
        Color: new ColorSequence([
            new ColorSequenceKeypoint(0, Color3.fromRGB(170, 85, 255)),
            new ColorSequenceKeypoint(0.714, Color3.fromRGB(170, 255, 255)),
            new ColorSequenceKeypoint(1, Color3.fromRGB(255, 255, 255))
        ]),
        Prefix: "<b>*[2X RATES]</b> DF Notifier"
    },
    DoubleDropRate: {
        Color: new ColorSequence([
            new ColorSequenceKeypoint(0, Color3.fromRGB(0, 255, 127)),
            new ColorSequenceKeypoint(1, Color3.fromRGB(255, 255, 255))
        ]),
        Prefix: "2x Drop Rate"
    }
};

export const GetBoosterVisuals = (boosterId: BoosterId) => BoosterVisuals[boosterId]!;