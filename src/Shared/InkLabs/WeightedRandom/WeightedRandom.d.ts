declare class WeightedRandom<T> {
    GetItems(): Map<T, { Weight: number, LuckInfluence: number }>;

    AddItem(item: T, weight: number, luckInfluence: number): void;

    RemoveItem(item: T): void;

    GetTotalWeight(): number;

    GetWeight(item: T, luckFactor?: number): number | undefined;

    GetWeights(luckFactor?: number): Map<T, number>;

    GetProbabilities(luckFactor?: number): Map<T, number>;

    GetProbability(item: T, luckFactor?: number): number | undefined;

    Next(luckFactor?: number): T;
}

export = WeightedRandom;