export type ThemePalette = {
    Id: string,
    Background: Map<number, Color3>,
    ForegroundOnBackground: Map<number, Color3>,
    AccentOnBackground: Map<number, Color3>,
    GreyOnBackground: Map<number, Color3>,
    ForegroundOnAccentBackground: Map<number, Color3>,
    AccentOnAccentBackground: Map<number, Color3>,
    ForegroundOnGreyBackground: Map<number, Color3>,

    ShouldInvert: Map<number, boolean>
}

export type ThemeContext = {
    Id: string,
    Palette: ThemePalette,
    ZDepth: number,

    Background: Color3,
    ForegroundOnBackground: Color3,
    AccentOnBackground: Color3,
    GreyOnBackground: Color3,
    ForegroundOnAccentBackground: Color3,
    AccentOnAccentBackground: Color3,
    ForegroundOnGreyBackground: Color3,

    ShouldInvert: boolean
}

interface IContext {
    New(id: string, palette: ThemePalette, zDepth: number): ThemeContext,

    FromRoot(palette: ThemePalette): ThemeContext,

    WithZOffset(context: ThemeContext, zOffset: number): ThemeContext,

    GetContext(id: string): ThemeContext | undefined
}

interface IPalette {
    New(id: string, baseLightness: number, accentHue: number): ThemePalette,

    GetStudioPalette(accentHue?: number): ThemePalette,

    GetPalette(id: string): ThemePalette | undefined
}

export const Context: IContext;
export const Palette: IPalette;

// return Theme :: {
// 	Palette: {
// 		New: (id: string, baseLightness: number, accentHue: number) -> ThemePalette,
// 		GetStudioPalette: (accentHue: number?) -> tempState<ThemePalette>,
// 		GetPalette: (id: string) -> ThemePalette?,
// 	},

// 	Context: {
// 		New: (id: string, palette: ThemePalette, zDepth: number) -> ThemeContext,
// 		FromRoot: (palette: ThemePalette) -> ThemeContext,
// 		WithZOffset: (context: ThemeContext, zOffset: number) -> ThemeContext,
//         GetContext: (id: string) -> ThemeContext?
// 	}
// }