declare class ParallelCast {
    Signal: RBXScriptSignal<(result: RaycastResult | undefined) => void>;

    constructor(id: string, directory: Instance, actorsAmount?: number);

    SetParams(params: RaycastParams): ParallelCast;

    SetOrigin(origin: Vector3): ParallelCast;

    SetDirection(direction: Vector3): ParallelCast;

    Run(): ParallelCast;

    Stop(): ParallelCast;

    Destroy(): void;
}

export = ParallelCast;