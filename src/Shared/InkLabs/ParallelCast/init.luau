--@EternityDev
--!strict
--!native
--!optimize 2
local Raycast = {}
Raycast.__index = Raycast

local ReplicatedStorage = game:GetService("ReplicatedStorage")
local RunService = game:GetService("RunService")
local Signal = require(script.Signal)

local PreSimulation = RunService.PreSimulation
local sampleActor = ReplicatedStorage:WaitForChild("RaycastActor")

function Raycast.new(header: string, directory: Instance, raycastParam: RaycastParams?, actors: number?)
	if not header then
		header = tostring(math.random(99, 1999))
	end
	if not raycastParam then
		raycastParam = RaycastParams.new()
	end
	
	local meta = setmetatable({}, Raycast)
	meta.filter = raycastParam
	meta.origin = Vector3.new()
	meta.direction = Vector3.new()
	meta.state = "inactive"
	meta.inputs = {}
	meta.outputs = {}
	meta.connections = {}
	meta.task = {}
	meta._actors = {}
	
	meta.Signal = Signal(`Signal-{header}`)
	
	if not directory:FindFirstChild(`Actors-{header}`) then
		Instance.new("Folder", directory).Name = `Actors-{header}`
	end
	
	for idx=1,actors or 4 do
		local actor = sampleActor:Clone()
		meta.inputs[idx] = actor.Input
		meta.outputs[idx] = actor.Output.Event
		
		if RunService:IsClient() then
			actor.Client.Enabled = true
			actor.Server.Enabled = false
		else
			actor.Client.Enabled = false
			actor.Server.Enabled = true
		end
		table.insert(meta._actors, actor)
		actor.Parent = directory:FindFirstChild(`Actors-{header}`)
	end
	
	meta:_bind()
	return meta
end

function Raycast:SetParams(raycastParam: RaycastParams)
	if not raycastParam then return self end
	self.filter = raycastParam
    return self
end

function Raycast:SetOrigin(origin: Vector3)
	if not origin then return self end
	self.origin = origin
    return self
end

function Raycast:SetDirection(direction: Vector3)
	if not direction then return self end
	self.direction = direction
    return self
end

function Raycast:_unbind()
	for _, c in self.connections do
		c:Disconnect()
	end
	table.clear(self.connections)
end

function Raycast:_bind()
	for idx, output: RBXScriptSignal in self.outputs do
		self.connections[idx] = output:Connect(function(...)
			self.Signal:Fire(...)
		end)
	end
end

function Raycast:Run(forceRun: boolean?)
	if not forceRun then
		if self.state == "active" then return self end
	end
	self.state = "active"
	local newTask
	newTask = task.spawn(function()
		while PreSimulation:Wait() do
			for _, input: BindableEvent in self.inputs do
				input:Fire(self.origin, self.direction, self.filter)
			end
		end
	end)
	table.insert(self.task, newTask)
    return self
end

function Raycast:Stop()
	if self.state == "inactive" then return self end
	
	for _, t in self.task do
		task.cancel(t)
	end
	table.clear(self.task)
	
	self.state = "inactive"
    return self
end

function Raycast:Destroy()
	self:Stop()
	self:_unbind()
	self.Signal:Destroy()
	for _, actor in self._actors do
		actor.Parent:Destroy()
		break
	end
	table.clear(self)
	setmetatable(self, nil)
end

return Raycast :: typeof(Raycast)