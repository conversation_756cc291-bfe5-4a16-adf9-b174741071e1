type TInputState = "began" | "ended";

type TInputDeclaration = {
    Keys: string[];

    // All keys pressed
    Func: (state: TInputState, holdTime?: number) => void;

    Ordered?: boolean;
    Priority?: number;

    MaxHoldTIme?: number;
    AlterFunc?: (state: TInputState, holdTime?: number) => void;

    // Individual key pressed
    Funcs?: { [key: string]: (state: TInputState, holdTime?: number) => void };
    ResetFuncs?: boolean; // If true, it won't fire individual key functions if the main function is fired

    MaxTime?: number;
    LateFunc?: () => void;
};

export declare const Input: {
    Keys: TInputDeclaration[];
};