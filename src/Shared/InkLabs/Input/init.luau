--Version 2.4
--Source:
--https://devforum.roblox.com/t/customizable-keybinds-better-uis-or-advanced-cas/2910676

local UIS = game:GetService("UserInputService")
local RunS = game:GetService("RunService")

local ConnectionStarted = false

local MaxHoldTime = {}
local ComboHoldTime = {}

local TimeConnections = {}

local ActiveKeys = {}

local AllKeys = {}

local metaKeys = {}
metaKeys.__newindex = function(t, index, values)
	if values == nil then
		for i,key in ipairs(metaKeys[index]["Keys"]) do
			if ActiveKeys[key] ~= nil then
				table.remove(ActiveKeys[key].Indexes, index)
				table.remove(ActiveKeys[key].Parents, index)
				table.remove(ActiveKeys[key].Priorities, index)
			end
		end

		metaKeys[index] = nil
		AllKeys[index] = nil
		return
	end
	assert(values.Keys, "Keys are nil")

	metaKeys[index] = values
	AllKeys[index] = table.clone(values["Keys"])
	
	for i, key in ipairs(values.Keys) do
		if ActiveKeys[key] == nil then
			ActiveKeys[key] = {
				Active = false,
				Indexes = {i},
				Parents = {index},
				Priorities = {values.Priority or 1} --
			}
			
			if game.UserInputService.TouchEnabled == true then
				if values.GUIs ~= nil and values.GUIs[key] ~= nil then
					local GUI = values.GUIs[key]
					GUI.Visible = true

					GUI.MouseButton1Down:Connect(function()
						ActiveKeys[key](Enum.UserInputState.Begin)
					end)
					GUI.MouseButton1Up:Connect(function()
						ActiveKeys[key](Enum.UserInputState.End)
					end)
				end
			end
			
			local ChangedKeys = {}
			local Active = {}
			
			local ActivatedKeys = {}
			local ActivatedConnections = {}
			
			setmetatable(ActiveKeys[key], {
				__call = function(t, ...)
					local values = {...}
					local state = values[1]

					local MPK = {} --MostPrioritizedKeys or HPK (Highest Priority Keys)
					local highest = 0
				

					for number, priority in pairs(ActiveKeys[key].Priorities) do
						if priority > highest then
							highest = priority
							table.clear(MPK)
							table.insert(MPK, number)
						elseif priority == highest then
							table.insert(MPK, number)
						end
					end
					
					local function duplicatesExist(arr: {}) --returns true if two or more values in array equal, false otherwise
						local hash = {}

						for i = 1, #arr do
							local v = arr[i]
							if hash[v] == nil then
								hash[v] = i
							else
								return true
							end
						end
						return false
					end
					
					for i, number in ipairs(MPK) do
						local index = ActiveKeys[key].Parents[number]
						local Keytable = metaKeys[index]
						
						if duplicatesExist(AllKeys[index]) then
							if Keytable.MaxTime == nil then
								Keytable.MaxTime = 3
							end
							
							if ActivatedKeys[index] == nil then
								ActivatedKeys[index] = 1
							end

							if ActivatedKeys[index] == number then
								
							else
								continue
							end
						end
						
						if state == Enum.UserInputState.Begin then
							if Keytable.Funcs ~= nil then
								local Func = Keytable.Funcs[key] or Keytable.Funcs[i]
								if Func ~= nil then
									Func(state)
									MaxHoldTime[key] = tick()
								end
							end

							if Keytable.MaxHoldTime ~= nil then
								MaxHoldTime[key] = tick()
							end

							if Keytable.MaxTime ~= nil and TimeConnections[index] == nil then
								task.spawn(function()
									local MaxTime = 0
									TimeConnections[index] = RunS.Heartbeat:Connect(function(dt)
										MaxTime += dt
										
										if MaxTime > Keytable.MaxTime then
											for i, key in ipairs(AllKeys[index]) do
												ActivatedKeys[index] = 1
												if Keytable.Ordered == true then
													if ChangedKeys[index] ~= nil then
														Keytable.Keys[ChangedKeys[index]] = key
													end
												else
													Keytable.Keys[i] = key
												end
											end
											
											if Keytable.LateFunc ~= nil then
												Keytable.LateFunc()
											end
											TimeConnections[index]:Disconnect()
											TimeConnections[index] = nil
										end
									end)
								end)
							end
							ActiveKeys[key].Active = true

							if Keytable.Ordered == true then

								for i=1, 10 do
									if Keytable.Keys[i] ~= nil then
										if Keytable.Keys[i] == key then
											Keytable.Keys[i] = nil
											ChangedKeys[index] = i
										end
										break
									end
								end
							else
								Keytable.Keys[ActiveKeys[key].Indexes[i]] = nil
							end

							if #Keytable.Keys == 0 then
								ComboHoldTime[index] = tick()
								Keytable.Func(state)
							end
						else -- ended
							local function KeyEnded()
								ActiveKeys[key].Active = false

								if Keytable.Ordered == true then
									if ChangedKeys[index] ~= nil then
										Keytable.Keys[ChangedKeys[index]] = key
									end
								else
									Keytable.Keys[ActiveKeys[key].Indexes[i]] = key
								end
							end
							
							if Keytable.MaxHoldTime ~= nil then
								if tick() - MaxHoldTime[key] > Keytable.MaxHoldTime then
									if Keytable.AlterFunc ~= nil then
										Keytable.AlterFunc(tick() - MaxHoldTime[key])
									end

									KeyEnded()

									continue
								end
							end
							
							if Keytable.Funcs ~= nil then
								local Func = Keytable.Funcs[key] or Keytable.Funcs[i]
								if Func ~= nil then
									if Keytable.Keys ~= 0 or Keytable.Keys == 0 and Keytable.ResetFuncs ~= true then
										Func(state, tick() - MaxHoldTime[key])
									end
								end
							end

							if #Keytable.Keys == 0 then
								Keytable.Func(state, tick() - ComboHoldTime[index])
								ComboHoldTime[number] = nil
							end
							
							if Keytable.MaxTime ~= nil then
								ActiveKeys[key].Active = false
								continue 
							end
							
							KeyEnded()
						end
					end
					if state == "ended" then
						for i,v in pairs(ActivatedKeys) do
							ActivatedKeys[i] = v+1
						end
					end
				end,
			})
		else
			table.insert(ActiveKeys[key].Indexes, i)
			table.insert(ActiveKeys[key].Parents, index)
			table.insert(ActiveKeys[key].Priorities, values.Priority or 1)
		end
	end
end

local KeyHandler = {
	Keys = setmetatable({}, metaKeys) --Keytable
}

local beganconnection
local endedconnection


function KeyHandler:Start()
	if ConnectionStarted == false then
		ConnectionStarted = true
		beganconnection = UIS.InputBegan:Connect(function(input, gPE)
			local Key = input.KeyCode.Name
			--if gPE then return end
			if ActiveKeys[Key] and ActiveKeys[Key].Active == false then
				ActiveKeys[Key](Enum.UserInputState.Begin)
			end
		end)
		endedconnection = UIS.InputEnded:Connect(function(input, gPE)
			local Key = input.KeyCode.Name
			--if gPE then return end
			if ActiveKeys[Key] and ActiveKeys[Key].Active == true then
				ActiveKeys[Key](Enum.UserInputState.End)
			end
		end)
	else
		if beganconnection ~= nil then
			KeyHandler:Stop()
			KeyHandler:Start()
		else
			warn("KeyHandler is already started")
		end
	end
end

function KeyHandler:Stop()
	if ConnectionStarted == true then
		ConnectionStarted = false
		beganconnection:Disconnect()
		endedconnection:Disconnect()
	else
		warn("KeyHandler is not active")
	end
end

return KeyHandler