declare class ISuperTween {
    Completed: RBXScriptSignal;
    Updated: RBXScriptSignal;
    Instance: Instance;
    TweenInfo: TweenInfo;
    Properties: { [key: string]: unknown };

    constructor(instance: Instance, tweenInfo: TweenInfo, properties: { [key: string]: unknown });

    Play(): void;

    Reset(): void;

    Cancel(): void;

    PropertyExists(propertyName: string): boolean;

    IsWhitelisted(custom: unknown): boolean;

    IndexOriginalProperties(): void;

    StartLoop(): void;

    Update(t: number): void;
}

export = ISuperTween;