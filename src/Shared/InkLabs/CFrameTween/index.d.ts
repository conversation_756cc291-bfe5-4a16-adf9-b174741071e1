type CFrameTweenProps = {
    Part: BasePart | MeshPart,
    TweenInfo?: TweenInfo,
    Properties: {
        InitialCFrame: CFrame,
        MiddleCFrame?: CFrame,
        FinalCFrame: CFrame,
    }
}

declare class CFrameTween {
    startingPoint: CFrame;
    endPoint: CFrame;
    target: BasePart | MeshPart;
    weight: NumberValue;
    tweenInfo: TweenInfo;
    tween: Tween;
    WP: CFrame;

    constructor(props: CFrameTweenProps);

    Start(): void;

    Pause(): void;

    Resume(): void;

    Cancel(): void;

    Scrub(time: number): void;
}

export = CFrameTween;