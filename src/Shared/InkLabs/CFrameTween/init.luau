local BetterTween = {}
BetterTween.__index = BetterTween

local ts = game:GetService("TweenService")
local rs = game:GetService("RunService")
local quaternionModule = require(script.Quaternion)

function lerp(t, a, b)
	return a + (b - a) * t
end

function quadraticBezier(t, p0, p1, p2)
	local l1 = lerp(t, p0, p1)
	local l2 = lerp(t, p1, p2)
	local quad = lerp(t, l1, l2)
	return quad
end

function slerp(t, a, b)
	local dot = a.x*b.x + a.y*b.y + a.z*b.z + a.w*b.w

	if dot < 0 then
		b = -b
		dot = -dot
	end

	local epsilon = 1e-6

	if dot > 1 - epsilon then
		return a:Lerp(b, t)
	else
		local theta_0 = math.acos(dot)
		local theta = theta_0 * t
		local sin_theta = math.sin(theta)
		local sin_theta_0 = math.sin(theta_0)

		local s0 = math.cos(theta) - dot * sin_theta / sin_theta_0
		local s1 = sin_theta / sin_theta_0

		return (a * s0) + (b * s1)
	end
end

type BetterTweenProps = {
	Part: BasePart | MeshPart,
    TweenInfo: TweenInfo?,
    Properties: {
        InitialCFrame: CFrame,
        MiddleCFrame: CFrame?,
        FinalCFrame: CFrame,
    }
}

function BetterTween.new(props: BetterTweenProps)
	local ConstructedTween = {}
	setmetatable(ConstructedTween, BetterTween)
	ConstructedTween.startingPoint = props.Properties.InitialCFrame --CFrame1
	ConstructedTween.endPoint = props.Properties.End --End
	ConstructedTween.target = props.Part --part
	ConstructedTween.weight = Instance.new("NumberValue")
	ConstructedTween.tweeninfo = props.TweenInfo or TweenInfo.new() --tweeninfo or TweenInfo.new()
	ConstructedTween.tween = ts:Create(ConstructedTween.weight, ConstructedTween.tweeninfo, { Value = 1 })
	ConstructedTween.WP = props.Properties.MiddleCFrame --wp

	return ConstructedTween
end

function BetterTween:Start()
	self.step = nil

	if typeof(self.endPoint) == "CFrame" then
		local startQuat = quaternionModule.fromCFrame(self.startingPoint)
		local endQuat = quaternionModule.fromCFrame(self.endPoint)
		local closestEndQuat = startQuat:slerpClosest(endQuat, 0)

		if self.WP then
			self.step = rs.PreSimulation:Connect(function()
				local position = quadraticBezier(self.weight.Value, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
				local orientation = closestEndQuat:slerp(endQuat, self.weight.Value)
				self.target.CFrame = CFrame.new(position) * orientation:toCFrame()
			end)
		else
			self.step = rs.PreSimulation:Connect(function()
				local position = lerp(self.weight.Value, self.startingPoint.Position, self.endPoint.Position)
				local orientation = closestEndQuat:slerp(endQuat, self.weight.Value)
				self.target.CFrame = CFrame.new(position) * orientation:toCFrame()
			end)
		end
	else
		if self.WP then
			self.step = rs.PreSimulation:Connect(function()
				self.target.Position = quadraticBezier(self.weight.Value, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
			end)
		else
			self.step = rs.PreSimulation:Connect(function()
				self.target.Position = lerp(self.weight.Value, self.startingPoint.Position, self.endPoint.Position)
			end)
		end
	end
	self.tween:Play()
	self.tween.Completed:Wait()
	task.wait()
	self.step:Disconnect()
end

function BetterTween:Pause()
	self.tween:Pause()
	self.step:Disconnect()
end

function BetterTween:Resume()
	if typeof(self.endPoint) == "CFrame" then
		local startQuat = quaternionModule.fromCFrame(self.startingPoint)
		local endQuat = quaternionModule.fromCFrame(self.endPoint)

		if self.WP then
			self.step = rs.PreSimulation:Connect(function()
				local position = quadraticBezier(self.weight.Value, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
				local orientation = slerp(self.weight.Value, startQuat, endQuat)
				self.target.CFrame = CFrame.new(position) * quaternionModule.fromCFrame(orientation)
			end)
		else
			self.step = rs.PreSimulation:Connect(function()
				local position = lerp(self.weight.Value, self.startingPoint.Position, self.endPoint.Position)
				local orientation = slerp(self.weight.Value, startQuat, endQuat)
				self.target.CFrame = CFrame.new(position) * quaternionModule.fromCFrame(orientation)
			end)
		end
	else
		if self.WP then
			self.step = rs.PreSimulation:Connect(function()
				self.target.Position = quadraticBezier(self.weight.Value, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
			end)
		else
			self.step = rs.PreSimulation:Connect(function()
				self.target.Position = lerp(self.weight.Value, self.startingPoint.Position, self.endPoint.Position)
			end)
		end
	end
	self.tween:Play()
end

function BetterTween:Cancel()
	self.tween:Pause()
	self.tween:Cancel()
	self.step:Disconnect()
end

function BetterTween:Scrub(weight)
	if typeof(self.endPoint) == "CFrame" then
		local startQuat = quaternionModule.fromCFrame(self.startingPoint)
		local endQuat = quaternionModule.fromCFrame(self.endPoint)

		if self.WP then
			local position = quadraticBezier(weight, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
			local orientation = slerp(weight, startQuat, endQuat)
			self.target.CFrame = CFrame.new(position) * quaternionModule.toCFrame(orientation)
		else
			local position = lerp(weight, self.startingPoint.Position, self.endPoint.Position)
			local orientation = slerp(weight, startQuat, endQuat)
			self.target.CFrame = CFrame.new(position) * quaternionModule.toCFrame(orientation)
		end
	else
		if self.WP then
			self.target.Position = quadraticBezier(weight, self.startingPoint.Position, self.WP.Position, self.endPoint.Position)
		else
			self.target.Position = lerp(weight, self.startingPoint.Position, self.endPoint.Position)
		end
	end
end

return BetterTween
