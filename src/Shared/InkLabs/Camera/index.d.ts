type CameraViewType =
    "Default"
    | "FirstPerson"
    | "FirstPersonVariant"
    | "ThirdPerson"
    | "ShiftLock"
    | "Cinematic"
    | string;
type CameraViewSettings = {
    CharacterVisibility: "All" | "None",
    Smoothness: number,
    Zoom: number,
    AlignChar: boolean,
    Offset: <PERSON><PERSON>e,
    MinZoom: number,
    MaxZoom: number,
    LockMouse: boolean,
    BodyFollow: boolean,
    Wobble: number
};

declare const CameraService: {
    SetVerticalRange(angle: number): void;
    SetCameraHost(newHost: BasePart): void;
    SetWobbling(value: number): void;
    TiltAllAxes(x: number, y: number, z: number): void;
    Tilt(degree: number): void;
    SetShake(intensity: number, toggle?: boolean): void;
    Shake(intensity: number, duration: number): void;
    ChangeFOV(newFOV: number, instant: boolean): void;
    ChangeSensitivity(val: number): void;
    Change(property: string, newVal: unknown, changeDefaultProperty: boolean): void;
    LockCameraPanning(lockXAxis: boolean, lockYAxis: boolean, lockAtX: number, lockAtY: number): void;
    CreateNewCameraView(id: CameraViewType, settingsArray: CameraViewSettings): void;
    SetCameraView(cameraViewType: CameraViewType): void;
    SetShakeIntensity(intensity: number): void;
};

export = CameraService;