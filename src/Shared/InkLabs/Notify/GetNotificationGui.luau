return function()
    local bannerNotification = Instance.new("ScreenGui")
    bannerNotification.Name = "BannerNotification"
    bannerNotification.DisplayOrder = 1e+03
    bannerNotification.IgnoreGuiInset = true
    bannerNotification.ScreenInsets = Enum.ScreenInsets.DeviceSafeInsets
    bannerNotification.ResetOnSpawn = false
    bannerNotification.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

    local activeNotifications = Instance.new("Frame")
    activeNotifications.Name = "ActiveNotifications"
    activeNotifications.AnchorPoint = Vector2.new(0.5, 0.5)
    activeNotifications.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    activeNotifications.BackgroundTransparency = 1
    activeNotifications.BorderColor3 = Color3.fromRGB(0, 0, 0)
    activeNotifications.BorderSizePixel = 9
    activeNotifications.Position = UDim2.fromScale(0.5, 0.5)
    activeNotifications.Size = UDim2.fromScale(1, 1)

    local list = Instance.new("UIListLayout")
    list.Name = "List"
    list.HorizontalAlignment = Enum.HorizontalAlignment.Center
    list.SortOrder = Enum.SortOrder.LayoutOrder
    list.Parent = activeNotifications

    local padding = Instance.new("UIPadding")
    padding.Name = "Padding"
    padding.PaddingTop = UDim.new(0, 10)
    padding.Parent = activeNotifications

    activeNotifications.Parent = bannerNotification

    local canvas = Instance.new("Frame")
    canvas.Name = "Canvas"
    canvas.AutomaticSize = Enum.AutomaticSize.Y
    canvas.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    canvas.BackgroundTransparency = 1
    canvas.BorderColor3 = Color3.fromRGB(0, 0, 0)
    canvas.BorderSizePixel = 0
    canvas.Size = UDim2.fromScale(1, 0)
    canvas.Visible = false

    local shape = Instance.new("ImageLabel")
    shape.Name = "Shape"
    shape.Image = "rbxassetid://11983017276"
    shape.ImageColor3 = Color3.fromRGB(0, 0, 0)
    shape.ImageTransparency = 0.5
    shape.ScaleType = Enum.ScaleType.Slice
    shape.SliceCenter = Rect.new(512, 512, 512, 512)
    shape.AnchorPoint = Vector2.new(0.5, 0.5)
    shape.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    shape.BackgroundTransparency = 1
    shape.BorderColor3 = Color3.fromRGB(0, 0, 0)
    shape.BorderSizePixel = 0
    shape.Position = UDim2.fromScale(0.5, 0.5)
    shape.Size = UDim2.fromOffset(60, 60)

    local scale = Instance.new("UIScale")
    scale.Name = "Scale"
    scale.Parent = shape

    shape.Parent = canvas

    local notification = Instance.new("ImageLabel")
    notification.Name = "Notification"
    notification.Image = "rbxassetid://11942813307"
    notification.ImageColor3 = Color3.fromRGB(0, 0, 0)
    notification.ImageTransparency = 0.3
    notification.ScaleType = Enum.ScaleType.Slice
    notification.SliceCenter = Rect.new(512, 512, 512, 512)
    notification.AnchorPoint = Vector2.new(0.5, 0)
    notification.AutomaticSize = Enum.AutomaticSize.XY
    notification.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    notification.BackgroundTransparency = 1
    notification.BorderColor3 = Color3.fromRGB(0, 0, 0)
    notification.BorderSizePixel = 0
    notification.Position = UDim2.fromScale(0.5, 0)

    local scale1 = Instance.new("UIScale")
    scale1.Name = "Scale"
    scale1.Parent = notification

    local padding1 = Instance.new("UIPadding")
    padding1.Name = "Padding"
    padding1.PaddingBottom = UDim.new(0, 5)
    padding1.PaddingLeft = UDim.new(0, 10)
    padding1.PaddingRight = UDim.new(0, 25)
    padding1.PaddingTop = UDim.new(0, 5)
    padding1.Parent = notification

    local icon = Instance.new("ImageLabel")
    icon.Name = "Icon"
    icon.Image = "rbxassetid://11326670020"
    icon.ImageTransparency = 0.1
    icon.ScaleType = Enum.ScaleType.Fit
    icon.AnchorPoint = Vector2.new(0, 0.5)
    icon.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    icon.BackgroundTransparency = 1
    icon.BorderSizePixel = 0
    icon.Position = UDim2.new(0, 15, 0.5, 0)
    icon.Size = UDim2.fromOffset(30, 30)
    icon.ZIndex = 2
    icon.Parent = notification

    local texts = Instance.new("CanvasGroup")
    texts.Name = "Texts"
    texts.AutomaticSize = Enum.AutomaticSize.XY
    texts.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    texts.BackgroundTransparency = 1
    texts.BorderColor3 = Color3.fromRGB(0, 0, 0)
    texts.BorderSizePixel = 0
    texts.Size = UDim2.new(1, -50, 0, 50)

    local header = Instance.new("TextLabel")
    header.Name = "Header"
    header.FontFace = Font.new(
    "rbxasset://fonts/families/GothamSSm.json",
    Enum.FontWeight.Medium,
    Enum.FontStyle.Normal
    )
    header.Text = "Header"
    header.TextColor3 = Color3.fromRGB(255, 255, 255)
    header.TextSize = 17
    header.TextTransparency = 0.1
    header.TextWrapped = true
    header.TextXAlignment = Enum.TextXAlignment.Left
    header.AutomaticSize = Enum.AutomaticSize.XY
    header.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    header.BackgroundTransparency = 1
    header.BorderColor3 = Color3.fromRGB(0, 0, 0)
    header.BorderSizePixel = 0
    header.ZIndex = 2

    local sizeConstraint = Instance.new("UISizeConstraint")
    sizeConstraint.Name = "SizeConstraint"
    sizeConstraint.MaxSize = Vector2.new(300, math.huge)
    sizeConstraint.MinSize = Vector2.new(250, 0)
    sizeConstraint.Parent = header

    header.Parent = texts

    local list1 = Instance.new("UIListLayout")
    list1.Name = "List"
    list1.Padding = UDim.new(0, 2)
    list1.SortOrder = Enum.SortOrder.LayoutOrder
    list1.VerticalAlignment = Enum.VerticalAlignment.Center
    list1.Parent = texts

    local padding2 = Instance.new("UIPadding")
    padding2.Name = "Padding"
    padding2.PaddingBottom = UDim.new(0, 10)
    padding2.PaddingLeft = UDim.new(0, 65)
    padding2.PaddingTop = UDim.new(0, 10)
    padding2.Parent = texts

    local description = Instance.new("TextLabel")
    description.Name = "Description"
    description.FontFace = Font.new("rbxasset://fonts/families/GothamSSm.json")
    description.Text = "Description"
    description.TextColor3 = Color3.fromRGB(255, 255, 255)
    description.TextSize = 14
    description.TextTransparency = 0.5
    description.TextWrapped = true
    description.TextXAlignment = Enum.TextXAlignment.Left
    description.AutomaticSize = Enum.AutomaticSize.XY
    description.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    description.BackgroundTransparency = 1
    description.BorderColor3 = Color3.fromRGB(0, 0, 0)
    description.BorderSizePixel = 0
    description.ZIndex = 2

    local sizeConstraint1 = Instance.new("UISizeConstraint")
    sizeConstraint1.Name = "SizeConstraint"
    sizeConstraint1.MaxSize = Vector2.new(300, math.huge)
    sizeConstraint1.MinSize = Vector2.new(250, 0)
    sizeConstraint1.Parent = description

    description.Parent = texts

    texts.Parent = notification

    notification.Parent = canvas

    local padding3 = Instance.new("UIPadding")
    padding3.Name = "Padding"
    padding3.PaddingBottom = UDim.new(0, 10)
    padding3.PaddingTop = UDim.new(0, 10)
    padding3.Parent = canvas

    canvas.Parent = bannerNotification
    return bannerNotification
end