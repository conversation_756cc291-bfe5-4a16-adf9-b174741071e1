export type Style = {
	Icon: string,
	ContentTransparency: number,
	ContentColor: Color3,
	BackgroundTransparency: number,
	BackgroundColor: Color3,
	Sound: string
}

local Styles: {[string]: Style} = {
	
	["Default"] = { 
		Icon = "rbxassetid://17250391808", 

		ContentTransparency = 0,
		ContentColor = Color3.new(1, 1, 1),

		BackgroundTransparency = 0.3,
		BackgroundColor = Color3.new(0, 0, 0),
		Sound = "rbxassetid://424002310"
	},
	
	["Error"] = { 
		Icon = "rbxassetid://17250391958", 

		ContentTransparency = 0,
		ContentColor = Color3.new(1, 0.411765, 0.411765),

		BackgroundTransparency = 0.3,
		BackgroundColor = Color3.new(0.227451, 0.137255, 0.137255),
		Sound = "rbxassetid://5274463739"
	},
	
	["Tip"] = { 
		Icon = "rbxassetid://17250391880", 

		ContentTransparency = 0,
		ContentColor = Color3.new(0.521569, 0.776471, 1),

		BackgroundTransparency = 0.3,
		BackgroundColor = Color3.new(0.215686, 0.247059, 0.290196),
		Sound = "rbxassetid://8723328430"
	},
	
	["Success"] = { 
		Icon = "rbxassetid://17250449894", 

		ContentTransparency = 0,
		ContentColor = Color3.new(0.615686, 1, 0.737255),

		BackgroundTransparency = 0.3,
		BackgroundColor = Color3.new(0.117647, 0.184314, 0.121569),
		Sound = "rbxassetid://9063660441"
	},
	
	["Gifted"] = { 
		Icon = "rbxassetid://17250449894", 

		ContentTransparency = 0,
		ContentColor = Color3.new(0.847059, 0.721569, 1),

		BackgroundTransparency = 0.3,
		BackgroundColor = Color3.new(0.247059, 0.211765, 0.329412),
		Sound = "rbxassetid://10066947742"
	},
	
}

return Styles
