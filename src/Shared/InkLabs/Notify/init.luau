local s, m = pcall(function()
	-- SERVICES --
	local RunService = game:GetService('RunService')
	local Players = game:GetService('Players')
	local TweenService = game:GetService('TweenService')
	local SoundService = game:GetService('SoundService')

	-- LIBRARIES --
	local Network = require(script.Parent.Network)

	-- EVENT --
	local NotificationEvent = Network.RemoteEvent.new("Notify")

	-- RUNTIME --
	local isClient = RunService:IsClient() and true or false
	local isSetup = false

	-- GUI --
	local PlayerGui
	local Gui
	local ActiveFrame
	local NotificationTemplate

	-- TWEEN INFO --
	local Info = TweenInfo.new(
		0.5, 
		Enum.EasingStyle.Exponential, 
		Enum.EasingDirection.Out, 
		0, 
		false, 
		0
	)

	-- SOUNDS --
	local Sounds: {[string]: Sound} = {}

	-- ICONS --
	local Styles = require(script.Styles)

	-- TYPES --
	type Props = {
		Player: Player?,
		Header: string,
		Description: string,
		Style: string,
		Duration: number,

		BackgroundTransparency: number?,
		BackgroundColor: Color3?,

		ContentTransparency: number?,
		ContentColor: Color3?
	}

	local Default: Props = {
		Header = "Notification",
		Description = "You have been notified!",
		Style = "Default",
		Duration = 5
	}

	local function playSound(soundId: string)
		local sound: Sound = Sounds[soundId]
		if sound then
			sound:Play()
			return
		end

		local soundGroup: SoundGroup = SoundService:FindFirstChild('Notification')
		if not soundGroup then
			soundGroup = Instance.new('SoundGroup')
			soundGroup.Name = 'Notification'
			soundGroup.Parent = SoundService
		end

		local newSound = Instance.new('Sound')
		newSound.Name = soundId
		newSound.SoundId = soundId
		newSound.Parent = soundGroup
		newSound:Play()

		Sounds[soundId] = newSound
	end

	local function validateProps(props: Props): Props
		props.Header = props.Header or Default.Header
		props.Description = props.Description or Default.Description
		props.Style = props.Style or Default.Style
		props.Duration = props.Duration or Default.Duration

		return props
	end

	local function setupClient()
		if not isClient or isSetup then return end
		PlayerGui = Players.LocalPlayer:WaitForChild('PlayerGui')
		Gui = require(script.GetNotificationGui)()
		Gui.Parent = PlayerGui
		ActiveFrame = Gui:WaitForChild('ActiveNotifications')
		NotificationTemplate = Gui:WaitForChild('Canvas')
		isSetup = true
	end

	local latestOrder = 0

	local function executeClientProcess(props: Props)
		setupClient()
		props = validateProps(props)

		local newCanvas = NotificationTemplate:Clone()
		local notification = newCanvas.Notification
		local shape = newCanvas.Shape

		local style: Styles.Style = Styles[props.Style] or Styles.Default

		newCanvas.Name = props.Header
		latestOrder += 1
		newCanvas.LayoutOrder = latestOrder

		notification.Texts.Header.Text = props.Header
		notification.Texts.Description.Text = props.Description
		notification.Icon.Image = style.Icon

		notification.Icon.ImageTransparency = 1
		notification.ImageTransparency = 1
		notification.Texts.GroupTransparency = 1

		shape.ImageColor3 = style.BackgroundColor
		notification.Texts.GroupColor3 = style.ContentColor
		notification.Icon.ImageColor3 = style.ContentColor

		shape.Scale.Scale = 0
		shape.ImageTransparency = 1

		newCanvas.Visible = true
		newCanvas.Parent = ActiveFrame

		-- // Anim start
		if style.Sound then
			playSound(style.Sound)
		end

		TweenService:Create(shape, Info, { ImageTransparency = style.BackgroundTransparency, Size = UDim2.fromOffset(notification.AbsoluteSize.X, notification.AbsoluteSize.Y) }):Play()
		TweenService:Create(shape.Scale, Info, { Scale = 1.2 }):Play()

		-- // --
		task.wait(0.3)
		-- // --

		-- // Transform shape into a NOT circle
		shape.Image = "rbxassetid://11942813307"

		TweenService:Create(shape, Info, { Size = UDim2.fromOffset(notification.AbsoluteSize.X, notification.AbsoluteSize.Y) }):Play()
		TweenService:Create(shape.Scale, Info, { Scale = 1 }):Play()

		-- // --
		task.wait(0.1)
		-- // --

		-- // Fade in
		TweenService:Create(notification.Texts, Info, { GroupTransparency = style.ContentTransparency }):Play()
		TweenService:Create(notification.Icon, Info, { ImageTransparency = style.ContentTransparency }):Play()

		-- // --
		task.wait(props.Duration)
		-- // --

		-- // Duration ended
		TweenService:Create(notification.Texts, Info, { GroupTransparency = 1 }):Play()
		TweenService:Create(notification.Icon, Info, { ImageTransparency = 1 }):Play()

		-- // --
		task.wait(0.2)
		-- // --

		-- // Revert shape to circle
		shape.Image = "rbxassetid://11983017276"

		TweenService:Create(shape, Info, {
			Size = UDim2.fromOffset(60, 60),
			ImageTransparency = style.BackgroundTransparency
		}):Play()

		TweenService:Create(shape.Scale, Info, { Scale = 1.2 }):Play()

		-- // --
		task.wait(0.3)
		-- // --

		TweenService:Create(shape, Info, { ImageTransparency = 1 }):Play()
		TweenService:Create(shape.Scale, Info, { Scale = 0 }):Play()

		-- // --
		task.wait(0.3)
		-- // --

		TweenService:Create(newCanvas.Padding, Info, {
			PaddingTop = UDim.new(0, 0),
			PaddingBottom = UDim.new(0, 0)
		}):Play()

		TweenService:Create(notification.Scale, Info, { Scale = 0 }):Play()

		-- // --
		task.wait(0.3)
		-- // --

		newCanvas:Destroy()
	end

	local function executeServerProcess(props: Props)
		if props.Player and (typeof(props.Player) ~= 'Instance' or not props.Player:IsDescendantOf(Players)) then return end

		if props.Player then
			NotificationEvent:FireClient(props.Player, props)
			return
		end
		
		if #Players:GetPlayers() < 1 then
			repeat task.wait() until #Players:GetPlayers() >= 1
			local plr = Players:GetPlayers()[1]
			if not plr.Character then
				repeat task.wait() until plr.Character
			end
			task.wait(0.5)
		end
		
		NotificationEvent:FireAllClients(props)
	end

	if isClient then
		NotificationEvent.OnEvent:Connect(executeClientProcess)
	end

	return function(props: Props)
		if not isClient then
			task.spawn(executeServerProcess, props)
		else
			task.spawn(executeClientProcess, props)
		end
	end
end)

if s then
	debug.setmemorycategory(`[INK] [{script.Name}]`)
	return m
else
	warn(`Error initializing {script.Name}:`, m)
	return {}
end
