interface IBezier {
    AddBezierPoint(point: Vector3 | BasePart, index?: number): void;

    ChangeBezierPoint(index: number, point: Vector3 | BasePart): void;

    GetAllPoints(): Array<Vector3>;

    GetPoint(index: number): Vector3 | undefined;

    RemoveBezierPoint(index: number): void;

    UpdateLength(): void;

    CalculatePositionAt(t: number): Vector3;

    CalculatePositionRelativeToLength(t: number): Vector3;

    CalculateDerivativeAt(t: number): Vector3;

    CalculateDerivativeRelativeToLength(t: number): Vector3;

    CreateVector3Tween(
        object: Instance | Map<unknown, unknown>, propertyTable: Map<string, unknown>, tweenInfo: TweenInfo,
        relativeToLength?: boolean
    ): void;

    CreateCFrameTween(
        object: Instance | Map<unknown, unknown>, propertyTable: Map<string, unknown>, tweenInfo: TweenInfo,
        relativeToLength?: boolean
    ): void;
}

interface IBezierConstructor {
    new(points: Vector3[] | BasePart[]): IBezier;
}

declare const Bezier: IBezierConstructor;