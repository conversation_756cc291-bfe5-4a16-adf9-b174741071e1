-- MAIN --
local RagdollUtils = {}

-- R6 UTILS --
RagdollUtils.R6 = {
	AttachmentCFrames = {
		["Neck"] = {CFrame.new(0, 1, 0, 0, -1, 0, 1, 0, -0, 0, 0, 1), CFrame.new(0, -0.5, 0, 0, -1, 0, 1, 0, -0, 0, 0, 1)},
		["Left Shoulder"] = {CFrame.new(-1.3, 0.75, 0, -1, 0, 0, 0, -1, 0, 0, 0, 1), CFrame.new(0.2, 0.75, 0, -1, 0, 0, 0, -1, 0, 0, 0, 1)},
		["Right Shoulder"] = {CFrame.new(1.3, 0.75, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1), CFrame.new(-0.2, 0.75, 0, 1, 0, 0, 0, 1, 0, 0, 0, 1)},
		["Left Hip"] = {CFrame.new(-0.5, -1, 0, 0, 1, -0, -1, 0, 0, 0, 0, 1), CFrame.new(0, 1, 0, 0, 1, -0, -1, 0, 0, 0, 0, 1)},
		["Right Hip"] = {CFrame.new(0.5, -1, 0, 0, 1, -0, -1, 0, 0, 0, 0, 1), CFrame.new(0, 1, 0, 0, 1, -0, -1, 0, 0, 0, 0, 1)},
	},

	RagdollInstanceNames = {
		["RagdollAttachment"] = true,
		["RagdollConstraint"] = true,
		["ColliderPart"] = true,
	},
}

RagdollUtils.R6.CreateColliderPart = function(Part : BasePart)
	if not Part then return end

	local colliderPart = Instance.new("Part")
	colliderPart.Name = "ColliderPart"
	colliderPart.Size = Part.Size/1.7
	colliderPart.Massless = true			
	colliderPart.CFrame = Part.CFrame
	colliderPart.Transparency = 1
	colliderPart.Parent = Part

	local Weld = Instance.new("WeldConstraint")
	Weld.Part0 = colliderPart
	Weld.Part1 = Part
	Weld.Parent = colliderPart
end

RagdollUtils.R6.Ragdoll = function(Character : Model)
	if not Character then return end

	local Humanoid : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
	if not Humanoid then return end

	Humanoid.AutoRotate = false

	for _, Motor : Motor6D in ipairs(Character:GetDescendants()) do
		if not Motor:IsA("Motor6D") then continue end
		if not RagdollUtils.R6.AttachmentCFrames[Motor.Name] then continue end

		Motor.Enabled = false

		-- NECK CHECK --
		local isNeck = Motor.Name == "Neck"

		-- CREATE ATTACHMENTS --
		local AttachmentCFrames = RagdollUtils.R6.AttachmentCFrames[Motor.Name]

		local Attachment0 : Attachment, Attachment1 : Attachment = Instance.new("Attachment"), Instance.new("Attachment")
		Attachment0.Name, Attachment1.Name = "RagdollAttachment", "RagdollAttachment"

		Attachment0.CFrame = AttachmentCFrames[1]
		Attachment1.CFrame = AttachmentCFrames[2]

		Attachment0.Parent = Motor.Part0
		Attachment1.Parent = Motor.Part1

		-- CREATE COLLIDER --
		RagdollUtils.R6.CreateColliderPart(Motor.Part1)

		-- CREATE CONSTRAINT --
		local BallConstraint : BallSocketConstraint = Instance.new("BallSocketConstraint")
		BallConstraint.Attachment0 = Attachment0
		BallConstraint.Attachment1 = Attachment1
		BallConstraint.Name = "RagdollConstraint"
		BallConstraint.Radius = 0.15
		BallConstraint.LimitsEnabled = true
		BallConstraint.TwistLimitsEnabled = if isNeck then true else false
		BallConstraint.MaxFrictionTorque = 0
		BallConstraint.Restitution = 0
		BallConstraint.UpperAngle = if isNeck then 45 else 90
		BallConstraint.TwistLowerAngle = if isNeck then -70 else -45
		BallConstraint.TwistUpperAngle = if isNeck then 70 else 45

		BallConstraint.Parent = Motor.Parent
	end
end

RagdollUtils.R6.Unragdoll = function(Character : Model)
	if not Character then return end

	local Humanoid : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
	if not Humanoid then return end
	if Humanoid.Health <= 0 then return end

	for _, v : Instance in ipairs(Character:GetDescendants()) do
		if RagdollUtils.R6.RagdollInstanceNames[v.Name] then
			v:Destroy()
		end

		if v:IsA("Motor6D") then
			v.Enabled = true
		end
	end

	if not Humanoid then return end -- Checking if the humanoid got destroyed while the loop was being executed somehow
	Humanoid.AutoRotate = true
end
-- // --

-- R15 UTILS --
RagdollUtils.R15 = {
	buildConstraints = require(script:WaitForChild("buildConstraints")),
	buildCollisionFilters = require(script:WaitForChild("buildCollisionFilters")),

	buildAttachmentMap = function(character)
		local attachmentMap = {}

		-- NOTE: GetConnectedParts doesn't work until parts have been parented to Workspace, so
		-- we can't use it (unless we want to have that silly restriction for creating ragdolls)
		for _,part in pairs(character:GetChildren()) do
			if part:IsA("BasePart") then
				for _,attachment in pairs(part:GetChildren()) do
					if attachment:IsA("Attachment") then
						local jointName = attachment.Name:match("^(.+)RigAttachment$")
						local joint = jointName and attachment.Parent:FindFirstChild(jointName) or nil

						if joint then
							attachmentMap[attachment.Name] = {
								Joint = joint,
								Attachment0=joint.Part0[attachment.Name]; 
								Attachment1=joint.Part1[attachment.Name];
							}
						end
					end
				end
			end
		end

		return attachmentMap
	end,
}

RagdollUtils.R15.Ragdoll = function(Character : Model)
	local ragdollConstraints = Character:WaitForChild("RagdollConstraints", 5)
	if not ragdollConstraints then return end

	local Humanoid : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
	if not Humanoid then return end
	
	--Humanoid.PlatformStand = true
	
	-- Disable animate script to stop jitter
	--local Animate : LocalScript? = Character:FindFirstChild("Animate")
	--if Animate then
	--	Animate.Enabled = false
	--end

	-- Stop playing animations
	local Animator : Animator = Humanoid:FindFirstChildWhichIsA("Animator")
	if Animator then
		for _,v in pairs(Animator:GetPlayingAnimationTracks()) do
			v:Stop(0)
		end
	end

	local Head = Character:FindFirstChild("Head")
	if Head then
		task.delay(0.1, function() -- Delay to make sure sounds are disabled, without it sometimes it would still have them playing
			for _, v in pairs(Head:GetChildren()) do
				if not v:IsA("Sound") then continue end
				v:Stop()
			end
		end)
	end

	for _,constraint in pairs(ragdollConstraints:GetChildren()) do
		if not constraint:IsA("Constraint") then continue end

		local rigidJoint = constraint and constraint.RigidJoint and constraint.RigidJoint.Value
		local expectedValue = nil

		if rigidJoint and rigidJoint.Part1 ~= expectedValue then
			rigidJoint.Part1 = expectedValue 
		end
	end
end

RagdollUtils.R15.Unragdoll = function(Character : Model)
	local ragdollConstraints = Character:WaitForChild("RagdollConstraints", 5)
	if not ragdollConstraints then return end

	local Humanoid : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
	if not Humanoid then return end
	
	--local Animate : LocalScript? = Character:FindFirstChild("Animate")
	--if Animate then
	--	Animate.Enabled = true
	--end
	
	--Humanoid.PlatformStand = false
	
	for _, constraint in pairs(ragdollConstraints:GetChildren()) do
		if not constraint:IsA("Constraint") then continue end

		local rigidJoint = constraint and constraint.RigidJoint and constraint.RigidJoint.Value
		local expectedValue = constraint and constraint.Attachment1 and constraint.Attachment1.Parent or nil

		if rigidJoint and rigidJoint.Part1 ~= expectedValue then
			rigidJoint.Part1 = expectedValue 
		end
	end

    Character:PivotTo(CFrame.new(Character:GetPivot().Position + Vector3.new(0, 2, 0)))
end

RagdollUtils.R15.BuildRagdoll = function(Character : Model)
	local HRP : BasePart? = Character:FindFirstChild("HumanoidRootPart")
	if HRP then
		HRP.CanCollide = false
	end 

	local attachmentMap = RagdollUtils.R15.buildAttachmentMap(Character)
	local ragdollConstraints = RagdollUtils.R15.buildConstraints(attachmentMap)
	local collisionFilters = RagdollUtils.R15.buildCollisionFilters(attachmentMap, Character.PrimaryPart)

	collisionFilters.Parent = ragdollConstraints
	ragdollConstraints.Parent = Character
end
-- // --


return RagdollUtils