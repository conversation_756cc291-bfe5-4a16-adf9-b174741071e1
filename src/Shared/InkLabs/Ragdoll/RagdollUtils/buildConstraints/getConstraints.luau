return function()
    local constraints = script.Parent

    local ankle = Instance.new("HingeConstraint")
    ankle.Name = "Ankle"
    ankle.LimitsEnabled = true
    ankle.UpperAngle = 0
    ankle.Parent = constraints

    local default = Instance.new("BallSocketConstraint")
    default.Name = "Default"
    default.LimitsEnabled = true
    default.TwistLimitsEnabled = true
    default.TwistLowerAngle = -15
    default.TwistUpperAngle = 15
    default.UpperAngle = 20
    default.Parent = constraints

    local elbow = Instance.new("HingeConstraint")
    elbow.Name = "Elbow"
    elbow.LimitsEnabled = true
    elbow.LowerAngle = 0
    elbow.UpperAngle = 135
    elbow.Parent = constraints

    local hip = Instance.new("BallSocketConstraint")
    hip.Name = "Hip"
    hip.LimitsEnabled = true
    hip.TwistLimitsEnabled = true
    hip.TwistLowerAngle = -3
    hip.TwistUpperAngle = 3
    hip.UpperAngle = 120
    hip.Parent = constraints

    local knee = Instance.new("HingeConstraint")
    knee.Name = "Knee"
    knee.LimitsEnabled = true
    knee.LowerAngle = -135
    knee.UpperAngle = -10
    knee.Parent = constraints

    local neck = Instance.new("BallSocketConstraint")
    neck.Name = "Neck"
    neck.LimitsEnabled = true
    neck.TwistLimitsEnabled = true
    neck.UpperAngle = 0
    neck.Parent = constraints

    local shoulder = Instance.new("BallSocketConstraint")
    shoulder.Name = "Shoulder"
    shoulder.LimitsEnabled = true
    shoulder.TwistLimitsEnabled = true
    shoulder.TwistLowerAngle = -30
    shoulder.TwistUpperAngle = 30
    shoulder.UpperAngle = 120
    shoulder.Parent = constraints

    local waist = Instance.new("BallSocketConstraint")
    waist.Name = "Waist"
    waist.LimitsEnabled = true
    waist.TwistLimitsEnabled = true
    waist.TwistLowerAngle = -1
    waist.TwistUpperAngle = 1
    waist.UpperAngle = 15
    waist.Parent = constraints

    local wrist = Instance.new("HingeConstraint")
    wrist.Name = "Wrist"
    wrist.LimitsEnabled = true
    wrist.LowerAngle = -20
    wrist.UpperAngle = 20
    wrist.Parent = constraints
end