-- SERVICES --
local Players = game:GetService("Players")
local CS = game:GetService("CollectionService")
local SCS : StarterCharacterScripts = game:GetService("StarterPlayer").StarterCharacterScripts

-- CONFIG --
local RagdollBlacklistTags = { "NoRagdoll" } -- Characters with any of these tags will not be ragdolled.

-- MAIN --
local Service = {}

local RagdollStorage : { [Model] : { DurationTask : any } } = {}
local RagdollUtils = require(script.RagdollUtils)

local function IsBlacklisted(Character : Model) : boolean
	local isBlacklisted = false

	for _, tag in RagdollBlacklistTags do
		if not CS:HasTag(Character, tag) then continue end

		isBlacklisted = true
		break
	end

	return isBlacklisted
end

local function IsPlayer(Character : Model) : boolean
	local isPlayer = false

	if Players:GetPlayerFromCharacter(Character) then
		isPlayer = true
	end

	return isPlayer
end

local function getRigType(Character : Model) : string
	local rigType = "R15"

	if Character:FindFirstChild("Torso") then
		rigType = "R6"
	end

	return rigType
end

function Service.Ragdoll(Character : Model, Duration : number?)
	if not CS:HasTag(Character, "ragdollInit") then return end
	if IsBlacklisted(Character) then return end
	if Duration and typeof(Duration) ~= 'number' then 
        Duration = nil 
    end

	local RigType = getRigType(Character)

	if not Duration then
		if not RagdollStorage[Character] then
			RagdollStorage[Character] = {}
		else
			if RagdollStorage[Character].DurationTask then
				task.defer(task.cancel, RagdollStorage[Character].DurationTask)
			end
		end
	else
		if not RagdollStorage[Character] then
			RagdollStorage[Character] = {}
			RagdollStorage[Character].DurationTask = task.delay(Duration, function()
				if not RagdollStorage[Character] then return end

				Service.Unragdoll(Character)
			end)
		else
			if RagdollStorage[Character].DurationTask then
				task.defer(task.cancel, RagdollStorage[Character].DurationTask)
			end
			
			RagdollStorage[Character].DurationTask = task.delay(Duration, function()
				if not RagdollStorage[Character] then return end

				Service.Unragdoll(Character)
			end)
		end
	end

	task.spawn(function()
		--if not isPlayer(Character) then
		local Hum : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
		if Hum then
			Hum:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
			Hum:ChangeState(Enum.HumanoidStateType.Ragdoll)
			Hum.PlatformStand = true

			local Animator : Animator? = Hum:FindFirstChildWhichIsA("Animator")
			if Animator then
				for _, anim : AnimationTrack in Animator:GetPlayingAnimationTracks() do
					anim:Stop(0)
				end
			end
		end
		
		if not IsPlayer(Character) then
			local Animate : Script? = Character:FindFirstChild("Animate")
			if Animate then
				Animate.Enabled = false
			end
		end
	end)

	Character:AddTag("Ragdoll");
	RagdollUtils[RigType].Ragdoll(Character)
end

function Service.Unragdoll(Character : Model)
	if not RagdollStorage[Character] then return end
	if RagdollStorage[Character].DurationTask then 
        task.defer(task.cancel, RagdollStorage[Character].DurationTask) 
    end
	RagdollStorage[Character] = nil

	local RigType = getRigType(Character)

	task.spawn(function()
		local Hum : Humanoid? = Character:FindFirstChildWhichIsA("Humanoid")
		if Hum then
			Hum:SetStateEnabled(Enum.HumanoidStateType.GettingUp, true)
			Hum:ChangeState(Enum.HumanoidStateType.GettingUp)
			Hum.PlatformStand = false
		end
	end)

	Character:RemoveTag("Ragdoll");
	RagdollUtils[RigType].Unragdoll(Character)
end

local clientClone = script:WaitForChild("RagdollClient"):Clone()
clientClone.Parent = SCS
clientClone.Enabled = true

local function initCharacter(Character : Model, isNPC : boolean?)
	local Humanoid : Humanoid? = Character:FindFirstChildOfClass("Humanoid")
	if not Humanoid then return end

	Humanoid.BreakJointsOnDeath = false
	Humanoid.RequiresNeck = false

	if getRigType(Character) == "R15" then
		RagdollUtils.R15.BuildRagdoll(Character)
	end

	if not Character:FindFirstChild("RagdollClient") and not isNPC then
		local RagdollClient : LocalScript = script:WaitForChild("RagdollClient"):Clone()
		RagdollClient.Parent = Character
		RagdollClient.Enabled = true
	end

	Character.Destroying:Once(function()
		if not RagdollStorage[Character] then return end
		if RagdollStorage[Character].DurationTask then 
            task.defer(task.cancel, RagdollStorage[Character].DurationTask) 
        end
		
		RagdollStorage[Character] = nil
	end)

	CS:AddTag(Character, "ragdollInit")
end

Service.InitializeRagdollService = function()
    Players.PlayerAdded:Connect(function(Player : Player)
        local Character : Model? = Player.Character
        if Character then
            initCharacter(Character)
        end
    
        Player.CharacterAdded:Connect(function(newCharacter : Model)
            initCharacter(newCharacter)
        end)
    end)
    
    Players.PlayerRemoving:Connect(function(Player : Player)
        local PlayerName = Player.Name
    
        task.spawn(function()
            for Character : Model, _ in pairs(RagdollStorage) do
                if Character.Name ~= PlayerName then continue end
                RagdollStorage[Character] = nil
            end
        end)
    end)
    
    -- CS:GetInstanceAddedSignal("Ragdoll"):Connect(function(Character : Model)
    --     if not Character or not Character:IsA("Model") then return end
    --     if RagdollStorage[Character] then return end
    
    --     Service.Ragdoll(Character)
    -- end)
    
    -- CS:GetInstanceRemovedSignal("Ragdoll"):Connect(function(Character : Model)
    --     if not Character or not Character:IsA("Model") then return end
    --     if not RagdollStorage[Character] then return end
    
    --     Service.Unragdoll(Character)
    -- end)
    
    for _, NPC : Model in pairs(CS:GetTagged("NPC")) do
        if not NPC or not NPC:IsA("Model") or not NPC:FindFirstChild("Humanoid") then continue end
        if CS:HasTag(NPC, "ragdollInit") then continue end
    
        initCharacter(NPC, true)
    end
    
    CS:GetInstanceAddedSignal("NPC"):Connect(function(NPC : Model)
        if not NPC or not NPC:IsA("Model") or not NPC:FindFirstChild("Humanoid") then return end
        if CS:HasTag(NPC, "ragdollInit") then return end
    
        initCharacter(NPC, true)
    end)

    Service.Init = function()
        warn("Service already initialized.")
    end
end

return Service