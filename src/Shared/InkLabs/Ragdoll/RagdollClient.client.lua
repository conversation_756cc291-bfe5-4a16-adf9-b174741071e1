-- SERVICES --
local CS = game:GetService("CollectionService")

-- MAIN --
local Character: Model = script.Parent
if not Character or not Character:Is<PERSON>("Model") then return end
local Humanoid : Humanoid = Character:WaitFor<PERSON>hild("Humanoid")
local Animate : LocalScript = Character:WaitForChild("Animate")

local Tag = "Ragdoll"

CS:GetInstanceAddedSignal(Tag):Connect(function(x: Model)
    if (x ~= Character) then return end
	if not CS:HasTag(Character, Tag) then return end
	if not Humanoid then return end

	if Animate then
		Animate.Enabled = false
	end
	
	Humanoid.PlatformStand = true

	Humanoid:ChangeState(Enum.HumanoidStateType.Ragdoll)
	Humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, false)
end)

CS:GetInstanceRemovedSignal(Tag):Connect(function(x: Model)
    if (x ~= Character) then return end
	if CS:HasTag(Character, Tag) then return end
	if not Humanoid:GetStateEnabled(Enum.HumanoidStateType.Ragdoll) then return end

	if Animate then
		Animate.Enabled = true
	end
	Humanoid.PlatformStand = false

	Humanoid:SetStateEnabled(Enum.HumanoidStateType.GettingUp, true)
	Humanoid:ChangeState(Enum.HumanoidStateType.GettingUp)
end)