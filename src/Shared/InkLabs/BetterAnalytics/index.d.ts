declare class BetterAnalytics {
    LogEconomyEvent(
        player: Player,
        flowType: string,
        currencyType: string,
        amount: number,
        endingBalance: number,
        transactionType: string,
        itemSku: string,
        customFields: unknown
    ): void;

    LogFunnelStepEvent(
        player: Player,
        funnelName: string,
        funnelSessionId: string,
        step: number,
        stepName: string,
        customFields: unknown
    ): void;

    LogOnboardingFunnelStepEvent(
        player: Player,
        step: number,
        stepName: string,
        customFields: unknown
    ): void;
}

export = BetterAnalytics;