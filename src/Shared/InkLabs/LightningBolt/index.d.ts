interface FakeAttachment {
    WorldPosition: Vector3;
    WorldAxis: Vector3;
}

declare class LightningBolt {
    Enabled: boolean;
    Attachment0: FakeAttachment;
    Attachment1: FakeAttachment;
    MinRadius: number;
    MaxRadius: number;
    Frequency: number;
    AnimationSpeed: number;
    Thickness: number;
    MinThicknessMultiplier: number;
    MaxThicknessMultiplier: number;
    MinTransparency: number;
    MaxTransparency: number;
    PulseSpeed: number;
    PulseLength: number;
    FadeLength: number;
    ContractFrom: number;
    Color: Color3 | ColorSequence;
    ColorOffsetSpeed: number;
    CurveSize0: number;
    CurveSize1: number;

    constructor(attachment0: FakeAttachment | Attachment, attachment1: FakeAttachment | Attachment, partCount: number);

    Destroy(this: LightningBolt): void;

    DestroyDissipate(this: LightningBolt, TimeLength?: number, Strength?: number): void;
}

export = LightningBolt;