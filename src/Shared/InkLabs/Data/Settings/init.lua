local AddonTypes = require(script.AddonTypes)

--// This is your data structure, you should change it to fit your game needs.
--// This is the same as a SaveStructure in ProfileService.
local STRUCTURE = {
	Level = 1,
	EXP = 0,
	MaxEXP = 100,
	
	Coins = 0,
	Rebirths = 0,
	Spins = 5,
	
	Skills = {},
	Books = {},
	
	Passes = {},
	Boosters = {},

	Obbies = {},
	
	LastLogin = -1,
	LoginStreak = 0,
	BanInfo = {
		IsBanned = false,
		Reason = "",
		Expires = -1
	},
}
--// ─────────────────────────────────────────────────────────────── //--

--// Changing the version will change the game's datastore scope, so be aware.
--// tl;dr it will cause data loss.
local DATA_VERSION: number = 1

--// This makes so that the datastore used in Studio is different than the one in the actual game.
local DEVELOPMENT_ENVIRONMENT: boolean = (game:GetService('RunService'):IsStudio() and true) or false

--// Whitelist makes so that it only replicates the data inside REPLICATION_FILTER.
--// Blacklist makes so that it replicates all player data, but the ones inside REPLICATION_FILTER.
local FILTER_TYPE: "Blacklist" | "Whitelist" = "Blacklist"

--// Array of strings, put the name of the Data that you want to filter.
local FILTER_LIST = {}

--// "Light": Each player's data is shared among all players in the game.
--// "Heavy": Only the player who owns the data will have access to it.
local FILTER_STRICTNESS: "Light" | "Heavy" = "Heavy"

--// Dictionary of addons, it might not work very well yet, so keep it empty for now.
--// You can see more about it in the official documentation: 
--// https://docs.inkrnl.com/projects/profilesync/addons
export type TDataSettings = {
	Name: string,
	InDevelopmentEnv: boolean,
	Version: number,
	Structure: {[string]: any},
	FilterType: "Blacklist" | "Whitelist",
	Filter: {string},
	FilterStrictness: "Light" | "Heavy",
	Debug: boolean,
	LoadedTag: string,
	InitializedTag: string
}

export type TSettingsParams = {
	Version: number?,
	Structure: {[string]: any}?,
	FilterType: "Blacklist" | "Whitelist" | nil,
	Filter: {string}?,
	FilterStrictness: "Light" | "Heavy" | nil,
	Debug: boolean?
}

return {
	["Name"] = "PlayerData_".. DATA_VERSION .. "_" .. ((DEVELOPMENT_ENVIRONMENT and "DEV") or "PRODUCTION"),
	["InDevelopmentEnv"] = DEVELOPMENT_ENVIRONMENT,
	["Version"] = DATA_VERSION,
	["Structure"] = STRUCTURE,
	["FilterType"] = FILTER_TYPE,
	["Filter"] = FILTER_LIST,
	["FilterStrictness"] = FILTER_STRICTNESS,
	["Debug"] = true,
	["LoadedTag"] = "DataLoaded",
	["InitializedTag"] = "DataInitialized"
} :: TDataSettings