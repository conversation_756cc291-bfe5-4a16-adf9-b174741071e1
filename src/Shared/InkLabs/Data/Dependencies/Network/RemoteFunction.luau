-- SERVICES --
local RunService = game:GetService("RunService")

-- DEPENDENCIES --
local Warp = require(script.Parent:WaitForChild('Dependencies'):WaitForChild('Warp'))

-- TYPES --
type rateLimitArg = {
	maxEntrance: number?,
	interval: number?,
}

local RemoteFunction = {}
RemoteFunction.__index = RemoteFunction

function RemoteFunction.new(Identifier: string, RateLimit: rateLimitArg?)
    local self = setmetatable({}, RemoteFunction)
	local Event = RunService:IsClient() and Warp.Client(Identifier) or Warp.Server(Identifier, RateLimit) or nil

	function self:InvokeServer(...)
		if not RunService:IsClient() then return end
		return Event:Invoke(30, ...)
	end
	
	function self:InvokeClient(Player : Player, ...)
		if not RunService:IsServer() then return end
		return Event:Invoke(5, Player, ...)
	end
	
	self.OnInvoke = Event
	return self
end

return RemoteFunction