--!nocheck
local RunService = game:GetService('RunService')
local isClient = RunService:IsClient()

do
	if isClient then 
		script.DataService:Destroy() 
	end
end

type Error = {
	error: string,
	trace: string?,
	context: string?,
	kind: string,
	parent: Error?,
	createdTick: number,
	createdTrace: string,
}

type Executor<T> = (resolve: (result: T) -> (), reject: (reason: any) -> (), onCancel: ((cancellationHook: () -> ()) -> boolean)?) -> ()
type ResolveFunction<T> = (result: T) -> ()
type RejectFunction = (reason: any) -> ()
type Consumer<T> = Promise<T>
type FinallyHandler = (status: Status) -> ()
type CancellationHook = () -> ()

type Promise<T> = {
	Status: Status,
	Error: Error,

	andThen: <U>(self: Promise<T>, successHandler: (value: T) -> U) -> Promise<T>,
	catch: (self: Promise<T>, failureHandler: (reason: any) -> any) -> Promise<T>,
	finally: (self: Promise<T>, finallyHandler: FinallyHandler) -> Promise<T>,
	getStatus: (self: Promise<T>) -> Status,
	cancel: (self: Promise<T>) -> (),
	await: (self: Promise<T>) -> (boolean, T),
	expect: (self: Promise<T>) -> T,
}

type ProfileData = {[string]: any}

type TSettingsParams = {
	Version: number?,
	Structure: {[string]: any}?,
	FilterType: "Blacklist" | "Whitelist" | nil,
	Filter: {string}?,
	FilterStrictness: "Light" | "Heavy" | nil,
	Debug: boolean?,
	Addons: {Client: {[string]: any}?, Server: {[string]: any}?}?
}

type ProfileSync = {
	GetProfile: (self: ProfileSync, player: Player | number, timeOut: number?, offline: boolean?) -> ProfileData?,
	GetProfileAsync: (self: ProfileSync, player: Player | number, timeOut: number?, offline: boolean?) -> Promise<ProfileData>,
	GetData: <T>(self: ProfileSync, player: Player, dataName: string, timeOut: number?) -> T?,
	GetDataAsync: <T>(self: ProfileSync, player: Player, dataName: string, timeOut: number?) -> Promise<T>,
	
	Add: (self: ProfileSync, player: Player, dataName: string,  amount: number) -> number?,
	Sub: (self: ProfileSync, player: Player, dataName: string, amount: number) -> number?,
	Set: (self: ProfileSync, player: Player, dataName: string, value: any) -> any?,
	ArrayInsert: (self: ProfileSync, player: Player, dataName: string, value: any) -> {any}?,
	ArrayRemove: (self: ProfileSync, player: Player, dataName: string, index: number) -> {any}?,
	ArraySet: (self: ProfileSync, player: Player, dataName: string, value: any) -> {any}?,
	DictionaryInsert: (self: ProfileSync, player: Player, dataName: string, value: any, index: string | number) -> {any}?,
	DictionaryRemove: (self: ProfileSync, player: Player, dataName: string, index: string | number) -> {any}?,
	Bind: (self: ProfileSync, player: Player, dataName: string, callback: (any) -> ()) -> (),
	BindAll: (self: ProfileSync, player: Player, callback: (any) -> ()) -> (),
	Init: (self: ProfileSync, settings: TSettingsParams) -> nil,
}

local ProfileSync: ProfileSync = isClient and require(script.DataController) or require(script.DataService)
return ProfileSync