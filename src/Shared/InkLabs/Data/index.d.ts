import {
  IPlayerData,
  TPlayerDataField,
  TPlayerDataValue,
} from "Shared/Data/Abstractions/IPlayerData";

type TSettingsParams = {
  Version?: number;
  Structure?: unknown;
  FilterType?: "Blacklist" | "Whitelist";
  Filter?: TPlayerDataField[];
  FilterStrictness?: "Light" | "Heavy";
  Debug?: boolean;
  Addons?: {
    Client?: {
      Leaderstats?: boolean;
      Attributes?: boolean;
    };
    Server?: {
      Boosters?: boolean;
    };
  };
};

interface IProfileSync {
  GetProfile(
    player: Player | number,
    timeOut?: number,
    offline?: boolean
  ): IPlayerData | undefined;

  GetProfileAsync(
    player: Player | number,
    timeOut?: number,
    offline?: boolean
  ): Promise<IPlayerData | undefined>;

  GetData<T extends TPlayerDataValue<U>, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    timeOut?: number
  ): T | undefined;

  GetDataAsync<T extends TPlayerDataValue<U>, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    timeOut?: number
  ): Promise<T | undefined>;

  Add<T extends TPlayerDataValue<U> & number, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    amount: number
  ): T | undefined;

  Sub<T extends TPlayerDataValue<U> & number, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    amount: number
  ): T | undefined;

  Set<T extends TPlayerDataValue<U>, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    value: T
  ): T | undefined;

  ArrayInsert<
    T extends TPlayerDataValue<U> & K[],
    K,
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    value: K
  ): T | undefined;

  ArrayRemove<
    T extends TPlayerDataValue<U> & unknown[],
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    index: number
  ): T | undefined;

  ArrayRemoveByValue<
    T extends TPlayerDataValue<U> & K[],
    K,
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    value: K
  ): T | undefined;

  ArraySet<
    T extends TPlayerDataValue<U> & unknown[],
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    value: T
  ): T | undefined;

  DictionaryInsert<
    T extends TPlayerDataValue<U> & {
      [key: string | number]: K;
    },
    K,
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    value: K,
    index: string | number
  ): T | undefined;

  DictionaryRemove<
    T extends TPlayerDataValue<U> & {
      [key: string | number]: K;
    },
    K,
    U extends TPlayerDataField
  >(
    player: Player,
    dataName: U,
    index: string | number
  ): T | undefined;

  Bind<T extends TPlayerDataValue<U>, U extends TPlayerDataField>(
    player: Player,
    dataName: U,
    callback: (value: T) => void
  ): () => void;

  BindAll<T extends TPlayerDataValue<U>, U extends TPlayerDataField>(
    player: Player,
    callback: (value: T, dataName: U) => void
  ): () => void;

  Init(settings?: TSettingsParams): IProfileSync;
}

declare const ProfileSync: IProfileSync;

export = ProfileSync;
