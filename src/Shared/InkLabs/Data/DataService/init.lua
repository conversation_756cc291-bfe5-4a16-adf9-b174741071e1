--!strict
-- SERVICES --
local Players = game:GetService "Players"

-- DEPENDENCIES --
local Libraries = require(script.Parent:WaitForChild "Dependencies")
local Network = Libraries.Network
local Promise = Libraries.Promise
local SuphiDataStore = require(script.SuphiDataStore)

-- NETWORK --
local DataReplicator = Network.RemoteEvent.new "DataReplicator"
local DataRequester = Network.RemoteFunction.new "DataRequester"

-- SETTINGS --
local _settings: {
    Name: string,
    InDevelopmentEnv: boolean,
    Version: number,
    Structure: { [string]: any },
    FilterType: "Blacklist" | "Whitelist",
    Filter: { string },
    FilterStrictness: "Light" | "Heavy",
    Debug: boolean,
    LoadedTag: string,
    InitializedTag: string,
    Addons: { [string]: any },
} =
    require(script.Parent.Settings)

local _dataStructure = _settings.Structure
local _dataVersion = _settings.Version
local _debug: boolean = _settings.Debug or false
local _loadedTag: string = _settings.LoadedTag or "DataLoaded" --// Player data has been loaded on Server
local _initializedTag: string = _settings.InitializedTag or "DataInitialized" --// Data service has been initialized
local _filterStrictness: "Light" | "Heavy" = _settings.FilterStrictness or "Heavy"

local _filter: { string } = _settings.Filter
local _filterType: "Blacklist" | "Whitelist" = _settings.FilterType

-- TYPES --
type IProfileData = { [string]: any }
type IProfile = { [string]: SuphiDataStore.DataStore }
type IBinds = { any }
type IPlayerBindings = { [string]: IBinds }

-- INTERNAL --
local _DEFAULT_TIMEOUT = 3
local _profilesBeingUpdated = {}

local Profiles: { IProfile } = {}
local Bindings: { [number]: IPlayerBindings } = {}
local Service = {}

-- INTERNAL FUNCTIONS --
local function GetPlayerDataStore(userId: number)
    local dataStore = SuphiDataStore.new(
        _settings.Name .. "_" .. tostring(_dataVersion),
        tostring(userId),
        ((_settings.InDevelopmentEnv and "DEV") or "PRODUCTION")
    )

    return dataStore
end

local function IsPlayerValid(player: Player?): boolean
    if
        not player
        or typeof(player) ~= "number" and typeof(player) ~= "Instance" and typeof(player) ~= "string"
        or typeof(player) == "number" and not Players:GetPlayerByUserId(player)
        or typeof(player) == "string" and not (tonumber(player) and Players:GetPlayerByUserId(
            tonumber(player)
        ))
        or not player:IsDescendantOf(Players)
    then
        return false
    end

    return true
end

local function ProcessPlayerBindings(player: Player, dataName: string, value: any)
    -- Handle bind
    local playerBindings: IPlayerBindings = Bindings[player.UserId]
    if playerBindings and playerBindings[dataName] then
        for _, callback: any in playerBindings[dataName] do
            task.spawn(function()
                callback(value)
            end)
        end
    end

    -- Handle bind all
    if playerBindings and playerBindings["ALL_DATA"] then
        for _, callback: any in playerBindings["ALL_DATA"] do
            task.spawn(function()
                callback(value, dataName)
            end)
        end
    end
end

local function ShouldReplicate(dataName: string): boolean
    return ((not _filter or #_filter < 1) and true)
        or (_filterType == "Blacklist" and table.find(_filter, dataName) and false)
        or (_filterType == "Whitelist" and not table.find(_filter, dataName) and false)
        or true
end

local function GetFilteredProfileData(data: IProfileData): IProfileData
    local newData = {}
    for dataName, value in data do
        if not ShouldReplicate(dataName) then continue end
        newData[dataName] = value
    end

    return newData
end

local function ReplicateSingleDataToClient(
    replicateTo: Player | "All",
    replicateFrom: Player,
    dataName: string,
    value: any
)
    if not IsPlayerValid(replicateTo) and replicateTo ~= "All" then return end
    if not IsPlayerValid(replicateFrom) then return end
    if not dataName or typeof(dataName) ~= "string" then return end
    if not ShouldReplicate(dataName) then return end

    if replicateTo == "All" then
        DataReplicator:FireAllClients(replicateFrom, dataName, value)
    else
        DataReplicator:FireClient(replicateTo, replicateFrom, dataName, value)
    end
end

local function GetDataStoreProfile(userId: number, offline: boolean?)
    return Promise.new(function(resolve, reject, onCancel)
        if Profiles[userId] then
            resolve(Profiles[userId])
            return
        end

        _profilesBeingUpdated[userId] = true
        local profile
        onCancel(function()
            _profilesBeingUpdated[userId] = nil
            if not Profiles[userId] and profile ~= nil then profile:Destroy() end
        end)

        profile = GetPlayerDataStore(userId)
        local result = profile:Open(_dataStructure)

        local player = Players:GetPlayerByUserId(userId)
        if result ~= SuphiDataStore.Response.Success then
            if player then
                player:Kick "Data could not be loaded, try again shortly. If the issue persists, please contact the support!"
            end

            _profilesBeingUpdated[userId] = nil
            reject(`Data for [{userId}] failed to load, might be related to roblox servers.`)
            return
        end

        profile:Reconcile(_dataStructure)
        _profilesBeingUpdated[userId] = nil
        if not offline then
            if player and player:IsDescendantOf(Players) then -- In case the player left before getting to this stage, so we check to make sure.
                Profiles[userId] = profile
                player:AddTag(_loadedTag)
            else -- If he left, we release his profile.
                profile:Destroy()
                reject(`Player [{userId}] is not longer in game.`)
            end
        end

        resolve(profile)
    end)
end

-- EDIT METHODS --
function Service:Add(player: Player, dataName: string, value: number): number?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if not value or typeof(value) ~= "number" or value < 0 then
        warn "[NUMERIC]: Value must be a NUMBER and OVER or EQUAL to 0!"
        return
    end
    if Profiles[player.UserId].Value[dataName] == nil then
        warn(
            "[GENERAL]: Invalid DataName: [",
            dataName,
            "], it does not exist on the player data!"
        )
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "number" then
        warn("[NUMERIC]: Invalid DataName Type: [", dataName, "], it is not a number!")
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName] += value
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [Add] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:Sub(player: Player, dataName: string, value: number): number?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if not value or typeof(value) ~= "number" or value < 0 then
        warn "[NUMERIC]: Value must be a NUMBER and OVER or EQUAL to 0!"
        return
    end
    if Profiles[player.UserId].Value[dataName] == nil then
        warn(
            "[GENERAL]: Invalid DataName: [",
            dataName,
            "], it does not exist on the player data!"
        )
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "number" then
        warn("[NUMERIC]: Invalid DataName Type: [", dataName, "], it is not a number!")
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName] -= value
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [Sub] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:Set(player: Player, dataName: string, value: any): any
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName] = value
    end)

    if success then
        ReplicateSingleDataToClient("All", player, dataName, value)
        ProcessPlayerBindings(player, dataName, value)
    else
        warn("[FATAL ERROR]: Failed during [Set] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:ArrayInsert(player: Player, dataName: string, value: any): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[ARRAY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end

    local success, err = pcall(function()
        table.insert(Profiles[player.UserId].Value[dataName], value)
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [ArrayInsert] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:ArrayRemove(player: Player, dataName: string, index: number): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[ARRAY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end

    local success, err = pcall(function()
        table.remove(Profiles[player.UserId].Value[dataName], index)
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [ArrayRemove] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:ArrayRemoveByValue(player: Player, dataName: string, value: any): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[ARRAY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end

    local success, err = pcall(function()
        local index = table.find(Profiles[player.UserId].Value[dataName], value)
        table.remove(Profiles[player.UserId].Value[dataName], index)
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [ArrayRemove] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:ArraySet(player: Player, dataName: string, value: any): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[ARRAY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName] = value
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [ArraySet] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:DictionaryInsert(
    player: Player,
    dataName: string,
    value: any,
    index: string | number
): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[DICTIONARY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end
    if not index or (typeof(index) ~= "number" and typeof(index) ~= "string") then
        warn "[DICTIONARY] Invalid Parameter: [INDEX] MUST be a STRING or NUMBER!"
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName][index] = value
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Value[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [DictionaryInsert] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

function Service:DictionaryRemove(
    player: Player,
    dataName: string,
    index: string | number
): { any }?
    if not IsPlayerValid(player) then
        warn("[GENERAL] Player: [", player, "] is no longer in game.")
        return
    end
    if not Profiles[player.UserId] then
        warn(
            "[GENERAL] Player: [",
            player,
            "] has not been initialized yet, do not try to change his data before it is loaded!"
        )
        return
    end
    if not dataName or typeof(dataName) ~= "string" then
        warn "[GENERAL]: DataName must be a STRING and NOT NIL!"
        return
    end
    if typeof(Profiles[player.UserId].Value[dataName]) ~= "table" then
        warn("[DICTIONARY] Invalid Data Type: [", dataName, "] is not a table!")
        return
    end
    if not index or (typeof(index) ~= "number" and typeof(index) ~= "string") then
        warn "[DICTIONARY] Invalid Parameter: [INDEX] MUST be a STRING or NUMBER!"
        return
    end

    local success, err = pcall(function()
        Profiles[player.UserId].Value[dataName][index] = nil
    end)

    if success then
        ReplicateSingleDataToClient(
            "All",
            player,
            dataName,
            Profiles[player.UserId].Data[dataName]
        )
        ProcessPlayerBindings(player, dataName, Profiles[player.UserId].Value[dataName])
    else
        warn("[FATAL ERROR]: Failed during [DictionaryRemove] -- Error:", err)
    end

    return Profiles[player.UserId].Value[dataName]
end

-- FETCH METHODS --
function Service:GetProfile(
    player: Player | number,
    timeOut: number?,
    offline: boolean?
): IProfileData?
    local userId = typeof(player) == "number" and player or player.UserId
    local profile: IProfile? = Profiles[userId]
    if profile and profile.Value then return profile.Value end

    if not _profilesBeingUpdated[userId] and offline then
        local serverProfile: IProfile? = GetDataStoreProfile(userId, true):expect()
        if not serverProfile or not serverProfile.Value then
            return nil, `Failed to load [{userId}]'s profile from a remote server!`
        end

        return serverProfile.Value
    end

    local t = os.clock()

    repeat
        profile = Profiles[userId]
        task.wait()
    until (profile and profile.Value)
        or not IsPlayerValid(player)
        or (os.clock() - t > (timeOut or _DEFAULT_TIMEOUT))

    if not IsPlayerValid(player) then return nil, `Player is no longer in game!` end

    if os.clock() - t > (timeOut or _DEFAULT_TIMEOUT) then
        return nil,
            `Data could not be loaded in the given time window [{timeOut or _DEFAULT_TIMEOUT}]!`
    end

    return Profiles[userId].Value
end

function Service:GetProfileAsync(player: Player | number, timeOut: number?, offline: boolean?)
    local userId = typeof(player) == "number" and player or player.UserId
    local profile: IProfile? = Profiles[userId]
    if profile and profile.Value then return Promise.resolve(profile.Value) end

    if not _profilesBeingUpdated[userId] and offline then
        return GetDataStoreProfile(userId, offline):andThen(function(newProfile: IProfile)
            if not profile or not profile.Value then
                return Promise.reject(
                    `Failed to load [{userId}]'s profile from a remote server!`
                )
            end
            return Promise.resolve(newProfile.Value)
        end)
    end

    return Promise.new(function(resolve, reject)
        local t = os.clock()

        repeat
            profile = Profiles[userId]
            task.wait()
        until (profile and profile.Value)
            or not IsPlayerValid(player)
            or (os.clock() - t > (timeOut or _DEFAULT_TIMEOUT))

        if not IsPlayerValid(player) then
            reject(`Player is no longer in game.`)
            return
        end

        if os.clock() - t > (timeOut or _DEFAULT_TIMEOUT) then
            reject(`Profile took longer than {timeOut or _DEFAULT_TIMEOUT} to load`)
            return
        end

        resolve(Profiles[userId] and Profiles[userId].Value)
    end)
end

function Service:GetData<T>(player: Player, dataName: string, timeOut: number?): T?
    local profile = Profiles[player.UserId]

    -- Profile was found, so we return it
    if profile ~= nil then return profile.Value and profile.Value[dataName] end

    -- Wait for profile
    profile = Service:GetProfile(player, timeOut)
    return profile and profile.Value and profile.Value[dataName]
end

function Service:GetDataAsync<T>(player: Player, dataName: string, timeOut: number?)
    local profile = Profiles[player.UserId]
    if profile and profile.Value then return Promise.resolve(profile.Value[dataName]) end

    return Promise.new(function(resolve, reject)
        profile = Service:GetProfileAsync(player, timeOut):expect()

        if profile and profile.Value and profile.Value[dataName] ~= nil then
            resolve(profile.Value[dataName])
        else
            reject(`Data [{dataName}] of player [{player}] could not be loaded / found!`)
        end
    end)
end

-- LISTENER METHODS --
function Service:Bind(player: Player, dataName: string, callback: (any) -> ()): ()
    if not IsPlayerValid(player) then
        warn("Player: [", player, "] is not valid!")
        return
    end
    if not dataName or typeof(dataName) ~= "string" or dataName == "ALL_DATA" then
        error "DataName must be a STRING!"
        return
    end
    if not callback or typeof(callback) ~= "function" then
        warn "Callback must be a FUNCTION!"
        return
    end

    if not Bindings[player.UserId] then Bindings[player.UserId] = {} end
    if not Bindings[player.UserId][dataName] then Bindings[player.UserId][dataName] = {} end

    table.insert(Bindings[player.UserId][dataName], callback)
    warn("Bound to", dataName)
    return function()
        table.remove(Bindings[player.UserId][dataName], table.find(Bindings[player.UserId][dataName], callback))
        warn("Unbound from", dataName)
    end
end

function Service:BindAll(player: Player, callback: (any) -> ()): ()
    if not IsPlayerValid(player) then
        warn("Player: [", player, "] is not valid!")
        return
    end
    if not callback or typeof(callback) ~= "function" then
        warn "Callback must be a FUNCTION!"
        return
    end

    if not Bindings[player.UserId] then Bindings[player.UserId] = {} end

    if not Bindings[player.UserId]["ALL_DATA"] then
        Bindings[player.UserId]["ALL_DATA"] = {}
    end

    table.insert(Bindings[player.UserId]["ALL_DATA"], callback)
    warn("Bound to ALL_DATA")
    return function()
        table.remove(Bindings[player.UserId]["ALL_DATA"], table.find(Bindings[player.UserId]["ALL_DATA"], callback))
        warn("Unbound from ALL_DATA")
    end
end

-- RELEASE PROFILE
function Service.ReleaseProfileAsync(userId: number)
    if not Profiles[userId] then return end

    Profiles[userId]:Destroy()
end

-- INIT --
type TSettingsParams = {
    Version: number?,
    Structure: { [string]: any }?,
    FilterType: "Blacklist" | "Whitelist" | nil,
    Filter: { string }?,
    FilterStrictness: "Light" | "Heavy" | nil,
    Debug: boolean?,
    Addons: { Client: { [string]: any }?, Server: { [string]: any }? }?,
}

function Service:Init(settings: TSettingsParams?)
    if settings then
        for i, v in settings do
            _settings[i] = v
        end

        _dataStructure = _settings.Structure
        _dataVersion = _settings.Version
        _filter = _settings.Filter
        _filterType = _settings.FilterType
        _filterStrictness = _settings.FilterStrictness
        _debug = _settings.Debug
        _settings.Name = "PlayerData"
    end

    local function initPlayer(player: Player)
        local t = os.clock()

        local loadPromise = GetDataStoreProfile(player.UserId)
            :andThen(function(profile: IProfile)
                if _debug then
                    warn(
                        player,
                        " | Data Loaded - Load Time: ",
                        string.sub(tostring(os.clock() - t), 1, 6),
                        profile.Value
                    )
                    -- Profiles[player.UserId] = profile
                    warn(`Inserted profile for {player.Name}`)
                    warn(Profiles)
                end
            end)
            :catch(warn)

        player.AncestryChanged:Once(function()
            loadPromise:cancel()
            _profilesBeingUpdated[player.UserId] = nil
            if Profiles[player.UserId] then
                Profiles[player.UserId]:Destroy()
                Profiles[player.UserId] = nil
            end

            if Bindings[player.UserId] then
                for dataName, _: { any } in Bindings[player.UserId] do
                    Bindings[player.UserId][dataName] = nil
                end

                Bindings[player.UserId] = nil
            end
        end)
    end

    for _, player: Player in Players:GetPlayers() do
        initPlayer(player)
    end

    Players.PlayerAdded:Connect(function(player: Player)
        initPlayer(player)
    end)

    workspace:AddTag(_initializedTag)
    DataRequester.OnInvoke:Connect(function(requester: Player, action: string, ...)
        local args = { ... }

        if action == "GetProfile" then
            local player: Player? = args[1]
            if not IsPlayerValid(player) then return end
            if _filterStrictness == "Heavy" and player.UserId ~= requester.UserId then
                return
            end

            local profile = Service:GetProfile(player, 5)
            return profile and GetFilteredProfileData(profile)
        end

        return
    end)

    Service.Init = function()
        warn "[WARNING]: Data service has already been initialized!"
        return Service
    end

    return Service
end

return Service