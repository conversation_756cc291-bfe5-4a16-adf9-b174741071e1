local _createdCaches = {}

export type Cache<K, V> = {
    Insert: (self: Cache<K, V>, key: K, value: V) -> Cache<K, V>,
    Remove: (self: Cache<K, V>, key: K) -> Cache<K, V>,
    Has: (self: Cache<K, V>, key: K) -> boolean,
    Get: (self: Cache<K, V>, key: K) -> V?,
    GetAll: (self: Cache<K, V>) -> {[K]: V},
    Destroy: (self: Cache<K, V>) -> ()
}

local Cache = {}
Cache.__type = "Cache"
Cache.__index = Cache

function Cache.new<K, V>(id: string?): Cache<K, V>
    local self = setmetatable({}, Cache)
    self._cache = {} :: {[K]: V}

    function self:Insert(key: K, value: V): Cache<K, V>
        self._cache[key] = value
        return self
    end

    function self:Remove(key: K): Cache<K, V>
        self._cache[key] = nil
        return self
    end

    function self:Has(key: K): boolean
        return self._cache[key] ~= nil
    end

    function self:Get(key: K): V?
        return self._cache[key]
    end

    function self:GetAll(): {[K]: V}
        return self._cache
    end

    function self:Destroy()
        self._cache = nil
        if id then
            _createdCaches[id] = nil
        end
        self = nil
    end

    if id then
        _createdCaches[id] = self
    end

    return self
end

function Cache.GetCacheById<K, V>(id: StringValue): Cache<K, V>?
    return _createdCaches[id.Value]
end

return Cache