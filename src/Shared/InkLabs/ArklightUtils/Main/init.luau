-- SERVICES --
local TS = game:GetService("TweenService")

local BezierModule = require(script.Bezier)
local BetterTween = require(script.BetterTween)

local Util = {}

-- AUXILIARY FUNCTIONS --
local function FilterHumanoid() : {}
	local Filter = {}

	for _, Humanoid : Humanoid in ipairs(workspace:GetDescendants()) do
		if not Humanoid:IsA("Humanoid") then continue end
		table.insert(Filter, Humanoid.Parent)
	end

	return Filter
end

local function CastRay(Origin : Vector3, Direction : Vector3, Filter : {}?) : RaycastResult?
	local Params = RaycastParams.new()
	Params.FilterType = Enum.RaycastFilterType.Include
	Params.IgnoreWater = true
	Params.FilterDescendantsInstances = {workspace:FindFirstChild("Map")}

	return workspace:Raycast(Origin, Direction, Params)
end

local function CastBlock(Origin : CFrame | Vector3, Direction : Vector3, Size : Vector3?, Filter : {}?) : RaycastResult?
	if typeof(Origin) == 'Vector3' then Origin = CFrame.new(Origin) end
	if not Size then Size = Vector3.new(1, 1, 1) end

	local Params = RaycastParams.new()
	Params.FilterType = Enum.RaycastFilterType.Include
	Params.IgnoreWater = true
	Params.FilterDescendantsInstances = {workspace:FindFirstChild("Map")}

	return workspace:Blockcast(Origin, Size,  Direction, Params)
end

local function createPartOnRay(Origin : CFrame | Vector3, RayLength : number?, Properties : {[string] : any}?, Filter : {Instance}?, ExistingPart : Part? ) : Part
	if typeof(Origin) == 'Vector3' then Origin = CFrame.new(Origin) end
	local Part : Part = ExistingPart or nil

	local Raycast = not Part and CastRay(Origin.Position, -Origin.UpVector * (RayLength or 25)) or CastBlock(Origin, -Origin.UpVector * (RayLength or 25), Part and Part.Size)
	if Raycast then
		Part = ExistingPart or Instance.new("Part")

		local BlacklistedMaterials = {
			"Neon", "Glass", "Forcefield"
		}

		Part.Material = not table.find(BlacklistedMaterials, Raycast.Instance.Material.Name) and Raycast.Instance.Material or Part.Material
		Part.Color = not table.find(BlacklistedMaterials, Raycast.Instance.Material.Name) and Raycast.Instance.Color or Part.Color
		Part.Reflectance = not table.find(BlacklistedMaterials, Raycast.Instance.Material.Name) and Raycast.Instance.Reflectance or Part.Reflectance
		Part.Transparency = not table.find(BlacklistedMaterials, Raycast.Instance.Material.Name) and Raycast.Instance.Transparency or Part.Transparency

		if Properties then
			for PropertyName : string, Value : any in pairs(Properties) do
				if Part[PropertyName] == nil then continue end
				Part[PropertyName] = Value
			end
		end

		if not ExistingPart then
			Part.Anchored = true
			Part.CanCollide = false
			Part.CanQuery = false

			Part.Parent = workspace:FindFirstChild("Debris") or workspace
		end
	end

	return Part, Raycast
end

function Util:GetFloorPosition(Origin : Vector3, RayLength : number?, Filter : {Instance}?) : Vector3
	local Params = RaycastParams.new()
	Params.FilterType = Enum.RaycastFilterType.Include
	Params.IgnoreWater = true
	Params.FilterDescendantsInstances = {workspace:FindFirstChild("Map")}

	local Raycast = workspace:Raycast(Origin + Vector3.new(0, 1, 0), Vector3.new(0, -(RayLength or 15), 0), Params)
	if Raycast then
		return Raycast.Position
	else
		return Origin
	end
end

function Util:FormatNumber(Number : number, Type : "Suffix" | "Commas" | "Notation" | nil) : string
	if not Number then return "0" end
	if typeof(Number) ~= 'number' then
		if typeof(Number) == 'string' then Number = tonumber(string.format(Number, "%d")) if not Number then return "0" end else return "0" end
	end

	if Type ~= "Commas" and Type ~= "Suffix" and Type ~= "Notation" then Type = "Suffix" end

	return require(script.FormatNumber)(Number, Type)
end

function Util:GetUpTweenWithRaycast(Character : Model, Distance : number?, Speed : number?, Style : Enum.EasingStyle?, EasingDir : Enum.EasingDirection?) : Tween
	local Tween = nil

	if not Character then error("Character is nil!") return Tween end
	if not Character.PrimaryPart then warn("Character has no PrimaryPart!") return Tween end
	if not Distance or typeof(Distance) ~= 'number' then Distance = 20 end
	if not Speed or typeof(Speed) ~= 'number' then Speed = 100 end
	if not Style or typeof(Style) ~= 'Enum' then Style = Enum.EasingStyle.Sine end
	if not EasingDir or typeof(EasingDir) ~= 'Enum' then EasingDir = Enum.EasingDirection.Out end

	local Params = RaycastParams.new()
	Params.FilterType = Enum.RaycastFilterType.Include
	Params.IgnoreWater = true
	Params.FilterDescendantsInstances = {workspace:FindFirstChild("Map")}

	local CF : CFrame, Size : Vector3 = Character:GetBoundingBox() 

	local Raycast = workspace:Blockcast(Character.PrimaryPart.CFrame, Size, Vector3.new(0, Distance, 0), Params)
	if Raycast then
		warn("hit", Raycast.Position.Y - ((Size.Y / 2) + 2))

		local Time = (Character:GetPivot().Position - Raycast.Position).Magnitude / Speed
		Tween = TS:Create(Character.PrimaryPart, TweenInfo.new(Time, Style, EasingDir), { Position = CF.Position + Vector3.new(0, Raycast.Position.Y - ((Size.Y / 2) + 2), 0)})
	else
		local Time = (Character:GetPivot().Position - Raycast.Position).Magnitude / Speed
		Tween = TS:Create(Character.PrimaryPart, TweenInfo.new(Time, Style, EasingDir), { Position = CF.Position + Vector3.new(0, Distance, 0)})
	end

	return Tween
end

function Util:GetPercent(Number : number, Max : number, Decimal : boolean?)
	if not Number or not Max then warn("ArklightUtil:GetPercent() Error: Number or Max is NIL!") return end

	local Difference = Number - Max
	local Percent = Difference / Max

	if not Decimal then
		return (Percent / 100) * 100
	else
		return Percent / 100
	end
end

function Util:CheckForInstanceType(Object : Instance, Type : string | {string})
	if Object == nil or Type == nil then return end

	if typeof(Type) == 'string' then
		if Object:IsA(Type) then return true end
		return false
	elseif typeof(Type) == 'table' then
		for _, TypeString : string in Type do
			if Object:IsA(TypeString) then return true end
		end

		return false
	end

	return false
end

function Util:RoundNumber(Number : number)
	local roundedNumber = tonumber(string.format("%.2f", Number))
	if roundedNumber == math.round(roundedNumber) then
		return math.round(roundedNumber)
	else
		return roundedNumber
	end
end

function Util:SeparateStrings(String : string)
	local result = ""

	for i = 1, #String do
		local c = String:sub(i, i)
		if c:match("%u") and i > 1 then
			result = result .. " "
		end
		result = result .. c
	end

	return result
end

function Util:ToHMS(Seconds : number)
	local function Format(Int)
		return string.format("%02i", Int)
	end

	local Minutes = (Seconds - Seconds%60)/60
	Seconds = Seconds - Minutes*60
	local Hours = (Minutes - Minutes%60)/60
	Minutes = Minutes - Hours*60

	return Format(Hours)..":"..Format(Minutes)..":"..Format(Seconds)
end

function Util:DynamicHMS(Seconds : number)
	local function Format(Int)
		return string.format("%02i", Int)
	end

	local Minutes = (Seconds - Seconds%60)/60
	Seconds = Seconds - Minutes*60
	local Hours = (Minutes - Minutes%60)/60
	Minutes = Minutes - Hours*60

	if Hours >= 1 then
		return Format(Hours)..":"..Format(Minutes)..":"..Format(Seconds)
	elseif Minutes >= 1 then
		return Format(Minutes)..":"..Format(Seconds)
	else
		return Format(Minutes)..":"..Format(Seconds)
	end
end

function Util:HoursToSeconds(Hours : number)
	if Hours < 0 then
		warn("Utilities.HoursToSeconds: Hours arg must be positive... Returning: 0")
		return 0
	end

	return math.round(Hours * 3600)
end

function Util:MinutesToSeconds(Minutes : number)
	if not Minutes or typeof(Minutes) ~= 'number' then warn("Utilities.MinutesToSeconds: Minutes argument is nil or not a NUMBER.") return 444 end
	return math.round(Minutes * 60)
end

function Util:SecondsToAny(Seconds : number, Type : string?)
	local function Format(Int)
		return string.format("%02i", Int)
	end

	local Minutes = (Seconds - Seconds%60)/60
	local Hours = (Minutes - Minutes%60)/60

	if Type == "Minutes" then
		return Format(Minutes)
	elseif Type == "Hours" then
		return Format(Hours)
	else
		if Seconds >= 3600 then
			return Format(Hours)
		else
			return Format(Minutes)
		end
	end
end

function Util:GenerateCrack(Args : { Origin : Vector3 | CFrame, Orientation : "Horizontal" | "Vertical", Color : {Start : Color3?, End : Color3?}?, Burning : boolean?, Duration : number?, Range : NumberRange?, SegmentSizeXY : Vector2?, AxisRange : number?, Amount : number? })
	if not Args or typeof(Args) ~= 'table' then return end
	if not Args.Origin then return end
	if typeof(Args.Origin) == 'Vector3' then Args.Origin = CFrame.new(Args.Origin) end
	if not Args.Orientation then Args.Orientation = "Horizontal" end
	if not Args.Color then Args.Color = {Start = Color3.new(0, 0, 0), End = Color3.new(0, 0, 0)} end
	if not Args.Duration then Args.Duration = 2 end
	if not Args.Range then Args.Range = NumberRange.new(15, 30) end
	if not Args.SegmentSizeXY then Args.SegmentSizeXY = Vector2.new(1, 0.1) end
	if not Args.AxisRange then Args.AxisRange = 360 end
	if not Args.Amount then Args.Amount = 5 end

	local function createBranch(TargetPosition : Vector3)

		local LastPointPosition : Vector3 = Args.Origin.Position
		local LastTargetEnd : CFrame = Args.Origin

		local function generateBranch()
			local Points = {}
			local Lines = {}

			local BranchDistance = (LastPointPosition - TargetPosition).Magnitude
			local Y_Variation = Args.Orientation == "Vertical" and math.random(-15, 15) or 0
			local X_Variation = Args.Orientation == "Horizontal" and math.random(-15, 15) or 0

			local StartCFrame : CFrame = CFrame.new(LastPointPosition, TargetPosition)
			local EndCFrame : CFrame = StartCFrame * CFrame.new(X_Variation, Y_Variation, -BranchDistance)
			LastTargetEnd = EndCFrame

			local StartPoint = StartCFrame.Position

			local DistanceFromOrigin = (StartPoint - LastTargetEnd.Position).Magnitude
			local MiddlePoint1 = CFrame.new(StartPoint, LastTargetEnd.Position) * CFrame.new(Args.Orientation == "Horizontal" and math.random(-15, 15) * 2 or 0, Args.Orientation == "Vertical" and math.random(-15, 15) * 2 or 0, -DistanceFromOrigin * 0.25)
			local MiddlePoint2 = CFrame.new(StartPoint, LastTargetEnd.Position) * CFrame.new(Args.Orientation == "Horizontal" and math.random(-15, 15) * 2 or 0, Args.Orientation == "Vertical" and math.random(-15, 15) * 2 or 0, -DistanceFromOrigin * 0.75)

			local Curve = BezierModule.new(StartPoint, MiddlePoint1.Position, MiddlePoint2.Position, LastTargetEnd.Position)
			local Segments = 4

			for i = 1, Segments do
				local t = (i - 1) / (Segments - 1)

				local Position = Curve:CalculatePositionAt(t)
				local Derivative = Curve:CalculateDerivativeAt(t)

				table.insert(Points, CFrame.new(Position, Position + Derivative)) 
			end

			for i = 1, #Points do
				if Points[i + 1] then
					local CurrentPos : Vector3 = Points[i].Position
					local FuturePos : Vector3 = Points[i + 1].Position
					local Distance : number = (CurrentPos - FuturePos).Magnitude

					-- CREATING PART --
					local Body = Instance.new("Part")
					Body.Anchored = true
					Body.Size = Vector3.new(Args.SegmentSizeXY.X / i, Args.SegmentSizeXY.Y / i, 0)
					Body.Material = Enum.Material.Neon
					Body.Color = Args.Color.Start or Color3.new(0, 0, 0)
					Body.CFrame = CFrame.new(0.5 * (CurrentPos + FuturePos), FuturePos) --CFrame.lookAt(CurrentPos, FuturePos)

					-- if Args.Burning then
					-- 	local Att = Instance.new("Attachment")
					-- 	Att.Position = Vector3.new(0, Body.Size.Y * 0.55, 0)
					-- 	Att.Parent = Body

					-- 	local pointLight = Instance.new("PointLight")
					-- 	pointLight.Range = 5
					-- 	pointLight.Brightness = 0.3
					-- 	pointLight.Color = Body.Color
					-- 	pointLight.Parent = Att

					-- 	local BurningPart = RS.Assets.BurningPart
					-- 	for _, Emitter in ipairs(BurningPart:GetChildren()) do
					-- 		if not Emitter:IsA("ParticleEmitter") then continue end
					-- 		local FX = Emitter:Clone()
					-- 		FX.Parent = Body
					-- 		FX.Enabled = true
					-- 	end
					-- end

					Body.Parent = workspace:FindFirstChild("Debris") or workspace
					game.Debris:AddItem(Body, Args.Duration + 2)

					-- Size tween
					TS:Create(Body, TweenInfo.new(0.15, Enum.EasingStyle.Exponential), {
						["Size"] = Vector3.new(Body.Size.X, Body.Size.Y, Distance), 
						--["CFrame"] = CFrame.lookAt((CurrentPos + FuturePos) / 2, FuturePos)
					}):Play()

					-- Color fadeout tween
					TS:Create(Body, TweenInfo.new(Args.Duration, Enum.EasingStyle.Quad, Enum.EasingDirection.In), {
						["Color"] = Args.Color.End or Color3.new(0, 0, 0)
					}):Play()

					if Args.Burning then
						task.delay(Args.Duration * 0.5, function()
							local att = Body:FindFirstChildOfClass("Attachment")
							if att then
								local pLight = att:FindFirstChildOfClass("PointLight")
								if pLight then
									TS:Create(pLight, TweenInfo.new(0.5), {Brightness = 0}):Play()
								end
							end

							for _, Emitter in ipairs(Body:GetChildren()) do
								if not Emitter:IsA("ParticleEmitter") then continue end
								Emitter.Enabled = false
							end
						end)
					end

					task.delay(Args.Duration + 0.35, function()
						TS:Create(Body, TweenInfo.new(0.4, Enum.EasingStyle.Quad), {
							["Size"] = Vector3.new(0, 0, Distance)
						}):Play()
					end)

					task.wait(0.005)
				else
					LastPointPosition = Points[i].Position
				end
			end
		end

		generateBranch()
	end

	local endPoints : {Vector3} = {}

	for i = 1, Args.Amount do
		local Point = Args.Origin * CFrame.Angles(Args.Orientation == "Vertical" and math.rad(math.random(-Args.AxisRange, Args.AxisRange)) or 0, Args.Orientation == "Horizontal" and math.rad(math.random(-Args.AxisRange, Args.AxisRange)) or 0, 0)
		Point = Point * CFrame.new(0, 0, -math.random(Args.Range.Min, Args.Range.Max))
		table.insert(endPoints, Point.Position)
	end

	for _, Point : Vector3 in ipairs(endPoints) do
		task.defer(function()
			createBranch(Point)
		end)
	end
end

function Util:GenerateCrater(Args : { 
	Origin : Vector3 | CFrame, 
	Duration : number?, 
	Radius : NumberRange?, 
	Amount : number?, 
	TweenTime : number?, 
	Angle : number?, 
	BlockSize : NumberRange?, 
	RayLength : number?, 
	Extra : {
		Style : Enum.EasingStyle?, 
		Direction : Enum.EasingDirection?}? 
	} 
)
	if not Args or typeof(Args) ~= 'table' then return end
	if not Args.Origin then return end
	if typeof(Args.Origin) == 'Vector3' then Args.Origin = CFrame.new(Args.Origin) end
	if not Args.Radius then Args.Radius = NumberRange.new(25, 50) end
	if typeof(Args.Radius) ~= 'NumberRange' then 
		Args.Radius = NumberRange.new(typeof(Args.Radius) == 'number' and Args.Radius 
			or 5, typeof(Args.Radius) == 'number' and Args.Radius 
			or 25) 
	end
	if not Args.Amount then Args.Amount = 5 end
	if not Args.TweenTime then Args.TweenTime = 1 end
	if not Args.BlockSize then Args.BlockSize = NumberRange.new(5, 8) end
	if not Args.Angle then Args.Angle = 45 end -- This shit dont even work tbh, I suck at this
	if not Args.RayLength then Args.RayLength = 25 end
	if not Args.Duration then Args.Duration = 2 end
	if not Args.Extra then Args.Extra = {} end

	local Amount = Args.Amount -- Amount of parts
	local MaxUpwardLength = 2.5 -- Length away from the ground
	local TweenTime = Args.TweenTime -- Time it takes to expand
	local KeepTime = Args.Duration -- Time it keeps after it has expanded
	local Origin : CFrame = Args.Origin -- From where the crater originates from (its center)
	local MaxRadius = Args.Radius.Max -- Its max size
	local MinRadius = Args.Radius.Min -- Its min size
	local MinBlockSize = Args.BlockSize.Min
	local MaxBlockSize = Args.BlockSize.Max
	local MinToMaxScale = MinRadius / MaxRadius

	local FullCircle = 360 / Amount

	for Index : number = 1, Amount do
		local xSize = math.sin(math.pi / Amount) * MaxRadius * 2 * 1.01
		local CF = Origin * CFrame.Angles(0, math.rad(Index * FullCircle), 0) * CFrame.new(0, 0, MaxRadius) * CFrame.Angles(math.rad(Args.Angle), 0, 0) --CFrame.Angles(math.rad(45), 0, 0)
		local yzSize = math.random(MinBlockSize, MaxBlockSize)

		local UpLengthCalc = 5 + MaxUpwardLength * 2

		local RayOrigin = CFrame.new(CF.Position) * CFrame.new(0, UpLengthCalc, 0)

		local Part, Raycast : RaycastResult? = 
			createPartOnRay(RayOrigin, Args.RayLength, {
				["Size"] = Vector3.new(xSize * MinToMaxScale, 
					yzSize * MinToMaxScale, yzSize * MinToMaxScale)
			}, nil, nil)
		if Part and Raycast then
			local Mult = 1
			if Raycast.Position.Y < CF.Position.Y then
				Mult = -1
			end

			local MagZX = CF * CFrame.new(0, (CF.Position - Raycast.Position).Magnitude * Mult, 0)
			local ToReturn : CFrame = CFrame.new(Raycast.Position, Raycast.Position + Raycast.Normal) * CFrame.Angles(math.rad(Args.Angle), 0, 0) --CFrame.Angles(math.rad(90), 0, 0)

			Part.CFrame = Origin * CFrame.Angles(0, math.rad(Index * FullCircle), 0) * CFrame.new(0, 0, MaxRadius * MinToMaxScale)

			local partRay = CastRay(Part.Position, -CFrame.new(Part.Position).UpVector * Args.RayLength, nil)
			if partRay then
				Part.CFrame *= 
					CFrame.new(0, 
						-(Part.Position.Y - partRay.Position.Y), 
						0)
			end

			local X, Y, Z = Origin:GetComponents()
			local Ax, Ay, Az, A1, A2, A3, A4, A5, A6, A7, A8, A9 = ToReturn:GetComponents()

			local TargetAngle = CFrame.Angles(
				math.rad(math.random(Args.Angle > 0 and Args.Angle * 0.85 or Args.Angle * 1.15, Args.Angle > 0 and Args.Angle * 1.15 or Args.Angle * 0.85)), 0, 0)
			local TargetCF = CFrame.new(ToReturn.Position, CFrame.new(X, Y, Z, A1, A2, A3, A4, A5, A6, A7, A8, A9).Position) * TargetAngle
			local TargetSize = Vector3.new(xSize, yzSize, yzSize)

			local ExpansionTween = TS:Create(Part, TweenInfo.new(TweenTime, Args.Extra.Style or Enum.EasingStyle.Sine, Args.Extra.Direction or Enum.EasingDirection.Out), {
				["Size"] = Vector3.new(xSize, yzSize, yzSize),
			})

			ExpansionTween:Play()

			local CFTween

			task.spawn(function()
				CFTween = BetterTween.new(
					Part, 
					Part.CFrame, 
					TargetCF,
					TweenInfo.new(TweenTime, Args.Extra.Style or Enum.EasingStyle.Sine, Args.Extra.Direction or Enum.EasingDirection.Out)
				)

				CFTween:Start()
			end)

			task.defer(function()
				local startTick = tick()

				while tick() - startTick < TweenTime do
					if tick() - startTick >= TweenTime then break end

					task.wait()
					local nPart, newRaycast : RaycastResult = createPartOnRay(Part.Position + Vector3.new(0, 5, 0), Args.RayLength, nil, nil, Part)

					if not newRaycast then
						if CFTween then 
							CFTween:Cancel() 
						end

						break
					else
						--print(ToReturn.Position.Y, newRaycast.Position.Y)
						ToReturn = ToReturn * CFrame.new(0, (ToReturn.Position.Y - newRaycast.Position.Y), 0)
						Ax, Ay, Az, A1, A2, A3, A4, A5, A6, A7, A8, A9 = ToReturn:GetComponents()
						TargetCF = CFrame.new(ToReturn.Position, CFrame.new(X, Y, Z, A1, A2, A3, A4, A5, A6, A7, A8, A9).Position) * TargetAngle

						Part.Position += Vector3.new(0, Part.Position.Y - newRaycast.Position.Y, 0)

						if CFTween then CFTween.endPoint = TargetCF end
					end
				end
			end)

			-- Wait for keep time after tween is completed and then delete the part
			task.delay(TweenTime, function()
				task.wait(KeepTime)

				if Part then
					TS:Create(Part, TweenInfo.new(0.45, Enum.EasingStyle.Quad), { ["Position"] = Part.Position - Vector3.new(0, (MaxUpwardLength + yzSize) , 0) }):Play()
					task.wait(0.45)
					if Part then 
						Part:Destroy() 
					end
				end
			end)
		end

	end
end

return Util
