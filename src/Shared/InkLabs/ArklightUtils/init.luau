local Main = require(script.Main);

type CraterProps = {
    Center: Vector3 | CFrame;
    
    Lifetime: number;
    Amount: number;
    FadeInTime: number;
    FadeOutTime: number;
    Angle: number;
    RayLength: number;
    
    Radius: NumberRange;
    BlockSize: NumberRange;

    FadeInStyle: Enum.EasingStyle;
    FadeOutStyle: Enum.EasingStyle;

    FadeInDirection: Enum.EasingDirection;
    FadeOutDirection: Enum.EasingDirection;
}

return {
    GenerateCrater = function(props: CraterProps)
        Main:GenerateCrater({
            Origin = props.Center, 
            Duration = props.Lifetime, 
            Radius = props.Radius, 
            Amount = props.Amount, 
            TweenTime = props.FadeInTime, 
            Angle = props.Angle, 
            BlockSize = props.BlockSize, 
            RayLength = props.RayLength, 
            Extra = {
                Style = props.FadeInStyle, 
                Direction = props.FadeInDirection
            }
        });
    end
}