import {Signal} from "../Network";

interface IMoveableHitbox {
    Touched: Signal<BasePart[]>;

    Start(): void;

    Stop(): void;

    Destroy(): void;

    GetTouchingParts(): BasePart[];

    WeldTo(part: BasePart): void;

    UnWeld(): void;
}

declare const VoxBreaker: {
    VoxelizePart(part: BasePart): BasePart[];

    CreateHitbox(
        size: Vector3,
        cframe: CFrame,
        shape: Enum.PartType,
        minVoxelSize: number,
        resetTime: number,
        params: OverlapParams
    ): BasePart[];

    CreateMoveableHitbox: (
        minVoxelSize: number,
        resetTime: number,
        size: Vector3,
        cframe: CFrame,
        shape: Enum.PartType,
        params: OverlapParams
    ) => IMoveableHitbox;

    CutInHalf(part: BasePart, parent?: Instance): BasePart[];
};

export = VoxBreaker;