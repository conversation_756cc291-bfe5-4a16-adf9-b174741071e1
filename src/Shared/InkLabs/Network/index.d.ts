export declare type SignalParameters<T> = Parameters<
    T extends unknown[] ? (...args: T) => never : T extends unknown ? (arg: T) => never : () => never
>;

export declare type SignalCallback<T> = (...args: SignalParameters<T>) => unknown;
export declare type SignalWait<T> = T extends unknown[] ? LuaTuple<T> : T;

export declare class Connection<T> {
    public readonly Connected: boolean;

    public Disconnect(): void;

    public Reconnect(): void;
}

export declare class Signal<T> {
    public static is: <O extends object>(object: O) => boolean;
    public static wrap: <T extends Callback>(signal: RBXScriptSignal<T>) => Signal<Parameters<T>>;
    public readonly RBXScriptConnection?: RBXScriptConnection;

    public Connect(fn: SignalCallback<T>): Connection<T>;

    public Once(fn: SignalCallback<T>): Connection<T>;

    public Wait(): SignalWait<T>;

    public Fire(...args: SignalParameters<T>): void;

    public DisconnectAll(): void;

    public Destroy(): void;
}

interface RateLimitArgs {
    maxEntrance: number | undefined;
    interval: number | undefined;
}

export declare class RemoteEvent<TArgs extends unknown[] = unknown[]> {
    public readonly OnServerEvent: RBXScriptSignal<(player: Player, ...args: TArgs) => void>;
    public readonly OnClientEvent: RBXScriptSignal<(...args: TArgs) => void>;
    public readonly RBXScriptConnection?: RBXScriptConnection;

    constructor(identifier: string, unreliable?: boolean, rateLimit?: RateLimitArgs);

    public FireAllClients(...args: TArgs): void;

    public FireClient(player: Player, ...args: TArgs): void;

    public FireServer(...args: TArgs): void;

    public Destroy(): void;
}

export declare class RemoteFunction<TArgs extends unknown[] = unknown[], TReturn = unknown> {
    public readonly OnServerInvoke: RBXScriptSignal<(player: Player, ...args: TArgs) => TReturn>;
    public readonly OnClientInvoke: RBXScriptSignal<(...args: TArgs) => TReturn>;
    public readonly RBXScriptConnection?: RBXScriptConnection;

    constructor(identifier: string, rateLimit?: RateLimitArgs);

    public InvokeClient(player: Player, ...args: TArgs): TReturn;

    public InvokeServer(...args: TArgs): TReturn;

    public Wait(): SignalWait<TArgs>;

    public DisconnectAll(): void;

    public Destroy(): void;
}