--!strict
--!native
--!optimize 2
local RunService = game:GetService("RunService")
local Type = require(script.Parent.Type)

if RunService:IsServer() then
	if not script:FindFirstChild("Reliable") then
		Instance.new("RemoteEvent", script).Name = "Reliable"
	end
	if not script:FindFirstChild("Unreliable") then
		Instance.new("UnreliableRemoteEvent", script).Name = "Unreliable"
	end
	if not script:FindFirstChild("Request") then
		Instance.new("RemoteEvent", script).Name = "Request"
	end
elseif not script:FindFirstChild("Reliable") or not script:Find<PERSON>irstChild("Unreliable") or not script:Find<PERSON>irs<PERSON><PERSON>hild("Request") then
	repeat task.wait() until script:Find<PERSON><PERSON><PERSON><PERSON>hild("Reliable") and script:<PERSON><PERSON><PERSON><PERSON><PERSON>hild("Unreliable") and script:Find<PERSON>irstChild("Request")
end

return {
	Reliable = script.Reliable,
	Unreliable = script.Unreliable,
	Request = script.Request
} :: Type.Event