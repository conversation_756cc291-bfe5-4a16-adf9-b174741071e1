-- SERVICES --
local RunService = game:GetService("RunService")

-- DEPENDENCIES --
local Warp = require(script.Parent:WaitForChild("Dependencies"):WaitForChild("Warp"))

-- TYPES --
type rateLimitArg = {
	maxEntrance: number?,
	interval: number?,
}

local RemoteEvent = {}
RemoteEvent.__index = RemoteEvent

function RemoteEvent.new(Identifier: string, Unreliable: boolean?, RateLimit: rateLimitArg?)
	local self = setmetatable({}, RemoteEvent)
	local Event = RunService:IsClient() and Warp.Client(Identifier) or Warp.Server(Identifier, RateLimit) or nil

	function self:FireServer(...)
		if not RunService:IsClient() then
			return
		end
		Event:Fire(not Unreliable, ...)
	end

	function self:FireClient(Player: Player, ...)
		if not RunService:IsServer() then
			return
		end
		Event:Fire(not Unreliable, Player, ...)
	end

	function self:FireAllClients(...)
		if not RunService:IsServer() then
			return
		end
		Event:Fires(not Unreliable, ...)
	end

	self.OnEvent = Event
	self.OnServerEvent = Event
	self.OnClientEvent = Event

	return self
end

return RemoteEvent