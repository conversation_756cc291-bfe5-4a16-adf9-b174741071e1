type ProductCategory = "Product" | "Gamepass" | "Asset" | "Bundle";

interface IProduct {
    Name: string;
    Id: number;
    Type: ProductCategory;

    Price?: number;
    Description?: string;
    Image?: string;
    Callback?: (player: Player) => void;
}

interface IMarketProducts {
    Product: Map<string, IProduct>;
    Gamepass: Map<string, IProduct>;
    Asset: Map<string, IProduct>;
    Bundle: Map<string, IProduct>;
}

export const Products: IMarketProducts;
export const PremiumCallback: ((player: Player) => void) | undefined;

export const SetProducts: (products: IMarketProducts) => void;
export const SetPremiumCallback: (callback: ((player: Player) => void)) => void;
export const AddProduct: (product: IProduct) => void;
export const GetProductByName: (productName: string, productCategory: ProductCategory) => IProduct | undefined;
export const GetProductById: (productId: number, productCategory: ProductCategory) => IProduct | undefined;
export const GetProductsByCategory: (productCategory: ProductCategory) => Map<string, IProduct> | undefined;
export const InitializeBuiltInClientProcess: () => void;
export const InitMarket: (
    products: IMarketProducts, premiumCallback?: ((player: Player) => void), clientProcess?: boolean) => void;