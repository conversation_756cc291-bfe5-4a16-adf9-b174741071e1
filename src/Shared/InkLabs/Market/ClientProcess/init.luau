-- SERVICES --
local RunService = game:GetService("RunService")
if RunService:IsServer() then return {} end

local Players = game:GetService("Players")
local TS = game:GetService('TweenService')
local RS = game:GetService('ReplicatedStorage')
local UIParticle = require(script.UIParticle)

-- PLAYER --
local Player = Players.LocalPlayer
local PlayerGui = Player:WaitForChild("PlayerGui")

local Gui = require(script.GetPurchaseScreen)()
Gui.Parent = PlayerGui

local MainFrame = Gui:WaitForChild("Main")
local MainIcon = MainFrame:WaitForChild("Icon")
local FullIconSize = MainIcon.Size

local PresetIcons = MainFrame:WaitForChild("Icons")
local MainLabel = MainFrame:WaitForChild("TextLabel")

local State = "None"
local Cubes = nil
local Emitter = nil
local ActiveTasks = {}
local ActiveTweens = {}

local function CubesLoop()
	local self = setmetatable({}, {})
	self.__index = self

	local CubesArray = {}

	local CubesHolder = MainFrame:WaitForChild("Cubes")
	CubesHolder.Visible = false

	CubesHolder = CubesHolder:Clone()
	CubesHolder.Visible = true
	CubesHolder.Parent = MainFrame

	for _, CubeBox : Frame in CubesHolder:GetChildren() do
		if not CubeBox:IsA("Frame") then continue end
		CubesArray[CubeBox.LayoutOrder] = CubeBox:WaitForChild("Main")
		CubesArray[CubeBox.LayoutOrder].Size = UDim2.fromScale(0, 0)
	end

	local GroundHitEvent = Instance.new("BindableEvent")
	local ShrinkedEvent = Instance.new("BindableEvent")

	local function hitGround(Index : number)
		local CubeFrame = CubesArray[Index]
		if not CubeFrame then warn(525, Index) return end

		local ShrinkTween = TS:Create(CubeFrame, TweenInfo.new(0.15, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {Size = UDim2.fromScale(1.2, 0.5)})

		-- SHADOW TWEEN --
		task.spawn(function()
			local Shadow : ImageLabel = CubeFrame and CubeFrame.Parent and CubeFrame.Parent:FindFirstChild("Shadow")
			if not Shadow then return end

			Shadow:TweenSize(UDim2.fromScale(1.2, 0.5), Enum.EasingDirection.Out, Enum.EasingStyle.Quad, 0.15, true)
		end)

		ShrinkTween:Play()
		ShrinkTween.Completed:Wait()
	end

	local function Jump(Index : number)
		local CubeFrame = CubesArray[Index]
		if not CubeFrame then warn(675, Index) return end

		local SizeUpTween = TS:Create(CubeFrame, TweenInfo.new(0.15, Enum.EasingStyle.Back, Enum.EasingDirection.In), {Size = UDim2.fromScale(1, 1)})
		SizeUpTween:Play()

		SizeUpTween.Completed:Once(function()
			local RotTween = TS:Create(CubeFrame, TweenInfo.new(.375, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {Rotation = CubeFrame.Rotation - 360})
			RotTween:Play()
		end)

		local JumpTween = TS:Create(CubeFrame, TweenInfo.new(.25, Enum.EasingStyle.Quad, Enum.EasingDirection.Out), {Position = UDim2.fromScale(0.5, -1)})
		JumpTween:Play()

		-- SHADOW TWEEN --
		task.spawn(function()
			local Shadow : ImageLabel = CubeFrame and CubeFrame.Parent and CubeFrame.Parent:FindFirstChild("Shadow")
			if not Shadow then return end

			Shadow:TweenSize(UDim2.fromScale(0.2, 0.2), Enum.EasingDirection.Out, Enum.EasingStyle.Quad, 0.25, true)
		end)

		task.delay(0.275, function()
			ShrinkedEvent:Fire(Index)
		end)

		JumpTween.Completed:Once(function()
			local FallTween = TS:Create(CubeFrame, TweenInfo.new(.35, Enum.EasingStyle.Sine, Enum.EasingDirection.In), {Position = UDim2.fromScale(0.5, 1)})
			FallTween:Play()

			-- SHADOW TWEEN --
			task.spawn(function()
				local Shadow : ImageLabel = CubeFrame and CubeFrame.Parent and CubeFrame.Parent:FindFirstChild("Shadow")
				if not Shadow then return end

				Shadow:TweenSize(UDim2.fromScale(1, 0.4), Enum.EasingDirection.In, Enum.EasingStyle.Sine, 0.35, true)
			end)

			FallTween.Completed:Wait()
			GroundHitEvent:Fire(Index)
		end)
	end

	GroundHitEvent.Event:Connect(function(Index : number)
		hitGround(Index)
	end)

	ShrinkedEvent.Event:Connect(function(Index : number)
		Jump(CubesArray[Index + 1] and Index + 1 or 1)
	end)

	Jump(1)

	function self:Destroy()
		for _, Cube in CubesArray do
			task.defer(function()
				local TransparencyTween = TS:Create(Cube, TweenInfo.new(0.35, Enum.EasingStyle.Sine, Enum.EasingDirection.InOut), {ImageTransparency = 1})
				TransparencyTween:Play()
			end)
		end

		task.delay(0.35, function()
			GroundHitEvent:Destroy()
			ShrinkedEvent:Destroy()
			CubesHolder:Destroy()
		end)
	end

	function self:GetCubes() : {Instance}
		return CubesArray
	end

	return self
end

local function clearTweens()
	for _, activeTween in ActiveTweens do
		activeTween:Destroy()
	end
end

local function clearThreads()
	for _, activeThread in ActiveTasks do
		pcall(function()
			task.cancel(activeThread)
		end)
	end
end

local function ToggleScreen(NewState : "Success" | "Fail" | "Pending")
	if not Gui.Enabled then
		Gui.Enabled = true
	end

	if NewState == 'Pending' then
		if State == 'Pending' then return end
		State = NewState
		
		local MainFrameTween = TS:Create(MainFrame, TweenInfo.new(0.45, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {BackgroundTransparency = 0.15})
		MainFrameTween:Play()
		Cubes = CubesLoop()

		clearTweens()
		clearThreads()	

		MainIcon.Rotation = 0
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)
		MainIcon.Size = UDim2.fromScale(0, 0)
		MainIcon.Image = PresetIcons.Loading.Image
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)

		local SizeTween = TS:Create(MainIcon, TweenInfo.new(0.35, Enum.EasingStyle.Back, Enum.EasingDirection.Out), { Size = FullIconSize, ImageColor3 = Color3.new(0.34902, 0.67451, 1) })
		table.insert(ActiveTweens, SizeTween)

		SizeTween:Play()

		local RotationTween = TS:Create(MainIcon, TweenInfo.new(0.5, Enum.EasingStyle.Sine, Enum.EasingDirection.Out, -1, false, 0.25), {Rotation = MainIcon.Rotation + 360})
		table.insert(ActiveTweens, RotationTween)

		RotationTween:Play()

		task.spawn(function()
			local lastIteration = tick()

			MainLabel.MaxVisibleGraphemes = 0
			MainLabel.TextColor3 = Color3.new(1, 1, 1)
			MainLabel.Text = "Processing Purchase..."

			local textTween = TS:Create(MainLabel, TweenInfo.new(0.25, Enum.EasingStyle.Sine), {MaxVisibleGraphemes = utf8.len(MainLabel.Text)})
			table.insert(ActiveTweens, textTween)
			textTween:Play()

			while State == 'Pending' do
				if tick() - lastIteration > 0.35 then -- So it doesn't delay the whole effect
					lastIteration = tick()
					MainLabel.Text = MainLabel.Text == 'Processing Purchase...' and "Processing Purchase" or MainLabel.Text .. "."
				end

				task.wait()
			end	
		end)

	elseif NewState == "Success" then
		if State ~= "Pending" then return end
		State = NewState

		if Cubes then
			Cubes:Destroy()
		end

		clearTweens()
		clearThreads()

		MainIcon.Rotation = -65
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)
		MainIcon.Size = UDim2.fromScale(0, 0)
		MainIcon.Image = PresetIcons.Success.Image
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)

		local SizeTween = TS:Create(MainIcon, TweenInfo.new(0.35, Enum.EasingStyle.Back, Enum.EasingDirection.Out), { Size = FullIconSize, Rotation = 0, ImageColor3 = Color3.new(0.227451, 1, 0.396078) })
		table.insert(ActiveTweens, SizeTween)
		SizeTween:Play()

		MainLabel.Text = "Purchase Completed!"

		if Emitter then
			Emitter:Emit(50)
		end

		local textTween = TS:Create(MainLabel, TweenInfo.new(0.25, Enum.EasingStyle.Sine), {TextColor3 = Color3.new(0.227451, 1, 0.396078), MaxVisibleGraphemes = utf8.len(MainLabel.Text)})
		table.insert(ActiveTweens, textTween)
		textTween:Play()

		local delayTask = task.delay(1.5, function()
			local newTextTween = TS:Create(MainLabel, TweenInfo.new(0.25, Enum.EasingStyle.Sine), {TextColor3 = Color3.new(1, 1, 1), MaxVisibleGraphemes = 0})
			table.insert(ActiveTweens, newTextTween)
			newTextTween:Play()

			local newSizeTween = TS:Create(MainIcon, TweenInfo.new(0.45, Enum.EasingStyle.Back, Enum.EasingDirection.In), { Size = UDim2.fromScale(0, 0), Rotation = -65 })
			table.insert(ActiveTweens, newSizeTween)
			newSizeTween:Play()

			newSizeTween.Completed:Once(function()
				local MainFrameTween = TS:Create(MainFrame, TweenInfo.new(0.45, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {BackgroundTransparency = 1})
				table.insert(ActiveTweens, MainFrameTween)
				MainFrameTween:Play()
			end)
		end)

		table.insert(ActiveTasks, delayTask)
	elseif NewState == "Fail" then
		if State ~= "Pending" then return end

		State = NewState
		if Cubes then
			Cubes:Destroy()
		end

		clearTweens()
		clearThreads()

		MainIcon.Rotation = -65
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)
		MainIcon.Size = UDim2.fromScale(0, 0)
		MainIcon.Image = PresetIcons.Failed.Image
		MainIcon.ImageColor3 = Color3.new(1, 1, 1)

		local SizeTween = TS:Create(MainIcon, TweenInfo.new(0.35, Enum.EasingStyle.Back, Enum.EasingDirection.Out), { Size = FullIconSize, Rotation = 0, ImageColor3 = Color3.new(1, 0.243137, 0.243137) })
		table.insert(ActiveTweens, SizeTween)
		SizeTween:Play()

		MainLabel.Text = "Purchase Canceled!"

		local textTween = TS:Create(MainLabel, TweenInfo.new(0.25, Enum.EasingStyle.Sine), {TextColor3 = Color3.new(1, 0.341176, 0.341176), MaxVisibleGraphemes = utf8.len(MainLabel.Text)})
		table.insert(ActiveTweens, textTween)
		textTween:Play()

		local delayTask = task.delay(1.5, function()
			local newTextTween = TS:Create(MainLabel, TweenInfo.new(0.25, Enum.EasingStyle.Sine), {TextColor3 = Color3.new(1, 1, 1), MaxVisibleGraphemes = 0})
			table.insert(ActiveTweens, newTextTween)
			newTextTween:Play()

			local newSizeTween = TS:Create(MainIcon, TweenInfo.new(0.45, Enum.EasingStyle.Back, Enum.EasingDirection.In), { Size = UDim2.fromScale(0, 0), Rotation = -65 })
			table.insert(ActiveTweens, newSizeTween)
			newSizeTween:Play()

			newSizeTween.Completed:Once(function()
				local MainFrameTween = TS:Create(MainFrame, TweenInfo.new(0.45, Enum.EasingStyle.Quad, Enum.EasingDirection.InOut), {BackgroundTransparency = 1})
				table.insert(ActiveTweens, MainFrameTween)
				MainFrameTween:Play()
			end)
		end)

		table.insert(ActiveTasks, delayTask)
	end
end

local ParticleTemplate = Instance.new("ImageLabel")
ParticleTemplate.Image = "rbxassetid://14783983085"
ParticleTemplate.ImageColor3 = Color3.fromRGB(58, 255, 101)
ParticleTemplate.ScaleType = Enum.ScaleType.Fit
ParticleTemplate.AnchorPoint = Vector2.new(0.5, 0.5)
ParticleTemplate.BackgroundTransparency = 1
ParticleTemplate.Size = UDim2.fromOffset(35, 35)
ParticleTemplate.ZIndex = 0
ParticleTemplate.Parent = script

Emitter = UIParticle.new(MainFrame, ParticleTemplate)

-- ASSIGN EMITTER PROPS --
Emitter.Rate = 25
Emitter.EmitterMode = "Fill"
Emitter.Lifetime = NumberRange.new(3, 5)
Emitter.RotSpeed = NumberRange.new(-45, 45)

local Transparency = NumberSequence.new({
	NumberSequenceKeypoint.new(0, 1),
	NumberSequenceKeypoint.new(.5, 0),
	NumberSequenceKeypoint.new(1, 1)
})

Emitter.Transparency = Transparency
Emitter.ZOffset = 0

Players.LocalPlayer:GetAttributeChangedSignal("_PURCHASE_STATUS"):Connect(function()
	local NewState = Players.LocalPlayer:GetAttribute('_PURCHASE_STATUS')
	if not NewState or typeof(NewState) ~= 'string' then return end
	
	ToggleScreen(NewState)
end)

return {}
