return function()
    local purchaseScreen = Instance.new("ScreenGui")
    purchaseScreen.Name = "PurchaseScreen"
    purchaseScreen.DisplayOrder = 15
    purchaseScreen.IgnoreGuiInset = true
    purchaseScreen.ScreenInsets = Enum.ScreenInsets.DeviceSafeInsets
    purchaseScreen.Enabled = false
    purchaseScreen.ResetOnSpawn = false
    purchaseScreen.ZIndexBehavior = Enum.ZIndexBehavior.Sibling

    local main = Instance.new("Frame")
    main.Name = "Main"
    main.AnchorPoint = Vector2.new(0, 1)
    main.BackgroundColor3 = Color3.fromRGB(30, 30, 36)
    main.BackgroundTransparency = 0.15
    main.BorderColor3 = Color3.fromRGB(0, 0, 0)
    main.BorderSizePixel = 0
    main.Position = UDim2.fromScale(0, 1)
    main.Size = UDim2.fromScale(1, 1)
    main.ZIndex = 0

    local pattern = Instance.new("ImageLabel")
    pattern.Name = "Pattern"
    pattern.Image = "rbxassetid://2151760196"
    pattern.ImageTransparency = 1
    pattern.ScaleType = Enum.ScaleType.Tile
    pattern.SliceCenter = Rect.new(0, 256, 0, 256)
    pattern.TileSize = UDim2.fromScale(0.015, 0.015)
    pattern.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    pattern.BackgroundTransparency = 1
    pattern.Position = UDim2.fromOffset(-500, -500)
    pattern.Size = UDim2.new(1, 500, 1, 500)
    pattern.ZIndex = 0
    pattern.Parent = main

    local cubes = Instance.new("Frame")
    cubes.Name = "Cubes"
    cubes.AnchorPoint = Vector2.new(0.5, 0.5)
    cubes.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    cubes.BackgroundTransparency = 1
    cubes.BorderColor3 = Color3.fromRGB(0, 0, 0)
    cubes.BorderSizePixel = 0
    cubes.Position = UDim2.fromScale(0.5, 0.85)
    cubes.Size = UDim2.fromScale(0.05, 0.055)
    cubes.SizeConstraint = Enum.SizeConstraint.RelativeYY

    local cubeHolder = Instance.new("Frame")
    cubeHolder.Name = "CubeHolder"
    cubeHolder.AnchorPoint = Vector2.new(0, 1)
    cubeHolder.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    cubeHolder.BackgroundTransparency = 1
    cubeHolder.BorderColor3 = Color3.fromRGB(0, 0, 0)
    cubeHolder.BorderSizePixel = 0
    cubeHolder.LayoutOrder = 1
    cubeHolder.Position = UDim2.fromScale(0, 1)
    cubeHolder.Size = UDim2.fromScale(1, 1)

    local main1 = Instance.new("ImageLabel")
    main1.Name = "Main"
    main1.Image = "rbxassetid://14783200972"
    main1.AnchorPoint = Vector2.new(0.5, 1)
    main1.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    main1.BackgroundTransparency = 1
    main1.BorderColor3 = Color3.fromRGB(0, 0, 0)
    main1.BorderSizePixel = 0
    main1.Position = UDim2.fromScale(0.5, 1)
    main1.Size = UDim2.fromScale(1, 1)
    main1.Parent = cubeHolder

    local shadow = Instance.new("ImageLabel")
    shadow.Name = "Shadow"
    shadow.Image = "rbxassetid://5552526748"
    shadow.ImageColor3 = Color3.fromRGB(0, 0, 0)
    shadow.ImageTransparency = 0.5
    shadow.AnchorPoint = Vector2.new(0.5, 1)
    shadow.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    shadow.BackgroundTransparency = 1
    shadow.BorderColor3 = Color3.fromRGB(0, 0, 0)
    shadow.BorderSizePixel = 0
    shadow.Position = UDim2.fromScale(0.5, 1)
    shadow.Size = UDim2.fromScale(1, 0.4)
    shadow.Visible = false
    shadow.ZIndex = 0
    shadow.Parent = cubeHolder

    cubeHolder.Parent = cubes

    local uIListLayout = Instance.new("UIListLayout")
    uIListLayout.Name = "UIListLayout"
    uIListLayout.Padding = UDim.new(1, 0)
    uIListLayout.FillDirection = Enum.FillDirection.Horizontal
    uIListLayout.HorizontalAlignment = Enum.HorizontalAlignment.Center
    uIListLayout.SortOrder = Enum.SortOrder.LayoutOrder
    uIListLayout.Parent = cubes

    local cubeHolder1 = Instance.new("Frame")
    cubeHolder1.Name = "CubeHolder"
    cubeHolder1.AnchorPoint = Vector2.new(0, 1)
    cubeHolder1.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    cubeHolder1.BackgroundTransparency = 1
    cubeHolder1.BorderColor3 = Color3.fromRGB(0, 0, 0)
    cubeHolder1.BorderSizePixel = 0
    cubeHolder1.LayoutOrder = 2
    cubeHolder1.Position = UDim2.fromScale(0, 1)
    cubeHolder1.Size = UDim2.fromScale(1, 1)

    local main2 = Instance.new("ImageLabel")
    main2.Name = "Main"
    main2.Image = "rbxassetid://14783200972"
    main2.AnchorPoint = Vector2.new(0.5, 1)
    main2.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    main2.BackgroundTransparency = 1
    main2.BorderColor3 = Color3.fromRGB(0, 0, 0)
    main2.BorderSizePixel = 0
    main2.Position = UDim2.fromScale(0.5, 1)
    main2.Size = UDim2.fromScale(1, 1)
    main2.Parent = cubeHolder1

    local shadow1 = Instance.new("ImageLabel")
    shadow1.Name = "Shadow"
    shadow1.Image = "rbxassetid://5552526748"
    shadow1.ImageColor3 = Color3.fromRGB(0, 0, 0)
    shadow1.ImageTransparency = 0.5
    shadow1.AnchorPoint = Vector2.new(0.5, 1)
    shadow1.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    shadow1.BackgroundTransparency = 1
    shadow1.BorderColor3 = Color3.fromRGB(0, 0, 0)
    shadow1.BorderSizePixel = 0
    shadow1.Position = UDim2.fromScale(0.5, 1)
    shadow1.Size = UDim2.fromScale(1, 0.4)
    shadow1.Visible = false
    shadow1.ZIndex = 0
    shadow1.Parent = cubeHolder1

    cubeHolder1.Parent = cubes

    local cubeHolder2 = Instance.new("Frame")
    cubeHolder2.Name = "CubeHolder"
    cubeHolder2.AnchorPoint = Vector2.new(0, 1)
    cubeHolder2.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    cubeHolder2.BackgroundTransparency = 1
    cubeHolder2.BorderColor3 = Color3.fromRGB(0, 0, 0)
    cubeHolder2.BorderSizePixel = 0
    cubeHolder2.LayoutOrder = 3
    cubeHolder2.Position = UDim2.fromScale(0, 1)
    cubeHolder2.Size = UDim2.fromScale(1, 1)

    local main3 = Instance.new("ImageLabel")
    main3.Name = "Main"
    main3.Image = "rbxassetid://14783200972"
    main3.AnchorPoint = Vector2.new(0.5, 1)
    main3.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    main3.BackgroundTransparency = 1
    main3.BorderColor3 = Color3.fromRGB(0, 0, 0)
    main3.BorderSizePixel = 0
    main3.Position = UDim2.fromScale(0.5, 1)
    main3.Size = UDim2.fromScale(1, 1)
    main3.Parent = cubeHolder2

    local shadow2 = Instance.new("ImageLabel")
    shadow2.Name = "Shadow"
    shadow2.Image = "rbxassetid://5552526748"
    shadow2.ImageColor3 = Color3.fromRGB(0, 0, 0)
    shadow2.ImageTransparency = 0.5
    shadow2.AnchorPoint = Vector2.new(0.5, 1)
    shadow2.BackgroundColor3 = Color3.fromRGB(0, 0, 0)
    shadow2.BackgroundTransparency = 1
    shadow2.BorderColor3 = Color3.fromRGB(0, 0, 0)
    shadow2.BorderSizePixel = 0
    shadow2.Position = UDim2.fromScale(0.5, 1)
    shadow2.Size = UDim2.fromScale(1, 0.4)
    shadow2.Visible = false
    shadow2.ZIndex = 0
    shadow2.Parent = cubeHolder2

    cubeHolder2.Parent = cubes

    cubes.Parent = main

    local textLabel = Instance.new("TextLabel")
    textLabel.Name = "TextLabel"
    textLabel.FontFace = Font.new("rbxasset://fonts/families/FredokaOne.json")
    textLabel.MaxVisibleGraphemes = 0
    textLabel.Text = "Processing Purchase..."
    textLabel.TextColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.TextSize = 14
    textLabel.TextWrapped = true
    textLabel.AnchorPoint = Vector2.new(0.5, 0.5)
    textLabel.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    textLabel.BackgroundTransparency = 1
    textLabel.BorderColor3 = Color3.fromRGB(0, 0, 0)
    textLabel.BorderSizePixel = 0
    textLabel.Position = UDim2.fromScale(0.5, 0.925)
    textLabel.Size = UDim2.fromScale(0.76, 0.05)
    textLabel.ZIndex = 2
    textLabel.Parent = main

    local icon = Instance.new("ImageLabel")
    icon.Name = "Icon"
    icon.Image = "rbxassetid://14783407465"
    icon.ScaleType = Enum.ScaleType.Fit
    icon.AnchorPoint = Vector2.new(0.5, 0.5)
    icon.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    icon.BackgroundTransparency = 1
    icon.BorderColor3 = Color3.fromRGB(0, 0, 0)
    icon.BorderSizePixel = 0
    icon.Position = UDim2.fromScale(0.5, 0.5)
    icon.Size = UDim2.fromScale(0.15, 0.15)
    icon.SizeConstraint = Enum.SizeConstraint.RelativeYY
    icon.Parent = main

    local icons = Instance.new("Frame")
    icons.Name = "Icons"
    icons.AnchorPoint = Vector2.new(0.5, 0.5)
    icons.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    icons.BackgroundTransparency = 1
    icons.BorderColor3 = Color3.fromRGB(0, 0, 0)
    icons.BorderSizePixel = 0
    icons.Position = UDim2.fromScale(0.5, 0.0741)
    icons.Size = UDim2.fromScale(0.1, 0.1)
    icons.SizeConstraint = Enum.SizeConstraint.RelativeYY

    local loading = Instance.new("ImageLabel")
    loading.Name = "Loading"
    loading.Image = "rbxassetid://14783407465"
    loading.ImageTransparency = 1
    loading.ScaleType = Enum.ScaleType.Fit
    loading.AnchorPoint = Vector2.new(0.5, 0.5)
    loading.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    loading.BackgroundTransparency = 1
    loading.BorderColor3 = Color3.fromRGB(0, 0, 0)
    loading.BorderSizePixel = 0
    loading.Position = UDim2.fromScale(0.5, 0.5)
    loading.Size = UDim2.fromScale(1, 1)
    loading.SizeConstraint = Enum.SizeConstraint.RelativeYY
    loading.Parent = icons

    local success = Instance.new("ImageLabel")
    success.Name = "Success"
    success.Image = "rbxassetid://14783433921"
    success.ImageTransparency = 1
    success.ScaleType = Enum.ScaleType.Fit
    success.AnchorPoint = Vector2.new(0.5, 0.5)
    success.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    success.BackgroundTransparency = 1
    success.BorderColor3 = Color3.fromRGB(0, 0, 0)
    success.BorderSizePixel = 0
    success.Position = UDim2.fromScale(0.5, 0.5)
    success.Size = UDim2.fromScale(1, 1)
    success.SizeConstraint = Enum.SizeConstraint.RelativeYY
    success.Parent = icons

    local failed = Instance.new("ImageLabel")
    failed.Name = "Failed"
    failed.Image = "rbxassetid://14783437649"
    failed.ImageTransparency = 1
    failed.ScaleType = Enum.ScaleType.Fit
    failed.AnchorPoint = Vector2.new(0.5, 0.5)
    failed.BackgroundColor3 = Color3.fromRGB(255, 255, 255)
    failed.BackgroundTransparency = 1
    failed.BorderColor3 = Color3.fromRGB(0, 0, 0)
    failed.BorderSizePixel = 0
    failed.Position = UDim2.fromScale(0.5, 0.5)
    failed.Size = UDim2.fromScale(1, 1)
    failed.SizeConstraint = Enum.SizeConstraint.RelativeYY
    failed.Parent = icons

    icons.Parent = main

    main.Parent = purchaseScreen
    return purchaseScreen
end