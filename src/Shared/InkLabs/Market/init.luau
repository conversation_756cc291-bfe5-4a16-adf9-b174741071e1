-- SERVICES --
local Players = game:GetService("Players")
local MarketplaceService = game:GetService("MarketplaceService")
local RunService = game:GetService("RunService")

local Notify = require(script.Parent.Notify)
local Network = require(script.Parent.Network)
local MarketEvent = Network.RemoteEvent.new('Marketplace')

-- TYPES --
type ProductCategory = "Product" | "Gamepass" | "Asset" | "Bundle"

type ProductType = {
    Name: string,
    Id: number,
    Type: ProductCategory,

    Price: number?,
    Description: string?,
    Image: string?,
    Callback: ((Player: Player) -> nil?)?
}

type ReceiptType = {
	PurchaseId : number,
	PlayerId : number,
	ProductId : number,
	PlaceIdWherePurchased : number,
	CurrencySpent : number,
	CurrencyType : Enum.CurrencyType
}

type AssetInformation = {
	Name: string,
	Description: string,
	PriceInRobux: string,
	Created: string,
	Updated: string,
	ContentRatingTypeId: number,
	MinimumMembershipLevel: number,
	IsPublicDomain: boolean
}

type MarketProducts = {
    Product: {[string]: ProductType},
    Gamepass: {[string]: ProductType},
    Asset: {[string]: ProductType},
    Bundle: {[string]: ProductType}
}

-- MAIN --
local Market = {}
Market.Products = {}
Market.PremiumCallback = nil
Market.Purchased = Network.Signal.new()

function Market.SetProducts(newProducts: MarketProducts)
    Market.Products = newProducts
end

function Market.SetPremiumCallback(callback: ((Player: Player) -> nil)?)
    Market.PremiumCallback = callback
end

function Market.AddProduct(product: ProductType): nil
    if not Market.Products[product.Type] then 
        Market.Products[product.Type] = {} 
    end

    Market.Products[product.Type][product.Name] = product
end

function Market.GetProductByName(productName: string, productCategory: ProductCategory?): ProductType?
    if productCategory then
        return Market.Products[productCategory] and Market.Products[productCategory][productName]
    end

    for _, category in Market.Products do
        if not category[productName] then
            continue
        end

        return category[productName]
    end

    return nil
end

function Market.GetProductById(productId: number, productCategory: ProductCategory?): ProductType?
    if productCategory then
        for _, product in pairs(Market.Products[productCategory]) do
            if product.Id ~= productId then continue end
            return product
        end

        return nil
    end

    for _, category in Market.Products do
        for _, product in category do
            if product.Id ~= productId then continue end
            return product
        end
    end
end

function Market.GetProductsByCategory(productCategory: ProductCategory): {[string]: ProductType}?
	return Market.Products[productCategory]
end

function Market.InitializeBuiltInClientProcess(): nil
    require(script.ClientProcess)
    return nil
end

local function notifyPurchase(player: Player, productName: string, receiver: Player?)
	if RunService:IsClient() then return end

	Notify({
		Player = player,
		Header = 'Purchase Successful!',
		Description = (receiver and `You bought [{productName}] for [{receiver}]!`) or `You bought [{productName}]!`,
		Style = 'Success',
		Duration = 10
	})

	if receiver then
		Notify({
			Player = receiver,
			Header = 'Gift Incoming!',
			Description = `You have received [{productName}] from [{player}]!`,
			Style = 'Gifted',
			Duration = 10
		})
	end
end

local function resetPurchasingStatus(player : Player, result : "Success" | "Fail")
	player:SetAttribute("Gifting", nil)
	player:SetAttribute("_PURCHASE_STATUS", result)
end

function Market.PromptPurchase(player: Player, productId: string | number, productCategory: ProductCategory?)
	if player:GetAttribute('_PURCHASE_STATUS') == 'Pending' then return end

	if RunService:IsClient() then 
		MarketEvent:FireServer('PromptPurchase', productId, productCategory)
		return
	end

	player:SetAttribute("_PURCHASE_STATUS", 'Pending')

	if typeof(productId) ~= 'number' then
		productId = Market.Products[productCategory or 'Product'] and Market.Products[productCategory or 'Product'][productId]
		if not productId then 
			resetPurchasingStatus(player, 'Fail')
			return 
		end
	end

	if productCategory == 'Asset' then
		MarketplaceService:PromptPurchase(player, productId)
	elseif productCategory == 'Gamepass' then
		MarketplaceService:PromptGamePassPurchase(player, productId)
	elseif productCategory == 'Bundle' then
		MarketplaceService:PromptBundlePurchase(player, productId)
	else
		MarketplaceService:PromptProductPurchase(player, productId)
	end
end

function Market.Gift(gifter: Player, receiver: Player, productId: string | number, productCategory: ProductCategory?)
	if RunService:IsClient() then
		MarketEvent:FireServer('Gift', receiver.UserId, productId, productCategory)
		return
	end

	gifter:SetAttribute("Gifting", receiver.Name)
	Market.PromptPurchase(gifter, productId, productCategory)
end

local function executeCallback(player: Player, product: ProductType): boolean
    if product.Callback then
        product.Callback(player)
        return true
    end

	return false
end

local function premiumCheck(player : Player)
	if player.MembershipType ~= Enum.MembershipType.Premium or not Market.PremiumCallback then return end
	Market.PremiumCallback(player)
end

local function checkPasses(player : Player)
	if not RunService:IsServer() then return end
    if not Market.Products.Gamepass then return end 

	for name, passId in Market.Products.Gamepass do
		task.spawn(function()
			local succ, hasPass = pcall(function()
				return MarketplaceService:UserOwnsGamePassAsync(player.UserId, passId)
			end)

			if not succ or hasPass ~= true then return end
			executeCallback(player, name)
		end)
	end
end

local purchaseCompletedFunction = function(player : Player, purchaseReceipt : ReceiptType, productCategory: ProductCategory)
	local s: boolean, productInfo: AssetInformation = pcall(function()
		return MarketplaceService:GetProductInfo(purchaseReceipt.ProductId, 
			productCategory == 'Product' and Enum.InfoType.Product
				or productCategory == 'Gamepass' and Enum.InfoType.GamePass
				or productCategory == 'Asset' and Enum.InfoType.Asset
				or productCategory == 'Bundle' and Enum.InfoType.Bundle
		)
	end)

	if not s then
		resetPurchasingStatus(player, "Fail")
		return
	end

	local giftingTarget: Player? = player:GetAttribute("Gifting") and typeof(player:GetAttribute("Gifting")) == 'string' and Players:FindFirstChild(player:GetAttribute("Gifting"))
    local product = Market.GetProductById(purchaseReceipt.ProductId, productCategory)

    if product then
        if not giftingTarget then
            executeCallback(player, product)
        else
            executeCallback(giftingTarget, product)
        end
    end

	notifyPurchase(player, productInfo.Name, giftingTarget)
	resetPurchasingStatus(player, "Success")
	Market.Purchased:Fire(player, purchaseReceipt, productCategory);
end

if RunService:IsServer() then
	MarketplaceService.PromptProductPurchaseFinished:Connect(function(UserId : number, AssetID : number, isPurchased : boolean)
		local Player = Players:GetPlayerByUserId(UserId)
		if not Player then return end

		if not isPurchased then
			resetPurchasingStatus(Player, "Fail") 
			return 
		end

		purchaseCompletedFunction(Player, {ProductId = AssetID}, "Product")
	end)

	MarketplaceService.PromptPurchaseFinished:Connect(function(Player : Player, AssetID : number, isPurchased : boolean)
		if not isPurchased then
			resetPurchasingStatus(Player, "Fail") 
			return 
		end

		purchaseCompletedFunction(Player, {ProductId = AssetID}, "Asset")
	end)

	MarketplaceService.PromptGamePassPurchaseFinished:Connect(function(Player : Player, AssetID : number, isPurchased : boolean)
		if not isPurchased then
			resetPurchasingStatus(Player, "Fail") 
			return 
		end

		purchaseCompletedFunction(Player, {ProductId = AssetID}, "Gamepass")
	end)

	MarketplaceService.PromptBundlePurchaseFinished:Connect(function(Player : Player, AssetID : number, isPurchased : boolean)
		if not isPurchased then
			resetPurchasingStatus(Player, "Fail")
			return 
		end

		purchaseCompletedFunction(Player, {ProductId = AssetID}, "Bundle")
	end)

	Players.PlayerMembershipChanged:Connect(premiumCheck)

	for _, Player : Player in Players:GetPlayers() do
		checkPasses(Player)
		premiumCheck(Player)
	end

	Players.PlayerAdded:Connect(function(Player : Player)
		checkPasses(Player)
		premiumCheck(Player)
	end)

	MarketEvent.OnEvent:Connect(function(player: Player, action: string, ...)
		local Args = {...}

		if action == 'PromptPurchase' then
			local id = Args[1]
			local purchaseType = Args[2]
			if not id then return end

			Market.PromptPurchase(player, id, purchaseType)
		elseif action == 'Gift' then
			local receiver = Args[1] and typeof(Args[1]) == 'number' and Players:GetPlayerByUserId(Args[1])
			local id = Args[2]
			local purchaseCategory = Args[3]
			if not receiver or not id then return end

			Market.Gift(player, receiver, id, purchaseCategory)
		end
	end)
end

Market.InitMarket = function(products: MarketProducts, premiumCallback: ((Player: Player) -> nil)?, clientProcess: boolean?)
    Market.SetProducts(products)
    Market.SetPremiumCallback(premiumCallback)
    if clientProcess then 
        Market.InitializeBuiltInClientProcess() 
    end
end

return Market
