local RunService = game:GetService("RunService")
local Players = game:GetService("Players")

local Mouse = {}
local _mouse = require(script.MouseModule2).new()
local replicationCooldown = 0.05

function Mouse.SetFilterList(filterList)
    if RunService:IsServer() then
        return
    end
    
    _mouse.TargetFilters:Set(filterList)
end

function Mouse.GetMouseHit(filterList: {Instance}?, player: Player?): Vector3
    if RunService:IsServer() then
        return player and player:Get<PERSON><PERSON>ribute("Mouse") or Vector3.zero
    end

    local character = player and player.Character or RunService:IsClient() and Players.LocalPlayer.Character
    if character then
        if filterList then
            table.insert(filterList, character)
        else
            filterList = {character}
        end
    end

    local oldFilter = nil
    if filterList then
        oldFilter = _mouse.TargetFilters:Get()
        _mouse.TargetFilters:Set(table.unpack(oldFilter), table.unpack(filterList))
    end

    local hitCFrame = _mouse.Hit
    if oldFilter then
        _mouse.TargetFilters:Set(oldFilter)
    end

    return hitCFrame.Position
end

return Mouse