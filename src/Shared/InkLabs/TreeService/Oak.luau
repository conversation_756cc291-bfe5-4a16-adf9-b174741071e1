local OakTree = {}

-- General settings
OakTree.Name = script.Name

-- Appearance
OakTree.Appearance = {
	Bark = {
		Material = Enum.Material.Concrete,
		Color = Color3.fromRGB(108, 88, 75)
	},

	Leaves = {
		Color = function(treeData)
			return Color3.fromRGB(
				91 + math.random(-10, 10),
				154 + math.random(-10, 10),
				76 + math.random(-10, 10)
			)
		end,
		Material = Enum.Material.Grass,
	},
}

-- Generation
OakTree.LeafGeneration = {
	Amount = function(treeData)
		if treeData.SegmentIndex >= treeData.TotalSegments then
			return 2
		end

		return 0
	end,

	Size = function(treeData)
		local diameter = math.random(14, 16) * Vector3.new(1, 0.75, 1)

		return diameter * Vector3.new(
			math.random(9, 12) / 10,
			math.random(9, 12) / 10,
			math.random(9, 12) / 10
		)
	end,

	CFrame = function(treeData)
		return 
			CFrame.Angles(
				0,
				0,
				math.rad(math.random(10, 15))
			)
			* CFrame.Angles(
				0,
				math.rad(math.random(-25, 25)),
				0
			)
			* CFrame.Angles(
				0,
				0,
				math.rad(math.random(-5, 5))
			)
	end,
}

OakTree.BranchGeneration = {
	Segments = function(treeData)
		return math.random(17, 21)
	end,

	Splits = function(treeData)
		if treeData.SegmentIndex == 5 then
			return math.random(3, 5)
		elseif treeData.SegmentIndex == 9 then
			return 2
		elseif treeData.SegmentIndex == 13 then
			return math.random(2, 3)
		end

		return 1
	end,

	Size = function(treeData)
		if treeData.SegmentIndex == 1 then
			local width = math.random(26, 28) / 10
			local size = Vector3.new(width, (math.random(45, 50) / 10), width)
			treeData.TrunkSize = size

			return size
		else
			return treeData.TrunkSize - (Vector3.new(0.1, 0.2, 0.1) * (treeData.SegmentIndex - 1))
		end
	end,

	CFrame = function(treeData)
		if treeData.SegmentIndex == 1 then
			CFrame.Angles(0, math.random(-180, 180), 0)
		end

		local finalCFrame = (treeData.LastBranch.CFrame - treeData.LastBranch.Position) 
			* CFrame.Angles(
				math.rad(math.random(-5, 5)),
				math.rad(math.random(-5, 5)),
				math.rad(math.random(-5, 5))
			)

		if treeData.SegmentIndex < 4 then
			finalCFrame = (treeData.LastBranch.CFrame - treeData.LastBranch.Position) 
				* CFrame.Angles(
					math.rad(math.random(-5, 5)),
					math.rad(math.random(-5, 5)),
					math.rad(math.random(-5, 5))
				)
		end

		if treeData.SegmentIndex == 5 then
			finalCFrame = finalCFrame
				* CFrame.Angles(
					0,
					math.rad((360 / treeData.TotalSplits) * treeData.SplitIndex),
					0
				) 
				* CFrame.Angles(
					math.rad(math.random(30, 35)),
					0,
					0
				)
		end

		if treeData.SegmentIndex > 8 and treeData.SegmentIndex < 10 and treeData.TotalSplits > 1 then
			finalCFrame = finalCFrame * CFrame.Angles(
				0,
				math.rad((360 / treeData.TotalSplits) * treeData.SplitIndex),
				0
			) 
				* CFrame.Angles(
					math.rad(math.random(35, 40)),
					0,
					0
				)
		end

		if treeData.SegmentIndex > 12 and treeData.SegmentIndex < 14 and treeData.TotalSplits > 1 then
			finalCFrame = finalCFrame * CFrame.Angles(
				0,
				math.rad((360 / treeData.TotalSplits) * treeData.SplitIndex),
				0
			) 
				* CFrame.Angles(
					math.rad(math.random(40, 45)),
					0,
					0
				)
		end

		return finalCFrame
	end,
}

return OakTree