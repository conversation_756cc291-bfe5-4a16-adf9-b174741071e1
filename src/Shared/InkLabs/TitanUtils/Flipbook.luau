-- Instances
local modules = script.Parent

-- Modules
local StopwatchCreator = require(modules.StopwatchCreator)

--[=[
    @class Flipbook

    Object that flips through textures on an image object
]=]
--[=[
    @type Textures {string}
    @within Flipbook
    A series of frames for a flipbook animation
]=]

local Flipbook = {}
Flipbook.__index = Flipbook

--[=[
    Creates a flipbook object

    @param imageObject -- The image object that will have it's texture flipped through (i.e a Decal)
    @param textureParamName -- The name of the texture parameter of the image object (Defaults to 'Texture') 
    @return Flipbook
]=]
function Flipbook.new(imageObject:Instance,textureParamName:string?)
    local self = setmetatable({},Flipbook)

    self.ImageObject = imageObject
    self.TextureParamName = textureParamName or 'Texture'

    return self
end

--[=[
    Loops through a table of images and sets the texture of image object according to the time passed

    @param textures Textures
    @param length -- The amount of time it takes for the flipbook to play
    @param multiplyAlpha (alpha: number) -> number -- A function that takes the current alpha and returns a modiefied version. Useful in conjunction with TweenService:GetValue() for custom easing.
    @param deleteOnEnd -- Should the flipbook delete itself after it's finished playing?
    @return Flipbook
]=]
function Flipbook:Play(textures:{string},length:number,multiplyAlpha,deleteOnEnd:boolean?)
    local flipbookStopwatch = StopwatchCreator.new()

    local function setNewTexture()
        local timePassed = flipbookStopwatch()
        local alpha = timePassed/length
        if multiplyAlpha then
            alpha = multiplyAlpha(alpha)
        end
        self.ImageObject[self.TextureParamName] = Flipbook.GetTextureFromAlpha(textures,alpha)
    end
    task.spawn(function()
        while not flipbookStopwatch:HasPassedTime(length) do
            setNewTexture()
            task.wait()
        end
        if deleteOnEnd then
            self:Delete()
        end
    end)
end

--[=[
    Clears the flipbook of all of it's values
]=]
function Flipbook:Delete()
    for i,_ in self do
        self[i] = nil
    end
end

--[=[
    Returns the most closely related frame given an alpha value

    @param textures Textures
    @param alpha -- The current progress of the flipbook. Ranges from 0-1
]=]
function Flipbook.GetTextureFromAlpha(textures:{string},alpha:number):string
    local textureCount = #textures
    local bestTextureNum = math.clamp(math.round(alpha*textureCount),1,textureCount)
    local bestTexture = textures[bestTextureNum]
    return bestTexture
end

--[=[
    Sets the image object's texture to the most closely related frame to the alpha value

    @param textures Textures
    @param alpha -- The current progress of the flipbook. Ranges from 0-1
]=]
function Flipbook:SetTextureFromAlpha(textures,alpha)
    self.ImageObject[self.TextureParamName] = Flipbook.GetTextureFromAlpha(textures,alpha)
end
return Flipbook