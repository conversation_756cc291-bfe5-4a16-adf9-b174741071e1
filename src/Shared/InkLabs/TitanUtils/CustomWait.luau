-- Instances
local modules = game.ReplicatedStorage.Modules

-- Modules
local StopwatchCreator = require(modules.StopwatchCreator)
local CustomWait = {}

function CustomWait.WaitUnlessValueChanged(tab,waitLength,index,value,callback)
	local waitSuccess = CustomWait.WaitUnlessTrue(waitLength,function()
		return tab[index] ~= value
	end)
	if waitSuccess and callback then
		callback()
	end
	return waitSuccess
end

-- Waits for the given time unless the callback returns true 
-- (Warning: The callback is called another time after the loop ends unless specified otherwise. This can cause unexpected behaviour)
function CustomWait.WaitUnlessTrue(waitLength,callback, noReturn)
	if type(waitLength) == 'function' then
		callback=waitLength
		waitLength=nil
	end
	local stopwatch = StopwatchCreator.new()
	while true do
		if (waitLength and stopwatch:HasPassedTime(waitLength)) or callback() then
			break
		end
		task.wait()
	end
	if not noReturn then
		return not callback()
	end
end

function CustomWait.RunTimeEvents(timeEvents:{time:number,func:any?},canContinue,eventReached,eventTimeName:string?,playbackSpeedMut:number?,loopCallback)
	playbackSpeedMut = playbackSpeedMut or 1
	eventReached = eventReached or function(eventNumber:string)
		timeEvents[eventNumber].func()
	end
	
	local lastEvent=0
	local stopwatch=StopwatchCreator.new()
	-- we wanna stop when we've reached the last time event
	while not timeEvents[#timeEvents].reached and (if canContinue then canContinue() else true) do
		for i=lastEvent+1,#timeEvents do
			local eventInfo = timeEvents[i]
			if stopwatch:HasPassedTime(eventInfo[eventTimeName or 'time']/playbackSpeedMut,true) then
				task.spawn(eventReached,i)
				eventInfo.reached=true
				lastEvent=i
			else
				break
			end
		end
		if loopCallback then
			loopCallback()
		end
		task.wait()
	end
end

function CustomWait.TweenUnless(tween:Tween,callback):boolean
	tween:Play()
	return CustomWait.WaitUnlessTrue(tween.TweenInfo.Time,callback)
end


return CustomWait

