local PartJoiner = {}

function PartJoiner.CalcWeldC0(cf0,cf1)
	return cf0:ToObjectSpace(cf1)
end

local function joinParts(name:string,p0:Part,p1:Part,c0:CFrame?,c1:CFrame?,parent:Instance?)
	local joinObject = Instance.new(name)
	joinObject.Part0 = p0
	joinObject.Part1 = p1
	if name~="WeldConstraint" then
		joinObject.C0 = c0 or PartJoiner.CalcWeldC0(p0.CFrame,p1.CFrame)
		if c1 then
			joinObject.C1 = c1
		end
	end
    
	joinObject.Parent = parent or p0
	return joinObject
end

function PartJoiner.AddMotor6D(p0:Part,p1:Part,c0:CFrame?,c1:CFrame?,parent:Instance?):Motor6D
	return joinParts("Motor6D",p0,p1,c0,c1,parent)
end

function PartJoiner.Weld(p0:Part,p1:Part,c0:CFrame?,c1:<PERSON>rame?,parent:Instance?):Weld
	return joinParts("Weld",p0,p1,c0,c1,parent)
end

function PartJoiner.AddWeldConstraint(p0:Part,p1:Part,c0:CFrame?,c1:CFrame?,parent:Instance?):WeldConstraint
	return joinParts("WeldConstraint",p0,p1,c0,c1,parent)
end

return PartJoiner