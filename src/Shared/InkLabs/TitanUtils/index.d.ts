interface ITitanEffects {
    RockLine: (props: {
        Start: CFrame,
        Distance: number,
        Amount: number,
        FadeInTime?: number,
        FadeOutTime?: number,
        Duration?: number,
        SideDistance?: number,
        Rotation?: number,
        Size?: Vector3
    }) => void,

    Crater: (props: {
        Center: CFrame,
        Radius: number,
        Amount: number,

        InnerSided?: boolean,
        BaseLength?: number,
        BaseHeight?: number,
        FadeInTime?: number,
        FadeOutTime?: number,
        Duration?: number,
        RemoveRandomness?: boolean,
        RaycastDistance?: number,
    }) => void,
}

interface ITitanCamera {
    Shake: (
        Origin: BasePart | Vector3, Intensity: number, Duration: number, FadeOut: TweenInfo, Distance: number,
        Speed?: number, FadeIn?: TweenInfo
    ) => void,
}

declare const TitanUtils: {
    Effects: ITitanEffects,
    CameraControl: ITitanCamera,
};

export = TitanUtils;