-- Services
local Lighting = game:GetService("Lighting")
local TweenService = game:GetService("TweenService")
local Debris = game:GetService('Debris')
-- Instances
local modules =script.Parent
local spawnedObjects = workspace:WaitForChild("Debris")

-- Modules
local GeneralUse = require(modules.GeneralUse)
local NumberHelper = require(modules.NumberHelper)
local StopwatchCreator = require(modules.StopwatchCreator)

local VFXHelp = {}

-- disables particles/trails and then deletes the part after all the paritcles dissappear
function VFXHelp.RemoveVFX(part,optSettings):number
	optSettings = optSettings or {}
	local noDisable=optSettings.noDisable
	local removeDelay=optSettings.removeDelay
	local ignoreBeams = optSettings.ignoreBeams
	if removeDelay then
		task.wait(removeDelay)
	end

	local highestTime = 0
	local function updateHighestLifetime(newTime)
		if newTime >= highestTime then
			highestTime=newTime
		end
	end
	if typeof(part) == 'Instance' then
		for _,object:(ParticleEmitter|Trail) in part:GetDescendants() do
			if object:IsA("ParticleEmitter")  then
				local maxLifetime = object.Lifetime.Max + (object:GetAttribute('EmitDelay') or 0) * (1/object.TimeScale)
				updateHighestLifetime(maxLifetime)
			elseif object:IsA('Trail') then
				updateHighestLifetime(object.Lifetime)
			end
		end
		if not noDisable then
			VFXHelp.DisableVFX(part,ignoreBeams)
		end
		Debris:AddItem(part,highestTime)
	else
		-- if the "part" variable is a table then we want to loop through the particles and remove them indivudally
		for _,object in part do
			if not noDisable then
				object.Enabled = false
			end
			local lifetime = object.Lifetime.Max
			updateHighestLifetime(lifetime)
			Debris:AddItem(object,lifetime)
		end
	end
	return highestTime
end

function VFXHelp.EmitFromModel(model,ignoreLight)
	local particleTab
	if type(model) == 'table' then
		particleTab=model
	else
		particleTab=model:GetDescendants()
	end
	for _,object in particleTab do
		if object:IsA("ParticleEmitter")  then
			local amount = object:GetAttribute("EmitCount") or object:GetAttribute("Amount")
			if amount then
				task.delay(object:GetAttribute('EmitDelay') or 0,function()
					object:Emit(amount)
				end)
			end
		elseif object:IsA("PointLight") and not ignoreLight then
			local goal = {}
			goal.Brightness = object.Brightness
			goal.Range = object.Range
			if object:FindFirstChild("ChangeBrightness") then
				goal.Brightness = object.ChangeBrightness.Value
			end
			local tweeninfo = TweenInfo.new(object.Timer.Value,Enum.EasingStyle.Linear,Enum.EasingDirection.Out,0,false,0)
			local tween = TweenService:Create(object,tweeninfo,goal)
			tween:Play()
		end
	end
end

function VFXHelp.SetSmokeColor(particlePart,optSettings)
	optSettings = optSettings or {}
	local raycastResult = optSettings.raycastResult
	local smokeBaseNames = optSettings.smokeBaseNames or {'Smoke'}
	local smokeParticles = {}

	for _,object in particlePart:GetDescendants() do
		if not object:IsA('ParticleEmitter') then
			continue
		end
		local isSmoke
		for _,baseName in smokeBaseNames do
			if not string.find(object.Name,baseName) then
				continue
			end

			isSmoke=true
			break
		end
		if not isSmoke then
			continue
		end
		table.insert(smokeParticles,object)
	end

	if #smokeParticles == 0 then
		return
	end

	if not raycastResult then
		raycastResult=GeneralUse.GetFirstCollidable(particlePart.Position,particlePart.CFrame.UpVector * -25)
	end
	
	if not raycastResult then
		return
	end

	for _,smokeParticle:ParticleEmitter in smokeParticles do
		smokeParticle.Color = ColorSequence.new(raycastResult.Instance.Color)
	end
	return raycastResult
end


function VFXHelp.ScaleParticle(baseNum,particle,givenNum,categories,baseParticle)
	categories = categories or {
		"Size";
		"Speed";
		"Acceleration";
	}

	for _,category in pairs(categories) do
		particle[category] = NumberHelper.ScaleValue(baseNum,(baseParticle or particle)[category],givenNum)
	end
	return particle
end

-- Scales the particle part and all the particles in it
function VFXHelp.ScaleParticlePart(particlePart:BasePart,scaleMut:number)
	if scaleMut == 1 then
		return
	end
	local function scalePart(part)
		part.Size*=scaleMut
	end
	local function scaleParticle(particle)
		VFXHelp.ScaleParticle(1,particle,scaleMut)
	end

	local scaleObjects = particlePart:GetDescendants()
	table.insert(scaleObjects,particlePart)
	for _,object in scaleObjects do
		if object:IsA('ParticleEmitter') then
			scaleParticle(object)
		elseif object:IsA('BasePart') then
			scalePart(object)
		end
	end
end

-- Emits particles and deletes them and the object part
function VFXHelp.EmitAndRemove(basePart:Instance|{ParticleEmitter},cf:CFrame?,optSettings:{}?)
	optSettings=optSettings or {}
	local dontClone = optSettings.dontClone
	local noAutoSmoke=optSettings.noAutoSmoke
	local maxFloorDist = optSettings.maxFloorDist
	local scaleMut = optSettings.scaleMut
	local raycastResult = optSettings.raycastResult
	if maxFloorDist then
		raycastResult = raycastResult or GeneralUse.GetFirstCollidable(cf.p+Vector3.new(0,.1,0),Vector3.new(0,-maxFloorDist,0))
		if not raycastResult then
			return
		end
		cf = cf and (cf - cf.p + raycastResult.Position)
	end
	
	local part
	if typeof(basePart) == 'Instance' then
		if dontClone then
			part=basePart
		else
			part=basePart:Clone()
		end
		if cf then
			part.CFrame = cf
			part.Parent =  spawnedObjects
		end
		if not noAutoSmoke then
			raycastResult = VFXHelp.SetSmokeColor(part,{raycastResult=raycastResult})
		end	
	else
		part=basePart
	end
	if scaleMut then
		VFXHelp.ScaleParticlePart(part,scaleMut)
	end
	VFXHelp.EmitFromModel(part,true)
	VFXHelp.RemoveVFX(part,{noDisable=true})
	return part,raycastResult
end

-- Adds particles to a character's body parts
function VFXHelp.AddParticlesToBody(target:Model,baseParticle:ParticleEmitter|{ParticleEmitter},stayLength:number?,validBodyParts:{string}?)
	local allParticles = {}
	for _,bodyPart in target:GetChildren() do
		if not bodyPart:IsA('BasePart') or bodyPart == target.PrimaryPart then
			continue
		end
		if validBodyParts and not table.find(validBodyParts,bodyPart.Name) then
			continue
		end

		local function addParticle(particle)
			local cloneParticle = particle:Clone()
			cloneParticle.Parent = bodyPart
			
			
			table.insert(allParticles,cloneParticle)
		end

		-- incase they send a folder of a particles instead of just 1
		if type(baseParticle) == "table" then
			for _,particle in baseParticle do
				addParticle(particle)
			end
		else
			addParticle(baseParticle)
		end
	end

	local function removeParticles()
		VFXHelp.RemoveVFX(allParticles,{removeDelay=stayLength})
	end
	if stayLength then
		removeParticles()
	else
		return removeParticles,allParticles
	end
end

function VFXHelp.ToggleVFX(part:BasePart,bool,ignoreBeams:boolean)
	for _,object in pairs(part:GetDescendants()) do
		if object:IsA("ParticleEmitter") or object:IsA('Trail') or (object:IsA('Beam') and not ignoreBeams) then
			object.Enabled = bool
		end
	end
end

function VFXHelp.EnableVFX(part:BasePart,ignoreBeams:boolean)
	VFXHelp.ToggleVFX(part,true,ignoreBeams)
end
function VFXHelp.DisableVFX(part:BasePart,ignoreBeams:boolean)
	VFXHelp.ToggleVFX(part,false,ignoreBeams)
end

function VFXHelp.RotateSlashParticles(slashRot:number,slashParticles:Part,startCF:CFrame,radius:number,slashTime:number,weldPart:BasePart?)
    local rotStopwatch = StopwatchCreator.new()

    local baseCF = weldPart and weldPart.CFrame

    local function calcCFrame()
        local alpha = rotStopwatch()/slashTime
        local newCF = startCF * CFrame.Angles(0,math.rad((-(180 *slashRot/slashRot)+ slashRot)*alpha),0) * CFrame.new(radius,0,0)
        if not baseCF then
            return newCF
        end

        -- if there is an weld part then we need to put the cframe in terms of where the weld part currently is
        -- that way the slash particles will remain in the right position even if the weld part moves
        local offsetCF = baseCF:ToObjectSpace(newCF)
        local finalCF = weldPart.CFrame:ToWorldSpace(offsetCF)
        return finalCF
    end

    task.spawn(function()
        while not rotStopwatch:HasPassedTime(slashTime) do
            slashParticles.CFrame = calcCFrame()
            task.wait()
        end
        VFXHelp.RemoveVFX(slashParticles)
    end)
end

function VFXHelp.CreateParticleScaler(particleParts:{BasePart})
	local particleScaler = {}
	local scaleParticles = {}
	
	for _,part in particleParts do
		for _, particle:ParticleEmitter in part:GetDescendants() do
			if not particle:IsA('ParticleEmitter') then
				continue
			end

			scaleParticles[particle] = particle:Clone()
		end
	end

	function particleScaler:Scale(alpha)
		for particle:ParticleEmitter,scaleParticle:ParticleEmitter in scaleParticles do
			VFXHelp.ScaleParticle(1,particle,alpha,nil,scaleParticle)
		end
	end
	return particleScaler
end

function VFXHelp.CreateBeamScaler(beamParts:{BasePart})
	local beamScaler = {}
	local scaleBeams = {}
	
	for _,part in beamParts do
		for _, beam:Beam in part:GetDescendants() do
			if not beam:IsA('Beam') then
				continue
			end

			scaleBeams[beam] = {
				CurveSize0 = beam.CurveSize0;
				CurveSize1 = beam.CurveSize1;
				Width0 = beam.Width0;
				Width1 = beam.Width1;
			}
		end
	end

	function beamScaler:Scale(alpha:number)
		for beam:Beam,scaleValues in scaleBeams do
			for valueName:string,value:number in scaleValues do
				beam[valueName] = alpha * value
			end
		end
	end
	
	return beamScaler
end
function VFXHelp.FlashScreen(flashSequence:{{settings:string|any; stayTime:number?}})
	local flash = Instance.new("ColorCorrectionEffect")
	flash.Parent = Lighting
	task.spawn(function()
		for _:number,valueTab in flashSequence do
			for index,value in valueTab.settings do
				flash[index] = value
			end

			task.wait(valueTab.stayTime or .07)
		end

		flash:Destroy()
	end)
end

return VFXHelp
