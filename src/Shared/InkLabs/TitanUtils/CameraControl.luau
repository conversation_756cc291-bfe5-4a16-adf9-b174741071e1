-- Services
local TweenService = game:GetService("TweenService")

-- Instances
local player = game.Players.LocalPlayer
local modules = script.Parent

-- Modules
local GeneralUse = require(modules.GeneralUse)
local TableHelper = require(modules.TableHelper)
local StopwatchCreator = require(modules.StopwatchCreator)
local camera

local CameraControl = {}


function CameraControl:UpdateCamera()
	camera = workspace.CurrentCamera
end

function CameraControl:SetCameraType(cameraType:Enum.CameraType?)
	cameraType = cameraType or Enum.CameraType.Custom 
	camera.CameraType = cameraType
	--[[coroutine.wrap(function()
		local loopCount = 0
		repeat
			camera.CameraType = cameraType
			if loopCount%10 == 0 then
				RunService.Stepped:Wait()
			end
		until camera.CameraType == cameraType
	end)()--]]
end

function CameraControl:ResetCamera()
	CameraControl:UpdateCamera()
	CameraControl:SetCameraType()
end

function CameraControl.FollowPartCFrame(part:BasePart,length:number?,keepCameraType:boolean?) -- follows the part's cframe for the give length of time (yields for the amount of time as well)
	CameraControl:UpdateCamera()
	CameraControl:SetCameraType(Enum.CameraType.Scriptable)
	local function update()
		camera.CFrame = part.CFrame
	end
	local followPartFunc:RBXScriptConnection = part.Changed:Connect(update)
	update()
	
	if length then 
		wait(length)
		CameraControl:UpdateCamera()
		followPartFunc:Disconnect()
		
		if not keepCameraType then
			CameraControl:ResetCamera()
		end
	else
		return followPartFunc -- if you decide to disable the function manually, make sure to run the ResetCamera function when you disconnect it
	end
end

local activeCameraShakers = {}
local cameraShakeActive = false

local function cameraShakeLoop()
	local char = player.Character or player.CharacterAdded:Wait()
	local humanoid = char.Humanoid
	while #activeCameraShakers>0 and char.Parent do
		local totalShakeStrength = Vector3.new()
		for _,shakeValue:Vector3Value in activeCameraShakers do
			totalShakeStrength+=shakeValue.Value
		end
		humanoid.CameraOffset = totalShakeStrength
		task.wait()
	end
	humanoid.CameraOffset = Vector3.new(0,0,0)
end
function bootCameraShake()
	if cameraShakeActive then
		return
	end
	cameraShakeActive = true
	task.spawn(function()
		cameraShakeLoop()
		cameraShakeActive = false
	end)
end


function CameraControl.ShakeScreen(epicenter:(Vector3|Part),shakeStrength:number,shakeTime:number,fadeOut:TweenInfo,shakeDist:number,shakeSpeed:number?,fadeIn:TweenInfo)
	local function calcShakeValue(shakeValue)
		return shakeValue
	end
	
	CameraControl.LoadShakeValue(activeCameraShakers,calcShakeValue,epicenter,shakeStrength,shakeTime,fadeOut,shakeDist,shakeSpeed,fadeIn)
	bootCameraShake()
end


function CameraControl.LoadShakeValue(shakeValueTable:{},calcShakeValue,epicenter:(Vector3|Part),shakeStrength:number,shakeTime:number,fadeOut:TweenInfo,shakeDist:number?,shakeSpeed:number?,fadeIn:TweenInfo?)
	shakeSpeed = shakeSpeed or .025
	shakeDist = shakeDist or 5
	local shakeValue = Instance.new('Vector3Value')
	table.insert(shakeValueTable,shakeValue)

	-- We want to account for the shake strength setting
	local strengthMut = 1

	local shakeStopwatch = StopwatchCreator.new(true)
	local function updateShake(getStrength)
		local function getValues()
			CameraControl:UpdateCamera()

			local shakePosition = if typeof(epicenter) == "Vector3" then epicenter else epicenter.CFrame.Position
			return calcShakeValue(getStrength(camera.CFrame.Position,shakePosition,shakeDist)*strengthMut)
		end
		
		-- We don't want to start another shake tween if the last one hasn't finished
		if not shakeStopwatch:HasPassedTime(shakeSpeed) then
			return
		end

		local tweenInfo = TweenInfo.new(shakeSpeed,Enum.EasingStyle.Linear,Enum.EasingDirection.Out)
		TweenService:Create(shakeValue,tweenInfo,{Value = Vector3.new(getValues(),getValues(),getValues())}):Play()		
	end

	task.spawn(function()
		GeneralUse:GetShakeOffset(shakeStrength,shakeTime,fadeOut,updateShake,{fadeIn = fadeIn})
		TableHelper.FindAndDestroy(shakeValueTable,shakeValue)
		shakeValue:Destroy()
	end)
end

return CameraControl
