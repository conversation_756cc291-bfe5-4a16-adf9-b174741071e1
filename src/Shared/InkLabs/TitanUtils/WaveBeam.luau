-- Modules
local WaveBeam = {}
WaveBeam.__index = WaveBeam

function WaveBeam.new(startPart:Part,endPart:Part,beams:{Beam},sectionCount:number,waveLengthAtCreation:number?)
	local self = setmetatable({},WaveBeam)
	self.StartPart = startPart
	self.EndPart = endPart


	waveLengthAtCreation = waveLengthAtCreation or self:GetWaveLength()
	local template = {}
	local startAttachments,endAttachments = {},{}
	local waveSections = {}
	for _,beam in beams do
		template[beam] = beam
	end
	self.WaveLengthAtCreation = waveLengthAtCreation or self:GetWaveLength()
	self.StartAttachments = startAttachments
	self.EndAttachments = endAttachments
	self.Sections = waveSections
	self.Template = template
	
	
	
	for sectionNum = 0,sectionCount-1 do
		local rotation = (math.pi*2)/sectionCount*sectionNum

		local function createAttachment(prefix:string,parent:Part)
			local attach = Instance.new("Attachment")
			attach.CFrame = CFrame.Angles(0,0,rotation)
			attach.Name = prefix .. "_Attachment_"..sectionNum
			attach.Parent = parent
			return attach
		end

		local startAttach,endAttach = createAttachment('Start',startPart),createAttachment('End',endPart)
		
		local sectionBeams = {}
		for baseBeam:Beam,_ in template do
			local beam = baseBeam:Clone()
			local curveSize = beam.CurveSize0
			beam.Attachment0 = startAttach
			beam.Attachment1 = endAttach
			beam.CurveSize0,beam.CurveSize1 = curveSize,-curveSize
			beam.Parent = startPart
			table.insert(sectionBeams,{
				beam = beam,
				baseBeam = baseBeam
			})
		end

		table.insert(waveSections,sectionBeams)
	end
	self:Update()
	return self
end

-- misc
function WaveBeam:GetWaveLength():number
	return (self.StartPart.Position - self.EndPart.Position).Magnitude
end

function WaveBeam:Update(sizeScaleMut:number)
	local waveLength = self:GetWaveLength()
	local scaleMut:number = waveLength/self.WaveLengthAtCreation

	sizeScaleMut = if sizeScaleMut then math.clamp(scaleMut/2,1,math.huge)*sizeScaleMut else scaleMut
	for _,sectionBeams in self.Sections do
		for _,beamInfo in sectionBeams do
			local templateBeam:Beam = self.Template[beamInfo.baseBeam]
			local beam:Beam = beamInfo.beam
			beam.TextureLength = templateBeam.TextureLength*scaleMut
			
			
			local curveSize,width = templateBeam.CurveSize0*sizeScaleMut,templateBeam.CurveSize0*sizeScaleMut
			beam.CurveSize0 = curveSize
			beam.CurveSize1 = -curveSize
			beam.Width0 = width
			beam.Width1 = width
		end
	end
end


return WaveBeam
