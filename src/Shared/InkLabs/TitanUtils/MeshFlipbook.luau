-- Services
local TweenService = game:GetService("TweenService")

-- Instances
local spawnedObjects = workspace.SpawnedObjects
local modules = script.Parent

-- Modules
local NumberHelper = require(modules.NumberHelper)
local Flipbook = require(modules.Flipbook)

-- Variables
local MeshFlipbook = {
	Slash0 = {
		[1] = "rbxassetid://13204898262",
		[2] = "rbxassetid://13204897736",
		[3] = "rbxassetid://13204897313",
		[4] = "rbxassetid://13204896833",
		[5] = "rbxassetid://13204896227",
		[6] = "rbxassetid://13204895744",
		[7] = "rbxassetid://13204895210",
		[8] = "rbxassetid://13204894750",
		[9] = "rbxassetid://13204894286",
		[10] = "rbxassetid://13204893811",
		[11] = "rbxassetid://13204893290",
		[12] = "rbxassetid://13204892795",
		[13] = "rbxassetid://13204892226",
		[14] = "rbxassetid://13204891702",
		[15] = "rbxassetid://13204891035",
		[16] = "rbxassetid://13204890584",
		[17] = "rbxassetid://13204890049",
		[18] = "rbxassetid://13204889468",
		[19] = "rbxassetid://13204888992"
	};
}

MeshFlipbook.__index = MeshFlipbook

function MeshFlipbook.new(meshName:string,textures:{string},weldPart:BasePart,cf:CFrame,size:Vector3,color:Color3)
	local self = setmetatable({},MeshFlipbook)
	size = size or Vector3.new(.5,0)
	color=color or Color3.fromRGB(330,510,498)
	local flipbookPart:Part = game.ReplicatedStorage.TitanAssets.MiscAssets.FlipbookMeshes[meshName]:Clone()
	local flipbookMesh:Mesh = flipbookPart.Mesh
	local flipbookDecal:Decal = flipbookPart.Decal
	local cfValue = Instance.new("CFrameValue")

	local function updateCF()
		local currentWeldPart = self.WeldPart
		local value = cfValue.Value
		
		local newCF = if currentWeldPart then currentWeldPart.CFrame:ToWorldSpace(value) else value
		flipbookPart.CFrame = newCF
	end
	local weldUpdate = weldPart and weldPart.Changed:Connect(function(property)
		if property ~= 'CFrame' then
			return
		end

		updateCF()
	end)

	cfValue.Changed:Connect(updateCF)
	
	local flipbook = Flipbook.new(flipbookDecal)
	self.Part = flipbookPart
	self.Mesh = flipbookMesh
	self.Decal = flipbookDecal
	self.Flipbook = flipbook
	self.Textures = textures
	self.CFValue = cfValue
	self.WeldPart = weldPart
	self.WeldPartStartCF = weldPart and weldPart.CFrame
	self.WeldUpdate = weldUpdate
	flipbookMesh.Scale = size
	flipbookDecal.Color3 = color
	cfValue.Value = self:ConvertCFrame(cf)
	flipbookPart.Parent = spawnedObjects
	
	self:SetTextureFromAlpha(0)
	return self
end

function MeshFlipbook:Tween(setting:string,tweenInfo:TweenInfo,tweenProperties:{[string]:any},dontPlay:boolean):Tween
	local tween = TweenService:Create(self[setting],tweenInfo,tweenProperties)
	if not dontPlay then
		tween:Play()
	end
	return tween
end

function MeshFlipbook:TweenDecal(tweenInfo:TweenInfo,tweenProperties:{[string]:any},dontPlay:boolean):Tween
	return self:Tween('Decal',tweenInfo,tweenProperties,dontPlay)
end

function MeshFlipbook:TweenPart(tweenInfo:TweenInfo,tweenProperties:{[string]:any},dontPlay:boolean):Tween
	return self:Tween('Part',tweenInfo,tweenProperties,dontPlay)
end

function MeshFlipbook:TweenMesh(tweenInfo:TweenInfo,tweenProperties:{[string]:any},dontPlay:boolean):Tween
	return self:Tween('Mesh',tweenInfo,tweenProperties,dontPlay)
end

function MeshFlipbook:TweenTransparency(transparency:number,tweenInfo:TweenInfo,dontPlay:boolean):Tween
	return self:TweenDecal(tweenInfo,{Transparency = transparency},dontPlay)
end

function MeshFlipbook:TweenCFrame(cframe:CFrame,tweenInfo:TweenInfo,dontPlay:boolean):Tween
	local finalCF = self:ConvertCFrame(cframe)
	return self:Tween('CFValue',tweenInfo,{Value = finalCF},dontPlay)
end

function MeshFlipbook:SetTransparency(transparency:number)
	self.Decal.Transparency = transparency
end

function MeshFlipbook:SetCFrame(cframe:CFrame)
	local finalCF = self:ConvertCFrame(cframe)
	self.CFValue.Value = finalCF
end

function MeshFlipbook:ConvertCFrame(cframe:CFrame)
	local weldPart = self.WeldPart
	local finalCF
	if weldPart then 
		finalCF = self.WeldPartStartCF:ToObjectSpace(cframe)
	else 
		finalCF = cframe 
	end
	return finalCF
end

function MeshFlipbook:TweenSize(size:Vector3,tweenInfo:TweenInfo,dontPlay:boolean):Tween
	return self:TweenMesh(tweenInfo,{Scale = size},dontPlay)
end

function MeshFlipbook:Play(length:number,multiplyAlpha,noAutoDestroy:boolean,textures:{string}?)
	self.WeldPartStartCF = self.WeldPart and self.WeldPart.CFrame
	self.Flipbook:Play(self.Textures or textures,length,multiplyAlpha,true)
	if not noAutoDestroy then
		task.delay(length,function()
			self:Destroy()
		end)
	end
end

function MeshFlipbook:SetTextureFromAlpha(alpha)
	self.Flipbook:SetTextureFromAlpha(self.Textures,alpha)
end

function MeshFlipbook:Destroy()
	self.CFValue:Destroy()
	self.Part:Destroy()
	if self.WeldUpdate then
		self.WeldUpdate:Disconnect()
	end
	for i,_ in self do
		self[i] = nil
	end
end


function MeshFlipbook.LoadSphereFlipbook(...)
	return MeshFlipbook.LoadFlipbook('Sphere',...)
end

function MeshFlipbook.CreateWind0Flipbook(...)
	return MeshFlipbook.new('Cone',MeshFlipbook.Wind0,...)
end

function MeshFlipbook.CreateWind1Flipbook(...)
	return MeshFlipbook.new('Sphere',MeshFlipbook.Wind1,...)
end

function MeshFlipbook.CreateWind2Flipbook(...)
	return MeshFlipbook.new('Sphere',MeshFlipbook.Wind2,...)
end

function MeshFlipbook.CreateWind3Flipbook(...)
	return MeshFlipbook.new('Sphere',MeshFlipbook.Wind3,...)
end


function MeshFlipbook.CreateDiceyWindFlipbook(...)
	return MeshFlipbook.new('Sphere',MeshFlipbook.DiceyWind0,...)
end

function MeshFlipbook.CreateSlashWind0Flippbok(...)
	return MeshFlipbook.new('Sphere',MeshFlipbook.SlashWind0,...)
end

function MeshFlipbook.CreateAnimatedWind(baseCFrame:CFrame,endSize:Vector3,heightBoost:number,endHeightBoost:number,windTime:number,flipbookCreateFunction,startTransparency:number?)
	local windCF = baseCFrame * CFrame.Angles(0,NumberHelper.RandomRotation(),0) * CFrame.new(0,heightBoost,0)
	local wind = (flipbookCreateFunction or MeshFlipbook.CreateWind2Flipbook)(nil,windCF,Vector3.new(0,endSize.Y,0),Color3.new(1,1,1))
	wind:Play(windTime)
	wind:TweenSize(endSize,TweenInfo.new(windTime,Enum.EasingStyle.Cubic))
	wind:SetTransparency(startTransparency or .95)
	wind:TweenCFrame(
		windCF*CFrame.Angles(0,math.pi/2 * NumberHelper.RandomReverseMutlipler(),0) * CFrame.new(0,endHeightBoost,0),
		TweenInfo.new(windTime)
	)

	return wind
end

return MeshFlipbook