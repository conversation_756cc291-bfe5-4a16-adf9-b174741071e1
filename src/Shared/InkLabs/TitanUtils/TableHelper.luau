local TableHelper = {}

--[[
    @name FindAndDestroy

    @description Looks for the index of the value in the table. If found, removes that index from the table. Currently only works for numbered non mixed tables

    @params [{[number]:any}] tab
        The table that will be searched

    @params [any] value
        The value that will be searched for

    @returns [boolean]
        If the index for the value was found in the table
]]
function TableHelper.FindAndDestroy(tab:{[number]:any},value:any):boolean
    local index =table.find(tab,value)
    if index then
        table.remove(tab,index)
        return true
    else
        return false
    end
end

--[[
    @name GetOrCreate

    @description Grabs the value from the table at that index. If the value is not found, a new value is created at that index

    @params [{[any]:any}] tab
        The table that will be searched

    @params [any] index
        The index for the value to be found

    @params [any?] substitute
        The value that will be substituted if a value isn't found. (Defaults to an empty table)
    
    @returns [any]
        The value grabbed
    
    @returns [boolean]
        If the value existed previously
]]

function TableHelper.GetOrCreate(tab:{[any]:any},index:any,substitute:any?):(any,boolean) -- gets value from table or creates of if the index doesn't exist
	local value = tab[index] 
	local valueExisted= true
	if value == nil then
		valueExisted=false
		value = substitute or {}
		tab[index] = value
	end
	return value,valueExisted
end

function TableHelper.GetAndDestroy(tab,index) -- grabs value from table and destroys index
	local value =tab[index]
	tab[index]=nil
	return value
end

function TableHelper.IsTableEmpty(tab:{}):boolean
	local isEmpty = true
	for _,_ in tab do
		isEmpty = false
		break
	end
	return isEmpty
end


-- Destroys a table's index if the value has nothing inside
function TableHelper.DestroyIfEmpty(tab:{},index:any):boolean
	local isEmpty = TableHelper.IsTableEmpty(tab[index])
	if isEmpty then
		tab[index] = nil
	end
	return isEmpty
end

return TableHelper