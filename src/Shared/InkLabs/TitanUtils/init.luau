if game:GetService("RunService"):IsServer() then
    return {}
end

local TitanUtils = {}

local generalUse = require(script.GeneralUse);
TitanUtils.General = {
    BasedValue = generalUse.basedValue,
    Round = generalUse.Round,
    CalculateWeldC0 = generalUse.CalcWeldC0,
    AddMotor6D = generalUse.AddMotor6D,
    AddWeld = generalUse.Weld,
    AddWeldConstraint = generalUse.AddWeldConstraint,
    GetTargetFromHit = generalUse.GetTargetFromHit,
    GetFirstCollidable = generalUse.GetFirstCollidable,
    ScaleParticle = generalUse.ScaleParticle,
    GetShakeOffset = generalUse.GetShakeOffset,
}

local cameraControl = require(script.CameraControl);
TitanUtils.CameraControl = {
    FollowPart = cameraControl.FollowPartCFrame,
    Shake = cameraControl.ShakeScreen,
}

local stopwatchCreator = require(script.StopwatchCreator);
TitanUtils.Stopwatch = {
    Reset = stopwatchCreator.Reset,
    GetPassedTime = stopwatchCreator.GetTimePassed,
    GetCurrentTime = stopwatchCreator.GetStopwatchTime,
    UpdateFreezeTimeSub = stopwatchCreator.UpdateFreezeTimeSub,
    Freeze = stopwatchCreator.Freeze,
    Unfreeze = stopwatchCreator.Unfreeze,
    HasPassedTime = stopwatchCreator.HasPassedTime,
    newTimer = stopwatchCreator.newTimer,
}

local baseEffects = require(script.BaseEffects);
TitanUtils.Effects = {
    RockLine = function(props: {
        Start: CFrame,
        Distance: number,
        Amount: number,

        FadeInTime: number?,
        FadeOutTime: number?,
        Duration: number?,

        SideDistance: number?,
        RotAngle: number?,
        BaseSize: Vector3?,
    })
        baseEffects.CreateRockLine(
            props.Start,
            props.Distance,
            props.Amount,

            props.FadeInTime or nil,
            props.FadeOutTime or nil,
            props.Duration or nil,

            props.SideDistance or nil,
            props.RotAngle or nil,
            props.BaseSize or nil
        );
    end,

    CreateRock = baseEffects.CreateRock,

    Crater = function(props: {
        Center: CFrame,
        Radius: number,
        Amount: number,

        InnerSided: boolean?,
        BaseLength: number?,
        BaseHeight: number?,
        FadeInTime: number?,
        FadeOutTime: number?,
        Duration: number?,
        RemoveRandomness: boolean?,
        RaycastDistance: number?,
    }) 
        baseEffects.GroundExpandV2(
            props.Center,
            props.Radius,
            props.Amount,

            {
                baseLength = props.BaseLength or nil,
                baseHeight = props.BaseHeight or nil,
                expandTime = props.FadeInTime or nil,
                baseDestroyWait = props.Duration or nil,
                destroyTime = props.FadeOutTime or nil,
                InnerSided = props.InnerSided or nil,
                removeRandomness = props.RemoveRandomness or nil,
                rayDist = props.RaycastDistance or nil,
            }
        );
    end,

    SurfaceImage = baseEffects.CreateSurfaceImage,
    GroundScorch = baseEffects.GroundScorch,
    GroundCrack = baseEffects.GroundCrack,
}

return TitanUtils