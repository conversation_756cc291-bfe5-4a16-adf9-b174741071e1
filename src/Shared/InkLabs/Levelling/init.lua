local Lib = {}

local EXP_PER_LEVEL = 100 --[[ See visual difference in here: https://www.desmos.com/calculator/tyylbrspwe ]]
local MAX_LEVEL_CAP = 500

-- Gets the needed EXP amount needed to reach a given level.
function Lib.GetRequiredEXPForLevel(Level : number) : number
	if not Level then return math.huge end
	return ((EXP_PER_LEVEL * 0.5) * ((Level) ^ 2)) + ((EXP_PER_LEVEL * 0.5) * (Level))
end

-- Gets the level based on the given EXP.
function Lib.GetLevelFromTotalEXP(TotalEXP : number) : number
	if not TotalEXP then return 1 end
	return math.min(math.floor(((-EXP_PER_LEVEL * 0.5) + math.sqrt(((EXP_PER_LEVEL * 0.5) ^ 2) + ((EXP_PER_LEVEL * 2) * TotalEXP))) / EXP_PER_LEVEL), MAX_LEVEL_CAP)
end

-- Returns the new level if the player can level up.
function Lib.GetNewLevel(CurrentLevel : number, TotalEXP : number) : number
	if not CurrentLevel then 
        CurrentLevel = 1 
    end

	if not TotalEXP then 
        TotalEXP = 0
    end
	
	local PredictedLevel = Lib.GetLevelFromTotalEXP(TotalEXP)
	local LevelsGained = math.floor(PredictedLevel - CurrentLevel)
	if LevelsGained <= 0 then return CurrentLevel end
	
	return PredictedLevel
end

return Lib