/**
 * Gets the total EXP required to reach a specific level.
 *
 * Uses the formula: EXP = 50 * level² + 50 * level
 * This creates a quadratic progression where higher levels require exponentially more EXP.
 *
 * @param level - The target level (1-500)
 * @returns The cumulative EXP needed to reach that level
 *
 * @example
 * ```ts
 * GetRequiredEXPForLevel(1);  // Returns 0
 * GetRequiredEXPForLevel(2);  // Returns 100
 * GetRequiredEXPForLevel(5);  // Returns 1000
 * ```
 */
export const GetRequiredEXPForLevel: (level: number) => number;

/**
 * Calculates what level a player should be based on their total accumulated EXP.
 *
 * This function uses the inverse of the EXP formula to determine the appropriate level.
 * The result is capped at MAX_LEVEL_CAP (500) and floored to get whole levels.
 *
 * @param totalEXP - The player's total accumulated experience points
 * @returns The level corresponding to that amount of EXP (1-500)
 *
 * @example
 * ```ts
 * GetLevelFromTotalEXP(0);   // Returns 1
 * GetLevelFromTotalEXP(100);   // Returns 2
 * GetLevelFromTotalEXP(500);   // Returns 3 (needs 600 for level 4)
 * GetLevelFromTotalEXP(1000);  // Returns 5
 * ```
 */
export const GetLevelFromTotalEXP: (totalEXP: number) => number;

/**
 * Determines if a player can level up and returns their new level.
 *
 * Compares the player's current level with what their level should be based on total EXP.
 * If no level up is possible, returns the current level unchanged.
 *
 * @param currentLevel - The player's current level
 * @param totalEXP - The player's total accumulated experience points
 * @returns The new level if leveling up is possible, otherwise the current level
 *
 * @example
 * ```ts
 * // Player is level 1 with 150 total EXP (enough for level 2)
 * GetNewLevel(1, 150);  // Returns 2
 *
 * // Player is level 2 with 350 total EXP (not enough for level 4)
 * GetNewLevel(2, 350);  // Returns 3
 *
 * // Check for level up
 * const newLevel = GetNewLevel(playerLevel, playerTotalEXP);
 * if (newLevel > playerLevel) {
 *   console.log(`Level up! ${playerLevel} → ${newLevel}`);
 * }
 * ```
 */
export const GetNewLevel: (currentLevel: number, totalEXP: number) => number;