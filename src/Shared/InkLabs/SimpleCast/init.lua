--!strict
--[[
	Version: 1.3.1
	how to use: https://devforum.roblox.com/t/simplecast-an-alternative-to-fastcast/2271321/2
	
	Copyright (C) 7/21/2023  seliso

    This program is free software: you can redistribute it and/or modify
    it under the terms of the GNU General Public License as published by
    the Free Software Foundation, either version 3 of the License, or
    (at your option) any later version.

    This program is distributed in the hope that it will be useful,
    but WITHOUT ANY WARRANTY; without even the implied warranty of
    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
    GNU General Public License for more details.

    You should have received a copy of the GNU General Public License
    along with this program.  If not, see <https://www.gnu.org/licenses/>.
]]

local RunService = game:GetService("RunService")

local MAXFRAMES = 1/120

--------------------------------------------------------------------

--inspired from partcache by xanthedragon

local CF_REALLY_FAR_AWAY = CFrame.new(0, 10e7, 0)
local ZERO_VECTOR = Vector3.new()

local CastCache = {}
CastCache.__index = CastCache

--------------------------------------------------------------------

export type CastData = {
	Object: BasePart,
	InitVelocity: Vector3,
	InitPosition: Vector3,
	LifeTime: number,
	TerminateOnHit: boolean,
	StartTime: number,
	Data: any
}

type CacheImpl = {
	__index: CacheImpl,
	new: (castObj: BasePart, customData: any, initCastCache: number, growthSize: number) -> CastCache,
	GetFromCache: (self: CastCache) -> CastData,
	ReturnToCache: (self: CastCache, castData: CastData) -> (),
	ExpandCache: (self: CastCache, size: number) -> (),
	Destroy: (self: CastCache) -> ()
}

type CacheProp = {
	Cache: {CastData},
	CacheDump: Folder,
	_dead: {CastData},
	_obj: BasePart,
	_growthSize: number,
	_customData: any
}

export type CastCache = typeof(setmetatable({} :: CacheProp, {} :: CacheImpl))

--keyof and indexof are from partcache 
local function keyOf(tbl: {}, value: any): any
	for index, obj in pairs(tbl) do
		if obj == value then
			return index
		end
	end
	return nil
end

local function indexOf(tbl: {}, value: any): any
	local fromFind = table.find(tbl, value)
	if fromFind then return fromFind end
	return keyOf(tbl, value)
end


function CastCache.new(castObj: BasePart, customData: any, initCastCache: number, growthSize: number): CastCache
	local folder = Instance.new("Folder")
	folder.Name = "CastCacheDump"
	folder.Parent = workspace

	castObj.CFrame = CF_REALLY_FAR_AWAY

	local self = setmetatable({
		Cache = table.create(initCastCache),
		CacheDump = folder,
		_dead = table.create(initCastCache),
		_obj = castObj,
		_growthSize = growthSize,
		_customData = customData
	} :: CacheProp, CastCache :: CacheImpl)

	self:ExpandCache(initCastCache)

	return self
end

function CastCache:GetFromCache(): CastData
	if #self._dead == 0  then
		self:ExpandCache(self._growthSize)
	end

	local castData = self._dead[#self._dead]

	--just in case
	if castData == nil then
		error("Unable to get CastData for: ".. self._obj.Name)
	end
	table.remove(self._dead, #self._dead)
	table.insert(self.Cache, castData)

	return castData
end

function CastCache:ReturnToCache(castData: CastData)
	local index = indexOf(self.Cache, castData)
	if index ~= nil then
		table.remove(self.Cache, index)
		table.insert(self._dead, castData)
		castData.Object.CFrame = CF_REALLY_FAR_AWAY
	end
end

function CastCache:ExpandCache(size: number)
	for i = 1, size do
		local obj = self._obj:Clone()
		obj.Parent = self.CacheDump

		table.insert(self._dead, {
			Object = obj,
			InitVelocity = ZERO_VECTOR,
			InitPosition = ZERO_VECTOR,
			LifeTime = 0,
			Data = table.clone(self._customData or {})
		})
	end
end

function CastCache:Destroy()
	for _, cast in pairs(self._dead) do
		cast.Object:Destroy()
		table.clear(cast.Data)
		table.clear(cast)
	end
	table.clear(self._dead)
	for _, cast in pairs(self.Cache) do
		cast.Object:Destroy()
		table.clear(cast.Data)
		table.clear(cast)
	end
	table.clear(self.Cache)
	self.CacheDump:Destroy()
	self._obj = nil
end

--------------------------------------------------------------------
--types

export type SimpleCastSettings = {
	MaxLifeTime: number,
	CacheSize: number,
	CacheGrowthSize: number,
	Gravity: Vector3,
	RaycastParam: RaycastParams,
	OnCastHit: (self: any, hitObj: RaycastResult, castData: CastData, newPosition: Vector3) -> (),
	CustomData: any,
	OnCastTerminate: (customData: CastData) -> (),
	OnCastMove: (self: any, customData: CastData, oldVec: Vector3, newVec: Vector3) -> (),
	TerminateOnHit: boolean?,
}

type Impl = {
	__index: Impl,

	GetPosition: (t: number, initVelo: Vector3, initPos: Vector3, gravity: Vector3) -> Vector3,
	GetVelocity: (t: number, initVelo: Vector3, gravity: Vector3) -> Vector3,
	FastMag: (vector: Vector3) -> number,

	new: (castObject: BasePart, simpleCastSettings: SimpleCastSettings, SharedCache: any) -> SimpleCast,

	FireCast: (self: SimpleCast, initVelocity: Vector3, initPosition: Vector3) -> CastData,
	CastTerminate: (cself: SimpleCast, castData: CastData) -> (),
	Raycast: (self: SimpleCast, startPoint: Vector3, endPoint: Vector3, length: number) -> RaycastResult,
	Destroy: (self: SimpleCast) -> (),

	_update: (self: SimpleCast, dt: number) -> (),
}

type Prop = {
	Gravity: Vector3,
	MaxLifeTime: number,

	_presim: any,
	_casts: CastCache,
	_hasSharedCast: boolean,
	_raycastParams: RaycastParams,

	_onCastHit: any,
	_onCastTerminate: any,
	_onCastMove: any,
	_terminateOnHit: boolean
}

export type SimpleCast = typeof(setmetatable({} :: Prop, {} :: Impl))

---------------------------------------------------------------------

local function lerp(a: number, b: number, t: number)
	return a * (1 - t) + b * t;
end

local function getPosition(t: number, initVelo: Vector3, initPos: Vector3, gravity: Vector3): Vector3
	return (gravity*0.5) * (t*t) + initVelo * t + initPos
end

local function getVelocity(t: number, initVelo: Vector3, gravity: Vector3): Vector3
	return gravity * t + initVelo
end

local function raycast(parms, origin: Vector3, newPos: Vector3, length: number): RaycastResult
	local rayOrigin = origin
	local rayDirection = (newPos - origin) * length
	return workspace:Raycast(rayOrigin, rayDirection, parms)
end


---------------------------------------------------------------------

local SimpleCast = {}
SimpleCast.__index = SimpleCast

SimpleCast.DefaultSettings = {
	MaxLifeTime = 2,
	CacheSize = 20,
	CacheGrowthSize = 10,
	Gravity = Vector3.new(0,-192,0),
	RaycastParam = RaycastParams.new(),
	OnCastHit = function(self, hitObj: RaycastResult, castData: CastData, newPosition: Vector3) 
		self:CastTerminate(castData)
	end,
	CustomData = {},
	OnCastTerminate = nil,
	OnCastMove = nil
}

function SimpleCast.GetPosition(t: number, initVelo: Vector3, initPos: Vector3, gravity: Vector3): Vector3
	return getPosition(t, initVelo, initPos, gravity)
end

function SimpleCast.GetVelocity(t: number, initVelo: Vector3, gravity: Vector3): Vector3
	return getVelocity(t, initVelo, gravity)
end

function SimpleCast.FastMag(vector: Vector3): number
	return vector:Dot(vector)
end

function SimpleCast.new(castObject: BasePart, simpleCastSettings: SimpleCastSettings, SharedCache: any)
	local self = setmetatable({
		Gravity = simpleCastSettings.Gravity,
		MaxLifeTime = simpleCastSettings.MaxLifeTime,

		_presim = nil,
		_casts = SharedCache or CastCache.new(castObject, simpleCastSettings.CustomData, simpleCastSettings.CacheSize, simpleCastSettings.CacheGrowthSize),
		_hasSharedCast = not (SharedCache == nil),
		_raycastParams = simpleCastSettings.RaycastParam,

		_onCastHit = simpleCastSettings.OnCastHit,
		_onCastTerminate = simpleCastSettings.OnCastTerminate,
		_onCastMove = simpleCastSettings.OnCastMove,
		_terminateOnHit = simpleCastSettings.TerminateOnHit ~= false;
	} :: Prop, SimpleCast :: Impl)
	self._raycastParams:AddToFilter({self._casts.CacheDump})
	local count = 0
	self._presim = RunService.PreSimulation:Connect(function(dt) 
		-- count += dt
		-- if count < MAXFRAMES then 
		-- 	return 
		-- end
		-- count -= MAXFRAMES
		self:_update(dt) 
	end)
	return self
end

function SimpleCast:FireCast(initVelocity: Vector3, initPosition: Vector3): CastData
	debug.profilebegin("SimpleCastFire")
	local newCast: CastData = self._casts:GetFromCache()
	newCast.Object.Position = initPosition
	newCast.InitVelocity = initVelocity
	newCast.InitPosition = initPosition
	newCast.LifeTime = 0
	newCast.StartTime = os.clock()
	debug.profileend()
	return newCast
end

function SimpleCast:CastTerminate(castData: CastData)
	debug.profilebegin("SimpleCastTerminate")
	if self._onCastTerminate then
		self._onCastTerminate(castData)
	end
	self._casts:ReturnToCache(castData)
	debug.profileend()
end

function SimpleCast:Destroy()
	self._presim:Disconnect()	
	if self._hasSharedCast == true then 
		return 
	end
	for _, cast in pairs(self._casts.Cache) do
		self._casts:ReturnToCache(cast)
	end
	self._casts:Destroy()
end


function SimpleCast:Raycast(startPoint: Vector3, endPoint: Vector3, length: number)
	return raycast(self._raycastParams, startPoint, endPoint, length)
end

function SimpleCast:_update(dt)	
	debug.profilebegin("SimpleCastUpdate")
	local timeNow = os.clock()

	for index, cast in ipairs(self._casts.Cache) do
		cast.LifeTime = timeNow - cast.StartTime
		local newVec = getPosition(cast.LifeTime, cast.InitVelocity, cast.InitPosition, self.Gravity)
		local oldVec = cast.Object.Position	
		local hitObj = self:Raycast(oldVec, newVec, 1)

		if timeNow >= cast.StartTime+self.MaxLifeTime or oldVec.Y <= workspace.FallenPartsDestroyHeight then
			self:CastTerminate(cast)
			continue
		end

		if hitObj ~= nil then
			self._onCastHit(self, hitObj, cast, newVec)

			if self._terminateOnHit then
				continue
			end
		end

        local dir = CFrame.new(newVec):PointToObjectSpace(oldVec).Unit
		cast.Object.CFrame = CFrame.lookAt(newVec, newVec + (dir * -1))

		if self._onCastMove then
			self._onCastMove(self, cast, oldVec, newVec)
		end
	end
	
	debug.profileend()
end

return SimpleCast