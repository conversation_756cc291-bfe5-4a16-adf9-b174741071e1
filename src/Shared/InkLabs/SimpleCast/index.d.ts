type TCastData = {
    Object: BasePart;
    InitVelocity: Vector3;
    InitPosition: Vector3;
    LifeTime: number;
    StartTime: number;
    Data: unknown;
};

type TSimpleCastSettings = {
    MaxLifeTime: number;
    CacheSize: number;
    CacheGrowthSize: number;
    Gravity: Vector3;
    RaycastParam: RaycastParams;
    CustomData: unknown;
    OnCastHit: (
        settings: TSimpleCastSettings, hitObj: RaycastResult, castData: TCastData, newPosition: Vector3) => void;
    OnCastTerminate: (settings: TSimpleCastSettings, customData: TCastData) => void;
    OnCastMove: (settings: TSimpleCastSettings, customData: TCastData, oldVec: Vector3, newVec: Vector3) => void;
    TerminateOnHit?: boolean
};

declare class SimpleCast {
    GetPosition: (time: number, initialVelocity: Vector3, initialPosition: Vector3, gravity: Vector3) => Vector3;
    FastMag: (vec: Vector3) => number;
    GetVelocity: (time: number, initialVelocity: Vector3, gravity: Vector3) => Vector3;

    constructor(castObject: BasePart, settings: TSimpleCastSettings, sharedCast?: unknown);

    FireCast(direction: Vector3, initialPosition: Vector3): TCastData;

    CastTerminate(castData: TCastData): void;

    Destroy(): void;

    Raycast(startPoint: Vector3, endPoint: Vector3, length: number): RaycastResult
}

export = SimpleCast;