local Players = game:GetService("Players")
local RunService = game:GetService("RunService")
-- CONSTANTS --
local States = {
	"Idle", "Run", "Walk", "Jump", "Fall", "Land", "Dead"
}

local DefaultIDs = {
	Idle = 14348929959,
	Run = 114436498541356,
	Walk = 123341190580865,
    Jump = 0,
    Fall = 0,
    Land = 0,
    Dead = 0,
}

-- CONTROLLERS --
local AnimationController = require(script.Parent.Animator)

-- MAIN --
local StateManager = {}
StateManager.__index = StateManager

function StateManager:StopAnimations()
	if not self.CurrentAnimation then return end

	AnimationController.StopAnimation(self.NPC, self.CurrentAnimation)
	self.CurrentAnimation = nil
end

function StateManager:ChangeAnimation(AnimationName : string, ManualDeletion : boolean?, CustomParameters : {}?)
	if not self.Animations[AnimationName] then return end
	if self.CurrentAnimation == AnimationName then return end
	if self.CurrentAnimation ~= nil then
		self:StopAnimations()
	end

	AnimationController.PlayAnimation(self.NPC, AnimationName, self.Animations[AnimationName], CustomParameters, ManualDeletion)
	self.CurrentAnimation = AnimationName
end

function StateManager.new(NPC : Model, Animations: {}?)
	local self = setmetatable({}, StateManager)
	self.NPC = NPC :: Model
	self.Humanoid = NPC:FindFirstChildOfClass("Humanoid") :: Humanoid
	self.CurrentState = "Idle" :: string
	self.CurrentAnimation = nil :: string?
    self.Animations = {}

    if Animations then
        for i, v in pairs(Animations) do
            self.Animations[i] = v
        end
    end

    for i, v in DefaultIDs do
        if not self.Animations[i] then
            self.Animations[i] = v
        end
    end

	if self.Humanoid and RunService:IsClient() then
		task.defer(function()
			while self.Humanoid do
				RunService.PostSimulation:Wait()
				if not NPC or not NPC:IsDescendantOf(workspace) then break end
                if Players.LocalPlayer:DistanceFromCharacter(self.NPC:GetPivot().Position) > 500 then 
                    self:StopAnimations() 
                    continue 
                end
				-- if NPC:GetAttribute("CurrentState") ~= "Idle" then continue end 
				
				local HRP : BasePart? = self.NPC and self.NPC:FindFirstChild("HumanoidRootPart")

				if self.Humanoid:GetState() == Enum.HumanoidStateType.Running then
					if HRP and HRP.AssemblyLinearVelocity.Magnitude > 5 and self.Humanoid.FloorMaterial ~= Enum.Material.Air then
						if HRP.AssemblyLinearVelocity.Magnitude <= 7 then
							self:ChangeState("Walk", self.Humanoid.WalkSpeed / 12)
							--warn("Walking")
						else
							--warn("Running")
							self:ChangeState("Run", self.Humanoid.WalkSpeed / 16)
						end

						--warn(HRP.AssemblyLinearVelocity.Magnitude)
					else
						--warn("Idling")
						self:ChangeState("Idle")
					end
				elseif self.Humanoid:GetState() == Enum.HumanoidStateType.Dead then
					self:ChangeState("Dead")
                elseif self.Humanoid:GetState() == Enum.HumanoidStateType.Jumping then
                    self:ChangeState("Jump")
                elseif self.Humanoid:GetState() == Enum.HumanoidStateType.Freefall then
                    self:ChangeState("Fall")
                elseif self.Humanoid:GetState() == Enum.HumanoidStateType.Landed then
                    self:ChangeState("Land")
				else
					self:ChangeState("Idle")
				end
			end
		end)
	end

	--warn("Init States:", self)
	return self	
end

function StateManager:ChangeState(newState : string, Speed : number?)
	if newState == self.CurrentState or self.CurrentState == "Dead" then return end
	if not self.NPC or not self.NPC:IsDescendantOf(workspace) then 
        print("Attempted to change state while not in the Workspace / Destroyed.", self.NPC) 
        return 
    end

	if newState == "Dead" then
		self.CurrentState = "Dead"
		self:StopAnimations()
		return
	end

	if table.find(States, newState) then
		self.CurrentState = newState
		self:ChangeAnimation(newState, true, {["Speed"] = Speed})
	end
end

return StateManager