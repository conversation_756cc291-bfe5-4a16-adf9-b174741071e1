declare const Rarities: {
    Common: "Common",
    Uncommon: "Uncommon",
    Rare: "Rare",
    Epic: "Epic",
    Legendary: "Legendary",
    Mythical: "Mythical",
    Secret: "Secret",
    Extraordinary: "Extraordinary",
    Celestial: "Celestial",
    Exclusive: "Exclusive"
};

type TRarityType = keyof typeof Rarities;

type ItemReturnType = {
    Item: string,
    Chance: number,
}

export type RandomItemExtra = {
    LuckBoost?: number,
    Chances?: Map<TRarityType, number>,
}

export const RandomColorHSV: (
    hueMin?: number,
    hueMax?: number,
    saturationMin?: number,
    saturationMax?: number,
    brightnessMin?: number,
    brightnessMax?: number
) => Color3;

export const GetRandomItem: (
    itemsList: Map<string, TRarityType>,
    extra?: RandomItemExtra
) => ItemReturnType;

export const GetItemsChances: (
    itemsList: Map<string, TRarityType>,
    extra?: RandomItemExtra
) => Array<ItemReturnType>;

export const GetSingleItemChance: (
    itemName: string,
    itemsList: Map<string, TRarityType>,
    extra?: RandomItemExtra
) => number;