-- DEPENDENCIES --
local TableUtil = require(script.TableUtil)

-- MAIN --
local RandomLib = {}

-- TYPES --
export type RarityType = 'Common' | 'Uncommon' | 'Rare' | 'Epic' | 'Legendary' | 'Mythical' | 'Secret' | 'Extraordinary' | 'Celestial' | 'Exclusive'

export type RandomItemExtra = {
	LuckBoost: number?,
	Chances: {[RarityType]: number}?
}

-- CONSTANTS --
local DefaultChances = {
	Common = 44, Uncommon = 25, Rare = 19, Epic = 10,
	Legendary = 1.8, Mythical = 0.1, Secret = 0.05,
	Extraordinary = 0.035, Celestial = 0.015, Exclusive = 0.01
}

-- Returns a random Color3
RandomLib.RandomColorHSV = function(HueMin : number?, HueMax : number?, SaturationMin : number?, SaturationMax : number?, BrightnessMin : number?, BrightnessMax : number?) : Color3
	local Default = {0, 1} -- Hue<PERSON><PERSON>, HueMax, Others
	
	-- Parameters Validation
	HueMin = HueMin ~= nil and typeof(HueMin) == 'number' and HueMin or Default[1]
	HueMax = HueMax ~= nil and typeof(HueMax) == 'number' and HueMax >= HueMin and HueMax or Default[2]
	SaturationMin = SaturationMin ~= nil and typeof(SaturationMin) == 'number' and SaturationMin or Default[1]
	SaturationMax = SaturationMax ~= nil and typeof(SaturationMax) == 'number' and SaturationMax >= SaturationMin and SaturationMax or Default[2]
	BrightnessMin = BrightnessMin ~= nil and typeof(BrightnessMin) == 'number' and BrightnessMin or Default[1]
	BrightnessMax = BrightnessMax ~= nil and typeof(BrightnessMax) == 'number' and BrightnessMax >= BrightnessMin and BrightnessMax or Default[2]
	
	local RNG = Random.new(math.randomseed(tick()))
	
	local Hue = RNG:NextNumber(HueMin, HueMax)
	local Saturation = RNG:NextNumber(SaturationMin, SaturationMax)
	local Brightness = RNG:NextNumber(BrightnessMin, BrightnessMax)
	
	return Color3.fromHSV(Hue, Saturation, Brightness)
end

-- Handles the multiplication of luck (Increases chance of higher rarity items)
local function getRarityMultipliers(Chances: {[RarityType]: number}): {[RarityType]: number}
	local sortedKeys = TableUtil.Keys(Chances)
	table.sort(sortedKeys, function(a, b) 
        return Chances[a] > Chances[b] 
    end)

	local Multipliers = {}
	local totalRarities = 0
	for order, rarity in ipairs(sortedKeys) do
		Multipliers[rarity] = order
		totalRarities += 1
	end
	
	for _, rarity in ipairs(sortedKeys) do
		Multipliers[rarity] /= totalRarities
	end

	return Multipliers
end

-- Returns a random (based on chance) item from the ItemsList
RandomLib.GetRandomItem = function(ItemsList: {[string]: RarityType}, Extra: RandomItemExtra?) : {Item : string, Chance : number}
	if not ItemsList then return end

	local Chances = Extra and Extra.Chances or DefaultChances
	local LuckBoost = Extra and Extra.LuckBoost or 0
	local Multipliers = getRarityMultipliers(Chances)

	local ValidRewards, TotalChance = {}, 0
	for ItemName, ItemRarity in pairs(ItemsList) do
		local WeightedChance = (Chances[ItemRarity] or 0) * (1 + (LuckBoost * (Multipliers[ItemRarity] or 0)))
		TotalChance += WeightedChance
		table.insert(ValidRewards, {Item = ItemName, Chance = WeightedChance})
	end

	local Target, CumulativeChance = math.random() * TotalChance, 0
	for _, RewardData in ipairs(ValidRewards) do
		CumulativeChance += RewardData.Chance
		if Target <= CumulativeChance then
			return RewardData
		end
	end
end

-- Returns a list of the real chances of the items to get picked by GetRandomItem function
RandomLib.GetItemsChances = function(ItemsList: {[string]: RarityType}, Extra: RandomItemExtra?) : {{Item : string, Chance : number}}
	if not ItemsList then return {} end

	local Chances = Extra and Extra.Chances or DefaultChances
	local LuckBoost = Extra and Extra.LuckBoost or 0
	local Multipliers = getRarityMultipliers(Chances)

	local ValidRewards, TotalChance = {}, 0
	for ItemName, ItemRarity in pairs(ItemsList) do
		local WeightedChance = (Chances[ItemRarity] or 0) * (1 + (LuckBoost * (Multipliers[ItemRarity] or 0)))
		TotalChance += WeightedChance
		table.insert(ValidRewards, {Item = ItemName, Chance = WeightedChance})
	end

	local ItemChances = {}
	for _, RewardData in ipairs(ValidRewards) do
		local ChancePercentage = (RewardData.Chance / TotalChance)
		table.insert(ItemChances, {Item = RewardData.Item, Chance = ChancePercentage})
	end

	return ItemChances
end

-- Returns the chance of a given item based on the parameters
RandomLib.GetSingleItemChance = function(ItemName : string, ItemsList: {[string]: RarityType}, Extra: RandomItemExtra?) : number
	if not ItemsList or not ItemName then return 0 end

	local Chances = Extra and Extra.Chances or DefaultChances
	local LuckBoost = Extra and Extra.LuckBoost or 0
	local Multipliers = getRarityMultipliers(Chances)

	local ValidRewards, TotalChance = {}, 0
	for itemNameIndex, ItemRarity in pairs(ItemsList) do
		local WeightedChance = (Chances[ItemRarity] or 0) * (1 + (LuckBoost * (Multipliers[ItemRarity] or 0)))
		TotalChance += WeightedChance
		table.insert(ValidRewards, {Item = itemNameIndex, Chance = WeightedChance})
	end

	local ItemChances = {}
	for _, RewardData in ipairs(ValidRewards) do
		local ChancePercentage = (RewardData.Chance / TotalChance)
		table.insert(ItemChances, {Item = RewardData.Item, Chance = ChancePercentage})
	end

	for _, v in ItemChances do
		if v.Item == ItemName then
			return v.Chance
		end
	end

	return 0
end

return RandomLib