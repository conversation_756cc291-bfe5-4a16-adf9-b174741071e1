import {CharacterModel} from "Shared/Components/CharacterModel";
import {Signal} from "../Network";

type TSpatialOption = "InBox" | "InRadius" | "InPart" | "Magnitude";
type TLookingFor = "Humanoid" | "Object";

export type DotProductRequirement = {
    DotProduct: number;
    PartForVector: BasePart;
    VectorType?: "LookVector" | "UpVector" | "RightVector";
    Negative?: boolean;
}

export type THitboxClassParams = {
    SizeOrPart: Vector3 | number | BasePart;
    SpatialOption?: TSpatialOption;
    InitialPosition?: CFrame;
    Blacklist?: unknown[];
    DebounceTime?: number;
    LookingFor?: TLookingFor;
    DotProductRequirement?: DotProductRequirement;
    UseClient?: boolean;
    ID?: string | number;
    VelocityPrediction?: boolean;
    Debug?: boolean;
    Debris?: number;
}

declare class THitboxClass {
    Size: Vector3 | number;
    Mode: "Magnitude" | "Part";
    SpatialOption: TSpatialOption;
    LookingFor: TLookingFor;
    DebounceTime: number;
    Part?: BasePart;
    Position: CFrame;
    DotProductRequirement?: DotProductRequirement;
    TaggedChars: Map<CharacterModel, boolean>;
    TaggedObjects: Map<BasePart, boolean>;
    ID?: string | number;
    TickVal: number;
    Blacklist?: unknown[];
    SendingChars: CharacterModel[];
    SendingObjects: BasePart[];
    DelayThreads: thread[];
    HitSomeone: Signal<CharacterModel>;
    HitObject: Signal<BasePart>;
    RunServiceConnection?: RBXScriptConnection;
    ClientConnection?: RBXScriptConnection;
    PartWeld?: BasePart;
    PartWeldOffset?: CFrame;
    Client?: Player;
    VelocityPrediction?: boolean;
    DebugMode: boolean;
    Lifetime: number;

    constructor(params: THitboxClassParams);

    ClearTaggedChars(): void;

    Start(): void;

    Stop(): void;

    SetPosition(position: CFrame): void;

    Destroy(): void;

    WeldToPart(part: BasePart, offset?: CFrame): void;

    Unweld(): void;

    ChangeWeldOffset(offset: CFrame): void;

    SetVelocityPrediction(state: boolean): void;

    SetDebug(state: boolean): void;
}

declare const ClearClientHitboxes: (client: Player) => Callback;
declare const ClearHitboxesWithID: (id: string | number) => Callback;
declare const GetHitboxCache: () => THitboxClass[];

export {
    THitboxClass,
    ClearClientHitboxes,
    ClearHitboxesWithID,
    GetHitboxCache
};