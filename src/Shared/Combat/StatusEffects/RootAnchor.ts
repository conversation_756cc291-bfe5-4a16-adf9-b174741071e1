import {Players} from "@rbxts/services";
import {Character, StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import {GetRootPart} from "../Common";

@StatusEffectDecorator
export class RootAnchor extends StatusEffect {
    constructor(character: Character) {
        super(character);
        this.DestroyOnEnd = true;
    }

    protected OnStartServer() {
        this.ToggleAnchor(true);
    }

    protected OnStartClient() {
        super.OnStartClient();
        if (this.Player !== Players.LocalPlayer)
            return;

        this.ToggleAnchor(true);
    }

    protected OnEndServer() {
        super.OnEndServer();
        this.ToggleAnchor(false);
    }

    protected OnEndClient() {
        super.OnEndClient();
        if (this.Player !== Players.LocalPlayer)
            return;
        this.ToggleAnchor(false);
    }

    private ToggleAnchor(value: boolean) {
        const rootPart = GetRootPart(this.Character.Instance);
        if (rootPart === undefined)
            return;

        rootPart.Anchored = value;
        rootPart.AssemblyLinearVelocity = Vector3.zero;
    }
}