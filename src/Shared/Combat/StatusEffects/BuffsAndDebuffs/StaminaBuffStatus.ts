import {Character, StatusEffectDecorator} from "@rbxts/wcs";
import {AttributeStatus} from "../AttributeStatus";
import {StaminaStatusAttribute} from "./Attributes";

@StatusEffectDecorator
export class StaminaBuffStatus extends AttributeStatus {
    constructor(character: Character, value: number) {
        super(character, StaminaStatusAttribute, value);
    }
}

@StatusEffectDecorator
export class StaminaDebuffStatus extends StaminaBuffStatus {
    constructor(character: Character, value: number) {
        super(character, value);
    }
}