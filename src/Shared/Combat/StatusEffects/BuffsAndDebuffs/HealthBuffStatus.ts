import { Character, StatusEffectDecorator } from "@rbxts/wcs";
import { AttributeStatus } from "../AttributeStatus";
import { HealthStatusAttribute } from "./Attributes";

@StatusEffectDecorator
export class HealthBuffStatus extends AttributeStatus {
    constructor(character: Character, value: number) {
        super(character, HealthStatusAttribute, value);
    }
}

@StatusEffectDecorator
export class HealthDebuffStatus extends HealthBuffStatus {
    constructor(character: Character, value: number) {
        super(character, value);
    }
}