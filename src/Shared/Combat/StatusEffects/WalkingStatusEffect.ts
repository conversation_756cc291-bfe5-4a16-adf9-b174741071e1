import {Character, StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import {StatusPriorities} from "./StatusPriorities";

@StatusEffectDecorator
export class WalkingStatusEffect extends StatusEffect {
    constructor(character: Character) {
        super(character);
        this.DestroyOnEnd = true;
    }
    protected OnStartServer() {
        super.OnStartServer();
        this.SetHumanoidData({
            WalkSpeed: [11, "Set"]
        }, StatusPriorities.Running + 0.5);
    }
}