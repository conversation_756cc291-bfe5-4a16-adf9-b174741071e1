import {Players, RunService} from "@rbxts/services";
import {Character, StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import RemoveStatusesOfType from "../../Functions/RemoveStatusesOfType";

import {GetRootPart, GetTargetPositionFromCharacter} from "../Common";
import {RootAnchor} from "./RootAnchor";
import {StatusPriorities} from "./StatusPriorities";

@StatusEffectDecorator
export class LookAtTarget extends StatusEffect {
    public readonly DestroyOnEnd = true;
    private readonly horizontalOnly: boolean;
    private anchorStatus: RootAnchor | undefined;
    private connection: RBXScriptConnection | undefined;

    constructor(character: Character, horizontalOnly: boolean = false) {
        super(character);
        this.horizontalOnly = horizontalOnly;
    }

    protected OnStartClient() {
        super.OnStartClient();
        if (this.Player === undefined || this.Player !== Players.LocalPlayer)
            return;

        this.connection = RunService.PostSimulation.Connect(() => {
            this.UpdatePosition();
        });

        this.Janitor.Add(() => this.connection?.Disconnect());
    }

    protected OnStartServer() {
        super.OnStartServer();
        const rootPart = GetRootPart(this.Character.Instance);
        if (rootPart === undefined)
            return;

        this.SetHumanoidData({
            AutoRotate: [false, "Set"]
        }, StatusPriorities.RootAnchor + 1);

        this.connection = RunService.PostSimulation.Connect(() => {
            this.UpdatePosition();
        });

        RemoveStatusesOfType(this.Character, RootAnchor);
        this.anchorStatus = new RootAnchor(this.Character);
        this.anchorStatus.Start();
        this.Janitor.Add(() => this.anchorStatus?.Destroy());
        this.Janitor.Add(() => this.connection?.Disconnect());
    }

    protected OnEndClient() {
        super.OnEndClient();
        this.connection?.Disconnect();
    }

    protected OnEndServer() {
        super.OnEndServer();
        this.anchorStatus?.Destroy();
        this.connection?.Disconnect();
    }

    private UpdatePosition() {
        const rootPart = GetRootPart(this.Character.Instance);
        if (rootPart === undefined) {
            return;
        }

        const mousePosition = GetTargetPositionFromCharacter(this.Character.Instance as Model);
        if (mousePosition === undefined) {
            return;
        }

        let finalPosition = mousePosition;
        if (this.horizontalOnly)
            finalPosition = new Vector3(finalPosition.X, rootPart.Position.Y, finalPosition.Z);

        if (RunService.IsClient())
            rootPart.CFrame = CFrame.lookAt(rootPart.Position, finalPosition);
        else {
            rootPart.CFrame = CFrame.lookAt(rootPart.Position, finalPosition);
        }
    }
}