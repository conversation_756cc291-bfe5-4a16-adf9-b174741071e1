import { Character, StatusEffect, StatusEffectDecorator } from "@rbxts/wcs";

@StatusEffectDecorator
export class AttributeStatus extends StatusEffect {
    public readonly AttributeName: string = "WHATTHEFUCKWHYNOTWORKKK";
    public Value: number = 0;
    private oldValue: number = 0;

    constructor(character: Character, attributeName: string, value: number) {
        super(character);
        this.AttributeName = attributeName;
        this.Value = value;
    }

    public ChangeValue(value: number) {
        this.oldValue = this.Value;
        this.Value = value;
        this.ApplyAttributeStatus();
    }

    public GetValue() {
        return this.Value;
    }

    protected OnStartServer() {
        super.OnStartServer();
        this.ApplyAttributeStatus();
    }

    protected OnEndServer() {
        super.OnEndServer();
        const current = this.Character.Instance.GetAttribute(this.AttributeName) as number | undefined ?? 0;
        const newValue = current - this.Value;
        this.Character.Instance.SetAttribute(this.AttributeName, newValue);
    }

    private ApplyAttributeStatus() {
        let current = this.Character.Instance.GetAttribute(this.AttributeName) as number | undefined ?? 0;

        // Remove the old value if it exists
        if (this.oldValue !== 0) {
            current = current - this.oldValue;
        }

        // Add the new value
        const newValue = current + this.Value;
        this.Character.Instance.SetAttribute(this.AttributeName, newValue);
    }
}