import { StatusEffect, StatusEffectDecorator } from "@rbxts/wcs";

@StatusEffectDecorator
export class NoMobilityStatus extends StatusEffect {
    protected OnStartClient(): void {
        super.OnStartClient();
        this.SetHumanoidData({
            WalkSpeed: [0, "Set"],
            JumpHeight: [0, "Set"],
            JumpPower: [0, "Set"]
        }, 3);
    }

    protected OnEndClient(): void {
        super.OnEndClient();
    }
}