import {Character, StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import {RagdollTag} from "../../Resources/Tags";
import {GetRootPart} from "../Common";

@StatusEffectDecorator
export class RagdollStatus extends StatusEffect {
    constructor(character: Character) {
        super(character);
        this.DestroyOnEnd = true;
    }

    protected OnStartServer() {
        super.OnStartServer();
        if (GetRootPart(this.Character.Instance) === undefined)
            return;

        this.Character.Instance.AddTag(RagdollTag);
    }

    protected OnEndServer() {
        super.OnEndServer();
        this.Character.Instance.RemoveTag(RagdollTag);
    }
}