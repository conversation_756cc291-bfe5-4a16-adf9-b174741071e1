import {StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import {GetCharacterStamina} from "../../Functions/CombatUtilities";
import {StaminaAttributeName} from "../../Resources/Attributes";
import {InvisibleCharacterTag} from "../../Resources/Tags";

@StatusEffectDecorator
export class InvisibilityStatus extends StatusEffect {
    protected OnStartServer() {
        super.OnStartServer();
        this.Character.Instance.AddTag(InvisibleCharacterTag);
        const diedConnection = this.Character.Humanoid.Died.Once(() => {
            this.End();
        });

        const staminaEndedConnection = this.Character.Instance.GetAttributeChangedSignal(StaminaAttributeName)
            .Connect(() => {
                if (GetCharacterStamina(this.Character.Instance) <= 1)
                    this.End();
            });

        this.Janitor.Add(diedConnection);
        this.Janitor.Add(staminaEndedConnection);
    }

    protected OnEndServer() {
        super.OnEndServer();
        this.Character.Instance.RemoveTag(InvisibleCharacterTag);
    }
}