import {Character, StatusEffectDecorator} from "@rbxts/wcs";
import {CharacterModifier} from "./CharacterModifier";
import {TModifierStackScalingType, TModifierType} from "./Types";

@StatusEffectDecorator
export class CharacterHealthModifier extends CharacterModifier<number> {
    constructor(character: Character, modifierType: TModifierType = "Buff") {
        super(character, `HealthModifier`, modifierType);
    }
    protected OnStackApply(value: number, scalingType: TModifierStackScalingType): void {
        const scaledValue = this.GetStackScaledValue(value, scalingType);
        const humanoid = this.Character.Humanoid;
        if (humanoid === undefined)
            return;

        const currentHealth = humanoid.Health;
        const currentHealthPercent = currentHealth / humanoid.MaxHealth;

        if (scaledValue > 0)
            humanoid.MaxHealth += scaledValue;
        else {
            humanoid.MaxHealth -= scaledValue;
        }

        humanoid.Health = math.clamp(humanoid.MaxHealth * currentHealthPercent, 1, humanoid.MaxHealth);
    }

    protected OnStackRemove(value: number, scalingType: TModifierStackScalingType): void {
        const scaledValue = this.GetStackScaledValue(value, scalingType);
        const humanoid = this.Character.Humanoid;
        if (humanoid === undefined)
            return;

        const currentHealth = humanoid.Health;
        const currentHealthPercent = currentHealth / humanoid.MaxHealth;

        if (scaledValue < 0)
            humanoid.MaxHealth += scaledValue;
        else {
            humanoid.MaxHealth -= scaledValue;
        }

        humanoid.Health = humanoid.MaxHealth * currentHealthPercent;
    }
}