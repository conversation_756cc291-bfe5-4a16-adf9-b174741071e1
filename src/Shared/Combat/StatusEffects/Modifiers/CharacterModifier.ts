import {Janitor} from "@rbxts/janitor";
import {HttpService} from "@rbxts/services";
import {Character, StatusEffect} from "@rbxts/wcs";
import {ExponentialStacking, HyperbolicStacking, LinearStacking} from "../../../Extensions/Math/StackingUtilities";
import {StopWatch} from "../../../Libraries/StopWatch";
import {TModifierStackScalingType, TModifierType} from "./Types";

interface IModifierStack {
    Id: string;
    Watch: StopWatch;
}

export abstract class CharacterModifier<TStackValue> extends StatusEffect {
    public readonly Id: string;
    public Stacks = [] as IModifierStack[];
    public StacksJanitor = new Janitor();
    public EffectInterval: number = 1;
    public readonly ModifierType: TModifierType;

    protected constructor(character: Character, id: string, modifierType: TModifierType) {
        super(character);
        this.ModifierType = modifierType;
        this.Id = id;
        this.Janitor.Add(this.StacksJanitor);
    }

    protected abstract OnStackApply(value: TStackValue, scalingType: TModifierStackScalingType): void;
    protected abstract OnStackRemove(value: TStackValue, scalingType: TModifierStackScalingType): void;
    protected OnStackInterval(): void {
        // Nothing
    }

    protected GetStackScaledValue(value: number, scalingType: TModifierStackScalingType, stackCap?: number) {
        const stackAmount = math.clamp(this.Stacks.size(), 1, stackCap ?? math.huge);
        switch (scalingType) {
            case "Exponential":
                return ExponentialStacking(value, stackAmount);
            case "Hyperbolic":
                return HyperbolicStacking(value, stackAmount);
            default:
                return LinearStacking(value, stackAmount);
        }
    }

    // Adds a new stack to the modifier, and returns its Id for future removal, if necessary.
    public AddStack(stackValue: TStackValue, stackScalingType?: TModifierStackScalingType, duration?: number, stackId?: string) {
        stackId ??= HttpService.GenerateGUID(false);
        stackScalingType ??= "Linear";
        duration ??= math.huge;
        const stack: IModifierStack = {
            Id: stackId,
            Watch: new StopWatch(duration, {
                OnStart: () => this.OnStackApply(stackValue, stackScalingType),
                OnEnd: () => {
                    this.OnStackRemove(stackValue, stackScalingType);
                    this.Stacks = this.Stacks.filter(x => x.Id !== stackId);
                },
                OnInterval: () => this.OnStackInterval()
            }, this.EffectInterval, true)
        };

        this.Stacks.push(stack);
        this.StacksJanitor.Add(() => {
            stack.Watch.Destroy();
        });
        stack.Watch.Start();
        return stackId;
    }

    // Removes a stack from the modifier.
    public RemoveStack(stackId: string) {
        if (this.Stacks.isEmpty())
            return;

        const stackToRemove = this.Stacks.find(x => x.Id === stackId);
        stackToRemove?.Watch.Destroy();
    }

    public ResetStacks() {
        this.StacksJanitor.Cleanup();
    }
}