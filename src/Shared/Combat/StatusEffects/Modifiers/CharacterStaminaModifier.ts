import {Character, StatusEffectDecorator} from "@rbxts/wcs";
import {GetCharacterMaxStamina, GetCharacterStamina} from "../../../Functions/CombatUtilities";
import {MaxStaminaAttributeName, StaminaAttributeName} from "../../../Resources/Attributes";
import {CharacterModifier} from "./CharacterModifier";
import {TModifierStackScalingType, TModifierType} from "./Types";

@StatusEffectDecorator
export class CharacterStaminaModifier extends CharacterModifier<number> {
    constructor(character: Character, modifierType: TModifierType = "Buff") {
        super(character, `StaminaModifier`, modifierType);
    }

    protected OnStackApply(value: number, scalingType: TModifierStackScalingType): void {
        const scaledValue = this.GetStackScaledValue(value, scalingType);
        const model = this.Character.Instance;
        if (model === undefined)
            return;

        const current = GetCharacterStamina(model);
        const currentMax = GetCharacterMaxStamina(model);
        const currentPercent = current / currentMax;

        if (scaledValue > 0) {
            model.SetAttribute(MaxStaminaAttributeName, currentMax + scaledValue);
        } else {
            model.SetAttribute(MaxStaminaAttributeName, currentMax - scaledValue);
        }

        const newMax = GetCharacterMaxStamina(model);
        const clampedValue = math.clamp(newMax * currentPercent, 1, newMax);
        model.SetAttribute(StaminaAttributeName, clampedValue);
    }

    protected OnStackRemove(value: number, scalingType: TModifierStackScalingType): void {
        const scaledValue = this.GetStackScaledValue(value, scalingType);
        const model = this.Character.Instance;
        if (model === undefined)
            return;

        const current = GetCharacterStamina(model);
        const currentMax = GetCharacterMaxStamina(model);
        const currentPercent = current / currentMax;

        if (scaledValue < 0) {
            model.SetAttribute(MaxStaminaAttributeName, currentMax + scaledValue);
        } else {
            model.SetAttribute(MaxStaminaAttributeName, currentMax - scaledValue);
        }

        const newMax = GetCharacterMaxStamina(model);
        const clampedValue = math.clamp(newMax * currentPercent, 1, newMax);
        model.SetAttribute(StaminaAttributeName, clampedValue);
    }
}