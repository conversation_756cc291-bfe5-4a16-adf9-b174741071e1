import {Character, StatusEffect, StatusEffectDecorator} from "@rbxts/wcs";
import {GetRootPart} from "../Common";

@StatusEffectDecorator
export class RotateCharacterToDirectionStatus extends StatusEffect {
    private positionToLookAt: Vector3;
    private alignOrientation: AlignOrientation = undefined!;

    constructor(character: Character, positionToLookAt: Vector3) {
        super(character);
        this.positionToLookAt = positionToLookAt;
        this.DestroyOnEnd = true;

        this.alignOrientation = new Instance("AlignOrientation");
        this.alignOrientation.Mode = Enum.OrientationAlignmentMode.OneAttachment;
        this.alignOrientation.Responsiveness = 100;
        this.alignOrientation.MaxTorque = 100000;
        this.alignOrientation.ReactionTorqueEnabled = false;
        this.alignOrientation.MaxAngularVelocity = 100000;
        this.alignOrientation.AlignType = Enum.AlignType.AllAxes;
        this.Janitor.Add(() => this.alignOrientation.Destroy());

        const rootPart = GetRootPart(this.Character.Instance, 5);
        if (rootPart === undefined) {
            return;
        }

        this.alignOrientation.Attachment0 = rootPart.FindFirstChild("RootAttachment") as Attachment;
        this.alignOrientation.Enabled = false;
        this.alignOrientation.Parent = rootPart;
    }

    protected OnStartClient(): void {
        super.OnStartClient();
        this.SetHumanoidData({
            AutoRotate: [false, "Set"]
        });

        const rootPart = GetRootPart(this.Character.Instance);
        if (rootPart === undefined)
            return;

        const rotation = CFrame.lookAt(rootPart.Position, this.positionToLookAt);
        this.alignOrientation.Enabled = true;
        this.alignOrientation.CFrame = rotation;
    }

    protected OnEndClient(): void {
        super.OnEndClient();
        this.alignOrientation.Enabled = false;
    }
}