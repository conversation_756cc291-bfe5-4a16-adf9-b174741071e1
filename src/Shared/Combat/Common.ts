import {Components} from "@flamework/components";
import {Dependency} from "@flamework/core";
import {Players, RunService} from "@rbxts/services";
import {CharacterComponent} from "../Components/CharacterComponent";

import {GetMouseHit} from "../InkLabs/Mouse";

export const GetTargetPositionFromCharacter = (character: Model) => {
    if (RunService.IsClient())
        return GetMouseHit();
    const isPlayer = Players.GetPlayerFromCharacter(character);
    if (isPlayer !== undefined) {
        return GetMouseHit(undefined, isPlayer);
    }

    const targetValue = character.FindFirstChild("TARGET") as ObjectValue | undefined;
    if (targetValue?.Value === undefined)
        return character.GetPivot().Position;

    const target = targetValue.Value as Model;
    return target.GetPivot().Position;
};

export const WeldToCharacter = (part: BasePart, character: Model) => {
    const weld = new Instance("Weld") as Weld;
    weld.Part0 = part;
    weld.Part1 = character.PrimaryPart;
    weld.Parent = part;
    return weld;
};

export const WeldToPart = (part: BasePart, target: BasePart, c0?: CFrame, c1?: CFrame) => {
    const weld = new Instance("Weld") as Weld;
    weld.Part0 = part;
    weld.Part1 = target;
    weld.C0 = c0 ?? new CFrame();
    weld.C1 = c1 ?? new CFrame();
    weld.Parent = part;
    return weld;
};

export const GetRootPart = (character: Instance, waitFor?: number) => {
    return waitFor !== undefined
        ? character.WaitForChild("HumanoidRootPart", waitFor) as BasePart | undefined
        : character.FindFirstChild("HumanoidRootPart") as BasePart | undefined;
};

export const GetCharacterComponentFromModel = (character: Model) => {
    const components = Dependency<Components>();
    return components.getComponent<CharacterComponent>(character);
};