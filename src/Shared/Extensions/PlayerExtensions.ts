import { Components } from "@flamework/components";
import { Dependency } from "@flamework/core";
import { PlayerComponent } from "Shared/Components/PlayerComponent";

/**
 * Gets the player component from the player, if it exists.
 * @param player The player to get the component from
 * @returns The player component, or undefined if it doesn't exist
 */
export function GetPlayerComponentFromPlayer(player: Player) {
    const components = Dependency<Components>();
    return components.getComponent<PlayerComponent>(player);
}

/**
 * Adds the "PlayerDataLoaded" tag to the player.
 * @param player The player to add the tag to
 */
export function AddPlayerDataTagToPlayer(player: Player) {
    player.AddTag("PlayerDataLoaded");
}

/**
 * @param player The player to check
 * @returns True if the player has the "PlayerDataLoaded" tag, false otherwise
 */
export function IsPlayerDataLoaded(player: Player) {
    return player.HasTag("PlayerDataLoaded");
}

export function GetPlayerLuck(player: Player) {
    return (player.GetAttribute("Luck") as number | undefined) ?? 0;
}

export function SetPlayerLuck(player: Player, luck: number) {
    player.SetAttribute("Luck", luck);
}

export function IncreasePlayerLuck(player: Player, luck: number) {
    SetPlayerLuck(player, GetPlayerLuck(player) + luck);
}

export function ReducePlayerLuck(player: Player, luck: number) {
    SetPlayerLuck(player, GetPlayerLuck(player) - luck);
}
