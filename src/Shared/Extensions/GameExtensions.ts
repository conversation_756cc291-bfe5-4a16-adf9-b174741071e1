export type ServerType = "Private" | "Reserved" | "Public";

export function isPrivateServer() {
    return game.PrivateServerId !== "" && game.PrivateServerOwnerId !== 0;
}

export function isReservedServer() {
    return game.PrivateServerId !== "" && game.PrivateServerOwnerId === 0;
}

export function getServerType(): ServerType {
    return isPrivateServer()
        ? "Private"
        : isReservedServer()
            ? "Reserved"
            : "Public";
}