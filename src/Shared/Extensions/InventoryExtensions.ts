import { GetOreById } from "Shared/Data/Items/Ores";
import Notify from "../InkLabs/Notify";
import { IInventoryItem } from "Shared/Resources/Inventory/InventoryItem";
import { HttpService } from "@rbxts/services";

export function GiveOreToPlayer(
  player: Player,
  id: string,
  obtainedFrom: string
) {
  const info = GetOreById(id);
  if (info === undefined) return;

  const item: IInventoryItem = {
    Id: `${id}_${player.UserId}_${HttpService.GenerateGUID(false)}`,
    Hidden: false,
    ItemType: "Ore",
    Locked: false,
    Name: info.Name,
    Rarity: info.Rarity,
    Tradeable: true,
    Unique: false,
    Data: {
      Id: id,
      Modifiers: [],
      ObtainedAt: DateTime.now().UnixTimestamp,
      ObtainedBy: tostring(player.UserId),
      ObtainedFrom: obtainedFrom,
    },
  };
  if (item === undefined) return;

  // inventoryService.GiveItem(player, item);
  Notify({
    Player: player,
    Header: "New item received!",
    Style: "Success",
    Description: `You have received 1x '${info.Name}'.`,
  });
}
