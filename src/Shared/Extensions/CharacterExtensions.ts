import {RunService} from "@rbxts/services";
import {AffectableHumanoidProps, Character} from "@rbxts/wcs";

// Character is guaranteed in SERVER side, but may be undefined in CLIENT side.
export async function GetCharacterFromInstanceAsync(characterModel: Instance, humanoidProps?: AffectableHumanoidProps, timeout?: number) {
    timeout ??= 20;
    let character = Character.GetCharacterFromInstance(characterModel);
    if (character !== undefined) {
        return character;
    }

    if (RunService.IsServer())
    {
        character = new Character(characterModel);
        if (humanoidProps !== undefined)
            character.SetDefaultProps(humanoidProps);
    } else {
        const start = os.clock();
        while (character === undefined && os.clock() - start < timeout) {
            character = Character.GetCharacterFromInstance(characterModel);
            task.wait();
        }
    }

    return character;
}