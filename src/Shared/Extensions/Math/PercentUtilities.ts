import {t} from "@rbxts/t";
// Percent difference between two positive numbers greater than 0. Percentage difference is usually calculated
// when you want to know the difference in percentage between two numbers. The order of the numbers does not matter
// as we are simply dividing the difference between two numbers by the average of the two numbers.
export function GetPercentDifference(v1: number, v2: number) {
    if (v1 === undefined || !t.numberPositive(v1))
        error(`Invalid 'v1' argument passed. Expected a number greater than 0. Got '${v1}'.`);

    if (v2 === undefined || !t.numberPositive(v2))
        error(`Invalid 'v2' argument passed. Expected a number greater than 0. Got '${v2}'.`);

    return math.abs(v1 - v2) / ((v1 + v2) / 2) * 100;
}

// Quantifies the change from one number to another and expresses the change as an increase or decrease.
// Going from 10 apples to 20 apples is a 100% increase (change) in the number of apples.
// Vice verse would be 20 to 10 therefore 50% decrease. Negative numbers indicate a decrease and positive increase.
export function GetPercentChange(originalValue: number, newValue: number) {
    return (newValue - originalValue) / math.abs(originalValue) * 100;
}

// Great for calculating percentages where you start at 0 and are aiming for a specific number.
export function GetPercentProgress(current: number, goal: number) {
    return (current / goal) * 100;
}