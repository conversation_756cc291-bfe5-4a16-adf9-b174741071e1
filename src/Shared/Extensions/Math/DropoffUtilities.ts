// SweetSpot dropoff is 100% dmg from center to half radius, then 25% from half radius to edge.
export function SweetSpotDropoff(distanceFromOriginInStuds: number, radiusInStuds: number) {
    // 100% damage from center to half radius, and then 25% damage from half radius to radius edge
    if (distanceFromOriginInStuds > radiusInStuds)
        return 0;
    return distanceFromOriginInStuds <= radiusInStuds / 2 ? 1 : 0.25;
}

export function LogarithmicDropoff(distanceFromOriginInStuds: number, radiusInStuds: number) {
    if (distanceFromOriginInStuds > radiusInStuds)
        return 0;

    const normalizedDistance = distanceFromOriginInStuds / radiusInStuds;
    return math.clamp(
        1
        / math.log(radiusInStuds + 1)
        * math.log(radiusInStuds - (radiusInStuds * normalizedDistance) + 1),
        0,
        1
    )
}