import {t} from "@rbxts/t";

const HUGE = math.huge;

export function isNaN(num: number) {
    return num !== num;
}

export function isFinite(num: number) {
    return num > -HUGE && num < HUGE;
}

// Returns x if less than or equal to max and greater than or equal to min (1 by default).
// Returns min if x is greater than max. 
// Returns max if x is less than min.
export function loopNumber(x: number, max: number, min: number = 1) {
    return x > max ? min : x < min ? max : x;
}

export function roundNumber(number: number, decimalPlaces?: number) {
    if (decimalPlaces !== undefined && !t.numberPositive(decimalPlaces))
        error(`Invalid 'decimalPlaces' argument. Expected a number greater or equal to 0, got '${decimalPlaces}'.`);

    const factor = decimalPlaces !== undefined ? 10 ^ decimalPlaces : 1;
    return math.floor(number * factor + 0.5) / factor;
}

export function getAlpha(start: number, goal: number, current: number) {
    return (current - start) / (goal - start);
}

export function lerpNumber(start: number, goal: number, alpha: number) {
    return start + (goal - start) * alpha;
}

export function isNumberWithinRange(x: number, min: number, max: number) {
    return x >= min && x <= max;
}