// Stacks the <strong>increaseBy</strong> linearly.
export function LinearStacking(increaseBy: number, stackAmount: number) {
    return increaseBy * stackAmount
}

// Stacks the <strong>increaseBy</strong> hyperbolically.
// This is good for something that increases by, say, '25%', but you don't want to reach 100% after 4 stacks.
// E.g. 'Reduce damage by 25% per stack', but you don't reach 100% damage reduction only at 4 stack count.
export function HyperbolicStacking(increaseBy: number, stackAmount: number) {
    return (1 - 1 / (1 + increaseBy * stackAmount))
}

// Stacks the <strong>increaseBy</strong> exponentially.
// This will cause rapid progression of the <strong>increaseBy</strong> at higher stack counts.
export function ExponentialStacking(increaseBy: number, stackAmount: number) {
    return (increaseBy) ^ stackAmount;
}