const PI = math.pi;
const TAU = 2 * PI;

export function getAngleDiffSigned(angleA: number, angleB: number) {
    let diff = (angleA - angleB) % TAU;
    if (diff > PI) {
        diff = diff - TAU;
    }
    return diff;
}

export function getClampedAngle(toClamp: number, lowerLimit: number, upperLimitDeg: number, limitOrigin: number = 0) {
    const diff = getAngleDiffSigned(limitOrigin, toClamp);
    const clampedDiff = math.clamp(diff, lowerLimit, upperLimitDeg);
    return (limitOrigin + clampedDiff);
}