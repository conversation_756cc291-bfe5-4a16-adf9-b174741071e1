import {Workspace} from "@rbxts/services";
import spr from "@rbxts/spr";

const colorCorrectionEffect = new Instance("ColorCorrectionEffect");
colorCorrectionEffect.Saturation = -1;
colorCorrectionEffect.TintColor = Color3.fromRGB(170, 170, 255);
colorCorrectionEffect.Parent = script;

export default function () {
    const colorCorrection = colorCorrectionEffect.Clone();
    colorCorrection.TintColor = Color3.fromRGB(255, 255, 255);
    colorCorrection.Contrast = 0;
    colorCorrection.Saturation = 0;
    colorCorrection.Parent = Workspace.CurrentCamera;

    spr.target(colorCorrection, 1, 10, {
        Saturation: colorCorrectionEffect.Saturation,
        TintColor: colorCorrectionEffect.TintColor,
        Contrast: colorCorrectionEffect.Contrast
    });

    task.wait(2);

    spr.target(colorCorrection, 1.3, 7, {
        Saturation: 0,
        TintColor: new Color3(1, 1, 1),
        Contrast: 0
    });
    spr.completed(colorCorrection, () => {
        colorCorrection.Destroy();
    });
}