import { EffectsFolder } from "Shared/Libraries/Directories";
import { Debris, Workspace } from "@rbxts/services";
import { CastRayOnMap } from "Shared/Libraries/SpatialLib";
import { GetRootPart } from "Shared/Combat/Common";

export default function (character: Model) {
    const dashEffectsFolder = EffectsFolder.FindFirstChild("Dash") as Folder;
    if (dashEffectsFolder === undefined)
        return;

    const rootPart = GetRootPart(character);
    if (rootPart === undefined)
        return;

    const attachment = new Instance("Attachment");
    attachment.Name = "Dash";
    attachment.Parent = Workspace.Terrain;

    let isActive = true;

    task.defer(() => {
        while (attachment !== undefined && isActive) {
            const raycastResult = CastRayOnMap(rootPart.Position, rootPart.CFrame.UpVector.mul(-1), 8);
            if (raycastResult === undefined) {
                task.wait()
                continue;
            }

            attachment.WorldPosition = raycastResult.Position;

            const particleEmitter = attachment.FindFirstChildOfClass("ParticleEmitter");
            if (particleEmitter !== undefined) {
                particleEmitter.Color = new ColorSequence(raycastResult.Instance.Color);
            }

            task.wait();
        }
    });

    dashEffectsFolder.GetChildren().forEach((child) => {
        child.Clone().Parent = attachment;
    });

    attachment.GetChildren().forEach((child) => {
        if (child.IsA("Sound"))
            child.Play();
        else if (child.IsA("ParticleEmitter"))
            child.Enabled = true;
    });

    task.delay(0.4, () => {
        let lifetime = 0
        attachment.GetChildren().forEach((child) => {
            if (child.IsA("ParticleEmitter")) {
                lifetime = math.max(lifetime, child.Lifetime.Max);
                child.Enabled = false;
            }
        });

        Debris.AddItem(attachment, lifetime);
    });
}