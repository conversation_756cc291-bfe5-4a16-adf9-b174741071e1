import { NumAbbr } from "@rbxts/number-manipulator";
import { Workspace } from "@rbxts/services";
import spr from "@rbxts/spr";
import { GuiAssetsFolder } from "../../Libraries/Directories";

type FloatingDamageGui = BillboardGui & {
  Icon: ImageLabel & {
    Shadow: ImageLabel;
    UIGradient: UIGradient;
    CriticalGradient: UIGradient;
  };
  TextLabel: TextLabel & {
    Shadow: TextLabel;
    UIGradient: UIGradient;
    CriticalGradient: UIGradient;
  };
};

const Abbreviator = new NumAbbr();
const TEMPLATE = GuiAssetsFolder.WaitForChild(
  "FloatingDamage"
) as FloatingDamageGui;

export default function (character: Model, damage: number, critical: boolean) {
  const attachment = new Instance("Attachment");
  attachment.Parent = Workspace.Terrain;
  attachment.WorldPosition = character.GetPivot().Position;

  const billboard = TEMPLATE.Clone();
  billboard.TextLabel.Text = Abbreviator.abbreviate(damage);
  billboard.TextLabel.Shadow.Text = billboard.TextLabel.Text;
  billboard.Parent = attachment;

  if (critical) {
    billboard.TextLabel.UIGradient.Enabled = false;
    billboard.TextLabel.CriticalGradient.Enabled = true;

    billboard.Icon.UIGradient.Enabled = false;
    billboard.Icon.CriticalGradient.Enabled = true;
  }

  spr.target(attachment, 2.5, 3, {
    WorldPosition: attachment.WorldPosition.add(
      new Vector3(math.random(-4, 4), math.random(3, 8), math.random(-4, 4))
    ),
  });

  task.wait(2);

  billboard.GetDescendants().forEach((x) => {
    if (x.IsA("ImageLabel")) {
      spr.target(x, 1.3, 5, {
        ImageTransparency: 1,
      });
      return;
    }

    if (x.IsA("TextLabel"))
      spr.target(x, 1.3, 5, {
        TextTransparency: 1,
      });
  });

  task.wait(2);

  attachment.Destroy();
}
