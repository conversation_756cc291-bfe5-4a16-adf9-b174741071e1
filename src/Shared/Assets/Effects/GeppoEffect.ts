import { EffectsFolder } from "Shared/Libraries/Directories";
import { Debris, Workspace } from "@rbxts/services";
import { EmitChildren } from "Shared/Libraries/FXUtilities";

export default function (originalPosition: Vector3, direction: Vector3) {
    const geppoEffectsFolder = EffectsFolder.FindFirstChild("Geppo") as Folder;
    if (geppoEffectsFolder === undefined)
        return;

    const attachment = new Instance("Attachment");
    attachment.Name = "Geppo";
    attachment.Parent = Workspace.Terrain;

    attachment.WorldPosition = originalPosition;
    attachment.WorldCFrame = CFrame.lookAt(originalPosition, originalPosition.add(direction.mul(100)))
        .mul(CFrame.Angles(math.rad(90), 0, 0));

    geppoEffectsFolder.GetChildren().forEach((child) => {
        child.Clone().Parent = attachment;
    });

    const lifetime = EmitChildren(attachment);
    Debris.AddItem(attachment, lifetime);
}