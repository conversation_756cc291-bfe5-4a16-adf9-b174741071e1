import { GenericEffectsFolder, GetDebrisFolder } from "Shared/Libraries/Directories";
import { <PERSON><PERSON><PERSON>, <PERSON> } from "@rbxts/services";
import { GetRootPart } from "Shared/Combat/Common";

type TExpOrb = MeshPart & {
    Sound: Sound;
    Trail: Trail;
    Shader: BasePart;
    Attachment1: Attachment;
    Attachment2: Attachment;
}

// Floats an orb from the origin towards the player with a spinning, lerping motion
export default function (origin: Vector3) {
    const orbMesh = GenericEffectsFolder.FindFirstChild("Orb") as TExpOrb;
    if (orbMesh === undefined)
        return;

    const character = Players.LocalPlayer.Character;
    if (character === undefined)
        return;

    const rootPart = GetRootPart(character);
    if (rootPart === undefined)
        return;

    const distance = (rootPart.Position.sub(origin)).Magnitude;
    if (distance > 500)
        return;

    const amount = math.random(3, 7);
    task.spawn(() => {
        for (let i = 0; i < amount; i++) {
            const orb = orbMesh.Clone();
            orb.CFrame = new CFrame(origin)
                .mul(CFrame.Angles(
                    math.rad(math.random(-360, 360)),
                    math.rad(math.random(-360, 360)),
                    math.rad(math.random(-360, 360))
                ));

            orb.Parent = GetDebrisFolder();

            if (i % 2 === 0)
                orb.Color = Color3.fromRGB(239, 224, 78);
            else
                orb.Color = Color3.fromRGB(255, 186, 52);

            orb.Trail.Color = new ColorSequence(orb.Color);
            orb.Transparency = 0;

            // Initialize movement parameters
            const duration = 1.5 * (math.random(75, 100) / 100);
            const spinModifier = math.random(5, 15);
            const startPos = origin;
            const midPos = startPos.add(new Vector3(
                math.random(-25, 25),
                math.random(5, 25),
                math.random(-25, 25)
            ));
            const startSize = orb.Size.mul(math.random(90, 110) / 100);
            let elapsed = 0;

            // Movement loop
            task.spawn(() => {
                while (orb && orb.Parent) {
                    elapsed += task.wait();
                    const progress = elapsed / duration;

                    if (progress >= 1 || rootPart === undefined) {
                        orb.Sound.Play();
                        orb.Trail.Enabled = false;
                        orb.Transparency = 1;
                        orb.Shader.Transparency = 1;
                        Debris.AddItem(orb, orb.Sound.TimeLength);
                        return;
                    }

                    // Update position with lerping
                    const currentPos = startPos.Lerp(midPos, progress)
                        .Lerp(midPos.Lerp(rootPart.Position, progress), progress);

                    // Update rotation with spinning
                    orb.CFrame = new CFrame(currentPos)
                        .mul(CFrame.Angles(math.pi / 2, 0, elapsed * spinModifier));

                    // Update size with scaling
                    const newSize = startSize.mul(1 - progress * 0.75);
                    orb.Size = newSize;

                    // Update shader size
                    if (orb.Shader) {
                        orb.Shader.Size = new Vector3(
                            newSize.X * 1.2,
                            newSize.Y - 0.1,
                            newSize.Z * 1.2
                        );
                    }

                    // Update attachments
                    const attachmentOffset = newSize.X / 10;
                    if (orb.Attachment1) {
                        orb.Attachment1.Position = new Vector3(0, attachmentOffset, 0);
                    }
                    if (orb.Attachment2) {
                        orb.Attachment2.Position = new Vector3(0, -attachmentOffset, 0);
                    }
                }
            });
        }
    });
}