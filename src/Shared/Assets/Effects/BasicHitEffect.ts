import { Players } from "@rbxts/services";
import ShakeCamera from "../../Functions/ShakeCamera";
import { GenericCharacterEffect } from "./GenericCharacterEffect";

export default function (character: Model) {
  if (Players.LocalPlayer.Character === character) {
    ShakeCamera(character.GetPivot().Position, 0.05, 0, new TweenInfo(0.1), 55);
  }

  GenericCharacterEffect("BasicHitEffect", character);
}
