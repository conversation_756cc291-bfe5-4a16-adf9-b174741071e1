import {<PERSON><PERSON><PERSON>, Players} from "@rbxts/services";
import ShakeCamera from "../../Functions/ShakeCamera";
import {GenericEffectsFolder} from "../../Libraries/Directories";
import {EmitChildren} from "../../Libraries/FXUtilities";

type TGenericCharacterEffect = "BlockHit" | "BlockBreak" | "PerfectBlock"
    | "BasicHitEffect" | "BladeHit" | "LevelUpFx";

export function GenericCharacterEffect(effectName: TGenericCharacterEffect, character: Model) {
    const rootPart = character.PrimaryPart;
    if (rootPart === undefined) return;

    const hitEffectPart = GenericEffectsFolder.FindFirstChild(effectName) as BasePart & {
        Sound: Sound;
        Attachment: Attachment;
    };
    if (hitEffectPart === undefined) return;

    const attachment = hitEffectPart.Attachment.Clone();
    attachment.Parent = rootPart;

    const soundEffect = hitEffectPart.Sound.Clone();
    soundEffect.Parent = attachment;

    if (effectName === "BlockHit")
        if (Players.LocalPlayer.Character === character) {
            ShakeCamera(
                character.GetPivot().Position, 0.025, 0, new TweenInfo(0.1),
                55
            );
        }

    const lifetime = EmitChildren(attachment);
    Debris.AddItem(attachment, lifetime);
}