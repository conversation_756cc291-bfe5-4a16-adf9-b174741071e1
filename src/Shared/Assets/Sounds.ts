export type TSoundInfo = {
    Id: string,
    MaxDistance: number,
    MinDistance: number,
    Mode: Enum.RollOffMode,
    Looped: boolean,
    Volume: number,
    PlaybackSpeed: number,
    TimePosition: number
};

export const Sounds: { [key: string]: TSoundInfo } = {
    SmallDebris: {
        Id: "rbxassetid://1741599172",
        MaxDistance: 150,
        MinDistance: 10,
        Mode: Enum.RollOffMode.InverseTapered,
        Looped: false,
        Volume: 1,
        PlaybackSpeed: 1.5,
        TimePosition: 0
    },
    Debris: {
        Id: "rbxassetid://3778608737",
        MaxDistance: 150,
        MinDistance: 10,
        Mode: Enum.RollOffMode.InverseTapered,
        Looped: false,
        Volume: 1,
        PlaybackSpeed: 1,
        TimePosition: 0
    },
    BigDebris: {
        Id: "rbxassetid://3744400428",
        MaxDistance: 500,
        MinDistance: 10,
        Mode: Enum.RollOffMode.InverseTapered,
        Looped: false,
        Volume: 5,
        PlaybackSpeed: 1,
        TimePosition: 0
    }
} as const;

export type TSound = keyof typeof Sounds;