import { CollectionService, Workspace } from "@rbxts/services";

export const GetClosestWaterPart = (currentPosition: Vector3) => {
    const waterParts = CollectionService.GetTagged("Water") as BasePart[];
    if (waterParts.isEmpty())
        error("Missing water parts, make sure to tag the water parts with 'Water'.");

    const closestPart = waterParts.reduce((closest, part) => {
        const partPosition = part.Position;
        const distance = currentPosition.sub(partPosition).Magnitude;
        return distance < closest.Position.sub(currentPosition).Magnitude ? part : closest;
    }, waterParts[0]);

    return closestPart;
}

export const GetHighestPositionOfPart = (part: BasePart) => {
    const partHeight = part.Size.Y;
    const partPosition = part.Position;
    const highestPosition = partPosition.Y + partHeight;

    return highestPosition;
}

export const GetLowestPositionOfPart = () => {
    return Workspace.FallenPartsDestroyHeight + 100;
}