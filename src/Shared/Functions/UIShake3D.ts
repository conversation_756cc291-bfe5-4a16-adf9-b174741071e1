import {RunService} from "@rbxts/services";
import Screen3D from "../Libraries/Screen3D";

type Component3D = {
    enabled: boolean;
    component2D?: GuiObject;
    surfaceGui?: SurfaceGui;
    parent2D?: GuiObject;
    screen3D: Screen3D;
    parent3D?: Component3D;
    offset: CFrame;
    viewportSize: Vector2;
    conn?: RBXScriptConnection;
    Enable: (this: Component3D) => Component3D;
    Disable: (this: Component3D) => Component3D;
    GetViewportSize: (this: Component3D) => Vector2;
    UDim2ToCFrame: (this: Component3D, position2d: UDim2) => CFrame;
    GetStudsScreenSize: (this: Component3D, viewportSize: Vector2) => Vector3;
    RecomputeParent: (this: Component3D) => Component3D;
    ReadWorldCFrame: (this: Component3D) => CFrame;
}

/**
 * Creates a shaking animation effect on a GUI object that gradually fades out
 * @param guiObject - The GUI object to apply the shaking effect to
 * @param defaultOffset - The default CFrame to position to on end
 * @param duration - Duration of the shake effect in seconds (default: 0.25)
 * @param intensity - Initial intensity/frequency of the shake (default: 1)
 * @param distanceInOffset - Maximum distance the object can move in pixels (default: 3)
 */
export default function(
    guiObject: Component3D,
    defaultOffset: CFrame,
    duration: number = 0.25,
    intensity: number = 1,
    distanceInOffset: number = 3
) {
    duration = math.max(duration, 0.25) * 2;
    const defaultPosition = defaultOffset;
    const startTime = os.clock();
    const connection = RunService.PreSimulation.Connect(() => {
        const elapsed = os.clock() - startTime;
        const progress = elapsed / duration;

        // Calculate fadeout multiplier (1 at start, 0 at end)
        const fadeMultiplier = math.max(0, 1 - progress);

        // Apply fadeout to both intensity and distance
        const currentIntensity = intensity * fadeMultiplier;
        const currentDistance = distanceInOffset * fadeMultiplier;

        const bobbleX = math.cos(os.clock() * currentIntensity) * currentDistance;
        const bobbleY =
            math.abs(math.sin(os.clock() * currentIntensity)) * currentDistance;

        print(bobbleX, bobbleY, defaultOffset);
        guiObject.offset = defaultOffset.add(new Vector3(bobbleX * 0.0035, bobbleY * 0.0035, 0));
    });

    task.delay(duration, () => {
        connection.Disconnect();
        guiObject.offset = defaultPosition;
    });
}