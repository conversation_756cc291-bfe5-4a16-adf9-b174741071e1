import TitanUtils from "../InkLabs/TitanUtils";
import {CameraShakeTriggeredSignal} from "../Networking";

export default function(
    Origin: BasePart | Vector3,
    Intensity: number,
    Duration: number,
    FadeOut: TweenInfo,
    Distance: number,
    Speed?: number,
    FadeIn?: TweenInfo
) {
    CameraShakeTriggeredSignal.Fire(Intensity, Duration);
    TitanUtils.CameraControl.Shake(
        Origin, Intensity, Duration, FadeOut, Distance, Speed, FadeIn
    );
}