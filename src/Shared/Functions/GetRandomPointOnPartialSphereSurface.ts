import GetRandomPointOnSphereSurface from "./GetRandomPointOnSphereSurface";

// Returns a point on a hemisphere's surface given a center, radius, and min/max from the origin
export default function(center: Vector3, radius: number, range: Vector2) {
    while (true) {
        const randomPoint = GetRandomPointOnSphereSurface(center, radius);
        if (randomPoint.Y >= center.Y + range.X && randomPoint.Y <= center.Y + range.Y)
            return randomPoint;
    }
}