import {Character} from "@rbxts/wcs";
import {BlockingStatus} from "Shared/Combat/StatusEffects/BlockingStatus";
import {AttackingStatus} from "../Combat/StatusEffects/AttackingStatus";
import {BlockBreakStatus} from "../Combat/StatusEffects/BlockBreakStatus";
import {HitStunStatus} from "../Combat/StatusEffects/HitStunStatus";
import {InCombatStatus} from "../Combat/StatusEffects/InCombatStatus";
import {PerfectBlockStatus} from "../Combat/StatusEffects/PerfectBlockStatus";
import {RagdollStatus} from "../Combat/StatusEffects/RagdollStatus";
import {MaxStaminaAttributeName, StaminaAttributeName} from "../Resources/Attributes";
import {GetAbilityInfo} from "../Resources/Combat/Abilities";
import {InCombatTag, SafeZoneTag, SwimmingTag, UnableToAttackTag} from "../Resources/Tags";

export const IN_COMBAT_DURATION = 40;

export const STUN_STATUSES = [
    BlockBreakStatus,
    HitStunStatus,
    RagdollStatus,
    PerfectBlockStatus
];

export function IsStunned(character: Character) {
    return STUN_STATUSES
        .some(x =>
            character.HasStatusEffects(x));
}

export function IsUnableToAttack(character: Instance) {
    return character.HasTag(UnableToAttackTag);
}

export function IsBlocking(character: Character) {
    return character.HasStatusEffects(BlockingStatus);
}

export function IsAttacking(character: Character) {
    return character.HasStatusEffects(AttackingStatus);
}

export function IsSeated(character: Character) {
    return character.Humanoid.Sit;
}

export function IsSwimming(character: Character) {
    return character.Instance.HasTag(SwimmingTag);
}

export function StartAttackingState(character: Character, duration?: number) {
    new AttackingStatus(character).Start(duration);
}

export function IsInCombat(character: Instance) {
    return character.HasTag(InCombatTag);
}

export function AddInCombatStatusToCharacter(character: Character) {
    new InCombatStatus(character).Start(IN_COMBAT_DURATION);
}

export function IsInSafeZone(character: Instance) {
    return character.HasTag(SafeZoneTag);
}

export function GetCharacterStamina(character: Instance) {
    return character.GetAttribute(StaminaAttributeName) as number ?? 0;
}

export function GetCharacterMaxStamina(character: Instance) {
    return character.GetAttribute(MaxStaminaAttributeName) as number ?? 100;
}

export function HasEnoughStaminaToUseAbility(character: Instance, abilityId: string) {
    const cost = GetStaminaCostForAbility(character, abilityId);
    if (cost <= 0)
        return true;

    return GetCharacterStamina(character) >= cost;
}

export function GetStaminaCostForAbility(character: Instance, abilityId: string) {
    const abilityInfo = GetAbilityInfo(abilityId);
    if (abilityInfo === undefined)
        return 0;

    const staminaRequirement = abilityInfo.StaminaCost;
    if (staminaRequirement === undefined)
        return 0;

    return staminaRequirement.Raw + (GetCharacterMaxStamina(character) * staminaRequirement.Percent);
}

export function ReduceCharacterStamina(character: Instance, amount: number) {
    character.SetAttribute(StaminaAttributeName, math.max(GetCharacterStamina(character) - amount, 0));
}