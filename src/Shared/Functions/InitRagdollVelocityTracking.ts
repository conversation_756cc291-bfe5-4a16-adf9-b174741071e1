import Maid from "@rbxts/maid";
import {CollectionService, Debris, RunService, Workspace} from "@rbxts/services";

import {CharacterModel} from "../Components/CharacterModel";
import {GetSound} from "../Libraries/Directories";
import {GetRandomColor3} from "../Libraries/FXUtilities";
import {BlockExplosionEffect} from "../Libraries/HelperEffects/BlockExplosionEffect";
import {SimpleCrater} from "../Libraries/HelperEffects/CraterEffects";
import DebrisExplosionEffect from "../Libraries/HelperEffects/DebrisExplosionEffect";
import {Packets} from "../Resources/Packets";
import {RagdollTag} from "../Resources/Tags";

export function InitRagdollVelocityTracking() {
    debug.setmemorycategory("RagdollVelocityTracking");
    if (RunService.IsServer()) {
        CollectionService.GetInstanceAddedSignal(RagdollTag).Connect((x) => {
            const char = x as CharacterModel;
            const hum = char.FindFirstChildOfClass("Humanoid") as Humanoid;
            const part = hum?.RootPart;
            if (part === undefined)
                return;

            const maid = new Maid();
            let lastCheck = os.clock();
            let checkCooldown = 0.1;

            let conn = part.Touched.Connect(() => {
                if (part === undefined || part.Anchored || char === undefined || !char.HasTag(RagdollTag)) {
                    maid?.Destroy();
                    return;
                }

                if (os.clock() - lastCheck < checkCooldown) return;
                lastCheck = os.clock();

                let velocity = part.AssemblyLinearVelocity;
                let power = velocity.Magnitude;
                if (power >= 35) {
                    // Networking.Physics.VelocityRockEffectEvent.FireAllClients(part.Position);
                    Packets.Replicators.RockEffect.sendToAll(part.Position);
                }
            });

            let destroyConn = part.Destroying.Once(() => maid?.Destroy());
            maid.GiveTask(() => conn?.Disconnect());
            maid.GiveTask(() => destroyConn?.Disconnect());
        });
    } else {
        Packets.Replicators.RockEffect.listen((position: Vector3) => {
            const debrisSound = GetSound("SmallDebris")?.Clone();
            if (debrisSound !== undefined) {
                debrisSound.PlaybackSpeed *= new Random().NextNumber(0.95, 1.25);

                const tempAttachment = new Instance("Attachment");
                tempAttachment.WorldPosition = position;
                debrisSound.Parent = tempAttachment;
                tempAttachment.Parent = Workspace.Terrain;
                debrisSound.Play();
                Debris.AddItem(tempAttachment, debrisSound.TimeLength + 1);
            }

            new SimpleCrater({
                Center: new CFrame(position),
                Amount: math.random(3, 7),
                Radius: math.random(2, 5),
                BaseHeight: new Random().NextNumber(0.3, 1),
                BaseLength: new Random().NextNumber(0.2, 0.6),
                FadeInTime: 0.05,
                RaycastDistance: 4
            });

            new BlockExplosionEffect({
                position: position,
                size: {
                    X: new NumberRange(0.075, 1),
                    Y: new NumberRange(0.075, 1),
                    Z: new NumberRange(0.075, 1)
                },
                amount: new NumberRange(1, 8),
                power: 0.25,
                rayDistance: 5,
                centerVariation: 6
            });

            DebrisExplosionEffect(position, 1, new NumberRange(3, 7), GetRandomColor3());
        });
    }
}