export default function <T extends BasePart | MeshPart | UnionOperation>(position: Vector3, parts: T[]) {
    if (parts.isEmpty())
        return;

    let smallestDistance = math.huge;
    let closestPart = undefined as T | undefined;
    parts.forEach(x => {
        const distance = position.sub(x.Position).Magnitude;
        if (distance >= smallestDistance)
            return;

        closestPart = x;
        smallestDistance = distance;
        return;
    });

    return closestPart ?? parts[0];
}