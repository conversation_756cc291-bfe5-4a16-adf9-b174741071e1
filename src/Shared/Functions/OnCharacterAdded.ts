import {Janitor} from "@rbxts/janitor";
import {Players} from "@rbxts/services";
import {OnPlayerAdded} from "./OnPlayerAdded";

export function OnCharacterAdded(player: Player, callback: (player: Player, character: Model) => void) {
    const janitor = new Janitor();
    const character = player.Character;
    if (character !== undefined)
        callback(player, character);

    const characterAddedConnection = player.CharacterAdded.Connect((newCharacter) =>
        callback(player, newCharacter));
    janitor.Add(() => characterAddedConnection.Disconnect());
    janitor.Add(player.Destroying.Once(() => janitor.Destroy()));
    return () => janitor.Destroy();
}

export function OnAnyCharacterAdded(callback: (player: Player, character: Model) => void) {
    const janitor = new Janitor();
    const onPlayerAdded = OnPlayerAdded(player => OnCharacterAdded(player, callback));
    janitor.Add(onPlayerAdded);
    return () => janitor.Destroy();
}

export function OnLocalCharacterAdded(callback: (character: Model) => void) {
    const janitor = new Janitor();
    if (Players.LocalPlayer.Character !== undefined)
        callback(Players.LocalPlayer.Character);

    const connection = Players.LocalPlayer.CharacterAdded.Connect((newCharacter) => callback(newCharacter));
    janitor.Add(connection);
    return () => janitor.Destroy();
}