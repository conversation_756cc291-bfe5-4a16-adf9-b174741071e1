import { create } from "@rbxts/vide";
import { GetRootPart } from "Shared/Combat/Common";
import { GetDarkerColor } from "Shared/Functions/UIHelpers";

function CreateMarkerGui(color: Color3) {
    const billboard = create("BillboardGui")({
        AlwaysOnTop: true,
        Size: UDim2.fromOffset(5, 5),
        ResetOnSpawn: false,
        Active: true,
        MaxDistance: math.huge,
        StudsOffset: new Vector3(0, 3.5, 0)
    });

    const mainFrame = create("Frame")({
        BackgroundColor3: color,
        Size: UDim2.fromScale(1, 1),
        Rotation: 45,
        SizeConstraint: "RelativeYY",
        Parent: billboard
    });

    create("UIStroke")({
        LineJoinMode: "Miter",
        Thickness: 3,
        Color: GetDarkerColor(color, 0.8),
        Parent: mainFrame
    });

    return billboard;
}

export default function <PERSON><PERSON><PERSON>cter(character: Model, color: Color3) {
    const rootPart = GetRootPart(character, 10);
    if (rootPart === undefined)
        return;

    const billboard = CreateMarkerGui(color);
    billboard.Parent = rootPart;

    return () => {
        billboard.Destroy();
        character.RemoveTag("Marked");
    };
}

