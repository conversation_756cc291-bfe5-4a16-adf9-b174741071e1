import {Character} from "@rbxts/wcs";
import {BlockBreakStatus} from "../Combat/StatusEffects/BlockBreakStatus";
import {BlockingStatus} from "../Combat/StatusEffects/BlockingStatus";
import {GetBlockCapacityFromDefense} from "./Data/CharacterStatsUtilities";

export const BLOCKING_ATTRIBUTE_NAME = "BlockCapacity";

export function CreateBlockingAttribute(character: Model, defensePoints: number = 0) {
    character.SetAttribute(BLOCKING_ATTRIBUTE_NAME, GetBlockCapacityFromDefense(defensePoints));
}

export function DamageCharacterBlockingHealth(character: Model, damage: number) {
    const currentCapacity = character.GetAttribute(BLOCKING_ATTRIBUTE_NAME) as number ?? 0;
    const newCapacity = math.clamp(currentCapacity - damage, 0, math.huge);
    character.SetAttribute(BLOCKING_ATTRIBUTE_NAME, newCapacity);
}

export function GetCharacterBlockingHealth(character: Model) {
    return character.GetAttribute(BLOCKING_ATTRIBUTE_NAME) as number ?? 0;
}

export function ApplyBlockBreakStatus(character: Character) {
    const blockingStatus = character.GetAllActiveStatusEffectsOfType(BlockingStatus);
    if (blockingStatus.size() > 0)
        blockingStatus.forEach((x) => x.Destroy());
    new BlockBreakStatus(character).Start(2);
}