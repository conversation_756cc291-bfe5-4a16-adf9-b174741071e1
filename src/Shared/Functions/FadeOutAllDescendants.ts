import spr from "@rbxts/spr";
import {AddPartToCollisionGroup} from "../Components/PhysicsManager";

export default function(parent: Instance) {
    parent.GetDescendants().forEach(x => {
        if (!x.IsA("BasePart") && !x.Is<PERSON>("MeshPart") && !x.Is<PERSON>("UnionOperation") && !x.IsA("Decal") && !x.IsA(
            "Texture")) {
            return;
        }

        if (x.Is<PERSON>("BasePart") || x.Is<PERSON>("UnionOperation") || x.<PERSON>("MeshPart")) {
            AddPartToCollisionGroup(x, "Debris");
        }

        spr.target(x, 1, 3, {
            Transparency: 1
        });
    })
}