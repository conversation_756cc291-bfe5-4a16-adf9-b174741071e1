import {Players, RunService} from "@rbxts/services";

import TableInspector from "../InkLabs/TableInspector";

const tablesTracked: string[] = [];
const inspector = RunService.IsServer() ? undefined : new TableInspector();
const playerGui = RunService.IsClient() && Players.LocalPlayer.WaitForChild("PlayerGui") as PlayerGui;
const gui = playerGui && new Instance("ScreenGui");
if (gui && inspector) {
    gui.Parent = playerGui;
    gui.Name = "TableInspector";
    gui.ResetOnSpawn = false;
    inspector.backFrame.Parent = gui;
}

export function CreateTableInspectorGui(name: string, t: unknown) {
    if (RunService.IsServer()) return;
    if (tablesTracked.includes(name)) return;
    if (!inspector) return;

    inspector.addTable(name, t);
    tablesTracked.push(name);
}