export const CharacterLimbNames = [
    "LowerTorso",
    "UpperTorso",
    "HumanoidRootPart",
    "LeftHand",
    "RightHand",
    "LeftFoot",
    "RightFoot",
    "LeftLowerArm",
    "LeftUpperArm",
    "RightLowerArm",
    "RightUpperArm",
    "LeftLowerLeg",
    "RightLowerLeg",
    "RightUpperLeg",
    "LeftUpperLeg",
    "Head"
];

export type CharacterLimbName = typeof CharacterLimbNames[number];

export default function (character: Model, limb: CharacterLimbName, waitFor?: number) {
    if (waitFor !== undefined)
        return character.WaitForChild(limb, math.max(waitFor, 1)) as BasePart | undefined;
    else
        return character.FindFirstChild(limb) as BasePart | undefined;
}