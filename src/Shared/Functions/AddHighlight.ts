import {GetRandomColor3} from "../Libraries/FXUtilities";

interface TProps {
    AlwaysOnTop?: boolean;
    FillColor?: Color3;
    OutlineColor?: Color3;
    FillTransparency?: number;
    OutlineTransparency?: number;
}

export default function(obj: Instance, props: TProps) {
    const highlight = new Instance("Highlight");
    highlight.OutlineColor = props.OutlineColor ?? GetRandomColor3();
    highlight.FillColor = props.FillColor ?? GetRandomColor3();
    highlight.OutlineTransparency = props.OutlineTransparency ?? 1;
    highlight.FillTransparency = props.FillTransparency ?? 1;
    highlight.DepthMode = props.AlwaysOnTop ? Enum.HighlightDepthMode.AlwaysOnTop : Enum.HighlightDepthMode.Occluded;
    highlight.Parent = obj;
    return highlight;
}