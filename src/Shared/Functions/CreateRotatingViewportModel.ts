import {Janitor} from "@rbxts/janitor";
import {RunService} from "@rbxts/services";
import ViewportModel from "../Libraries/ViewportModel";

export default function(parentFrame: Frame, model: Model, zIndex?: number, doNotRotate?: boolean) {
    const janitor = new Janitor();
    parentFrame.Destroying.Once(() => janitor.Destroy());
    const viewportFrame = new Instance("ViewportFrame");
    janitor.Add(viewportFrame);
    viewportFrame.Size = UDim2.fromScale(1, 1);
    viewportFrame.BackgroundTransparency = 1;
    viewportFrame.ZIndex = zIndex ?? parentFrame.ZIndex;
    viewportFrame.Parent = parentFrame;

    model.Parent = viewportFrame;
    const camera = new Instance("Camera");

    camera.FieldOfView = 70;
    camera.Parent = viewportFrame;

    let screenGui = parentFrame.FindFirstAncestorOfClass("ScreenGui");
    const viewportModel = new ViewportModel(viewportFrame, camera);
    const [cframe, size] = model.GetBoundingBox();
    viewportModel.SetModel(model);

    let theta = 0;
    let orientation = new CFrame();
    const distance = viewportModel.GetFitDistance(cframe.Position);

    viewportFrame.CurrentCamera = camera;
    const runServiceConnection = RunService.PostSimulation.Connect(deltaTime => {
        if (doNotRotate === true || !parentFrame.Visible)
            return;

        if (screenGui !== undefined && !screenGui.Enabled)
            return;

        if (screenGui === undefined) {
            screenGui = parentFrame.FindFirstAncestorOfClass("ScreenGui");
            if (screenGui === undefined || !screenGui.Enabled)
                return;
        }

        theta += math.rad(20 * deltaTime);
        orientation = CFrame.fromEulerAnglesYXZ(math.rad(-20), theta, 0);
        camera.CFrame = new CFrame(cframe.Position).mul(orientation).mul(new CFrame(0, 0, distance));
    });

    janitor.Add(() => runServiceConnection.Disconnect());
}