import Maid from "@rbxts/maid";
import {formatNum} from "@rbxts/number-manipulator";
import {Workspace} from "@rbxts/services";
import spr from "@rbxts/spr";

const baseVPSize = new Vector2(1920, 1080);
const getCurrentVPSize = () =>
    Workspace.CurrentCamera!.ViewportSize;

export const UpdateGradientVisuals = (gradient: UIGradient, min: number, max: number) =>
    spr.target(gradient, 1, 2, {Offset: new Vector2(math.clamp(min / max, 0, 1), 0)});

export const UpdateNumericLabelValues = (label: TextLabel, value: number, maxValue?: number) =>
    label.Text = `${string.upper(label.Name)}: ` + (maxValue !== undefined ? `${math.round(value)} / ${math.round(
        maxValue)}` : tostring(formatNum(math.round(value))));

export const GetScaledStrokeThickness = (scale: number) =>
    math.max(scale * ((getCurrentVPSize().Magnitude / baseVPSize.Magnitude)), 0);

export const GetDarkerColor = (color: Color3, factor: number) =>
    color.Lerp(new Color3(0, 0, 0), math.clamp(factor, 0, 1));

export const GetLighterColor = (color: Color3, factor: number) =>
    color.Lerp(new Color3(1, 1, 1), math.clamp(factor, 0, 1));

export const InitScaledStroke = (stroke: UIStroke) => {
    stroke.SetAttribute("Thickness", stroke.Thickness);

    const updateThickness = () => {
        spr.target(
            stroke,
            1,
            5,
            {Thickness: GetScaledStrokeThickness(stroke.GetAttribute("Thickness") as number ?? 0)}
        );
        // stroke.Thickness = GetScaledStrokeThickness(stroke.GetAttribute("Thickness") as number ?? 0);
    };

    const strokeMaid = new Maid();
    const updateConnection = Workspace.CurrentCamera!.GetPropertyChangedSignal("ViewportSize")
        .Connect(() => updateThickness());
    const attributeConnection = stroke.GetAttributeChangedSignal("Thickness").Connect(() => updateThickness());
    stroke.Destroying.Once(() => strokeMaid.Destroy());
};

export const AnimateStrokeThickness = (stroke: UIStroke, thickness: number) => {
    stroke.SetAttribute("Thickness", thickness);
};

type TButttonVisualProps = {
    DefaultColor?: Color3;
    HoverColor?: Color3;
    HoverText?: string;
    HoverStrokeColor?: Color3;
    HoverStrokeTransparency?: number;
}

export const PopUpNames = {
    Confirmation: "ConfirmationPopUp",
    EquippingOverlay: "EquipSkillPopUp"
} as const;

export const InitBaseButtonVisuals = (button: TextButton | ImageButton, props?: TButttonVisualProps) => {
    const maid = new Maid();
    const defaultColor = props?.DefaultColor ?? button.BackgroundColor3;
    const hoverColor = props?.HoverColor ?? defaultColor;

    const stroke = button.FindFirstChildOfClass("UIStroke");
    if (stroke) {
        stroke.Color = GetDarkerColor(defaultColor, 0.5);
        InitScaledStroke(stroke);
    }

    const defaultText = button.IsA("TextButton") ? button.Text : "";
    const hoverText = props?.HoverText ?? defaultText;

    const defaultStrokeColor = stroke?.Color ?? GetDarkerColor(defaultColor, 0.5);
    const defaultStrokeTransparency = stroke?.Transparency ?? 0.5;

    const updateVisuals = (hover: boolean) => {
        // warn("Updating visuals", hover);
        const color = hover ? hoverColor : defaultColor;
        const strokeColor = props?.HoverStrokeColor ?? GetDarkerColor(color, 0.5);

        spr.target(button, 0.8, 3, {BackgroundColor3: color});
        if (stroke) {
            spr.target(stroke, 1, 5, {
                Color: strokeColor,
                Transparency: hover ? props?.HoverStrokeTransparency ?? defaultStrokeTransparency : 1
            });

            AnimateStrokeThickness(stroke, hover ? 2 : 0);
        }

        if (button.IsA("TextButton"))
            button.Text = hover ? hoverText ?? defaultText : defaultText;
    };

    const hoverConnection = button.MouseEnter.Connect(() => updateVisuals(true));
    const leaveConnection = button.MouseLeave.Connect(() => updateVisuals(false));
    updateVisuals(false);

    maid.GiveTask(() => hoverConnection?.Disconnect());
    maid.GiveTask(() => leaveConnection?.Disconnect());
    button.Destroying.Once(() => maid.Destroy());
};