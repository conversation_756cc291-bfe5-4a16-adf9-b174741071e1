// Returns a point on a sphere's surface given a center and radius
export default function(center: Vector3, radius: number) {
    const u = math.random();
    const v = math.random();
    const theta = u * 2 * math.pi;
    const phi = math.acos(2 * v - 1);

    const x = radius * math.sin(phi) * math.cos(theta);
    const y = radius * math.sin(phi) * math.sin(theta);
    const z = radius * math.cos(phi);

    return center.add(new Vector3(x, y, z));
}