import {Dependency} from "@flamework/core";
import {CollectionService, Players, RunService} from "@rbxts/services";
import {OwnerIdAttributeName} from "Shared/Resources/Attributes";
import {WanderingWaypointService} from "../../Server/Services/WanderingWaypointService";
import {TFactionId} from "../Resources/Combat/Factions";
import {NPCJobInteractionWaypointTag, NPCJobWaypointTag} from "../Resources/Tags";

export type JobWaypointPart = BasePart & {
    Occupant: ObjectValue;
    WaitsFor: ObjectValue;
};

export function GetClosestAvailableJobToPosition(
    jobId: string,
    position: Vector3,
    range: number
) {
    const jobs = CollectionService.GetTagged(
        NPCJobWaypointTag
    ) as JobWaypointPart[];
    if (jobs.isEmpty()) return;

    const availableJobs = jobs.filter((x) => x.Occupant.Value === undefined);
    let closestJob: JobWaypointPart | undefined = undefined;
    let closestDistance = range + 1;
    availableJobs.forEach((x) => {
        const distance = position.sub(x.Position).Magnitude;
        if (distance > closestDistance) return;

        closestDistance = distance;
        closestJob = x;
    });

    return closestJob;
}

export function GetClosestJobInteractionPartAvailable(
    position: Vector3,
    range: number
): JobWaypointPart | undefined {
    const jobInteractions = CollectionService.GetTagged(
        NPCJobInteractionWaypointTag
    ) as JobWaypointPart[];
    if (jobInteractions.isEmpty()) return;

    const availableJobs = jobInteractions.filter(
        (x) => x.Occupant.Value === undefined
    );
    if (availableJobs.isEmpty()) return;

    let closestJob: JobWaypointPart | undefined = undefined;
    let closestDistance = range + 1;
    availableJobs.forEach((x) => {
        const distance = position.sub(x.Position).Magnitude;
        if (distance > closestDistance) return;

        closestDistance = distance;
        closestJob = x;
    });

    return closestJob;
}

export function GetRandomWanderingWaypointInRange(
    factions: TFactionId[],
    currentPosition: Vector3,
    range: number,
    lastWaypointPart?: BasePart
) {
    if (RunService.IsClient())
        return;

    const waypointService = Dependency<WanderingWaypointService>();
    if (waypointService === undefined)
        return;

    let waypoints = waypointService
        .GetWaypointsInRadius(currentPosition, factions, range)
        .filter(x => x !== lastWaypointPart);
    if (waypoints.isEmpty())
        return;

    const spread = 5;
    const waypoint = waypoints[math.random(0, waypoints.size() - 1)];
    const position = waypoint.Position.add(
        new Vector3(
            new Random().NextNumber(-spread, spread),
            0,
            new Random().NextNumber(-spread, spread)
        )
    );


    return { p: position, part: waypoint };
}

export function GetNpcTarget(npcCharacter: Instance) {
    return (npcCharacter.FindFirstChild("TARGET") as ObjectValue | undefined)
        ?.Value as Model | undefined;
}

export function GetNpcOwner(npcCharacterModel: Instance): Player | undefined {
    if (npcCharacterModel === undefined)
        return;
    const ownerAttribute = npcCharacterModel.GetAttribute(
        OwnerIdAttributeName
    ) as string | undefined;
    if (ownerAttribute === undefined) return;

    return Players.GetPlayerByUserId(tonumber(ownerAttribute)!) ?? undefined;
}