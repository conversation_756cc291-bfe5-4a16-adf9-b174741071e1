import {RunService} from "@rbxts/services";

/**
 * Creates a shaking animation effect on a GUI object that gradually fades out
 * @param guiObject - The GUI object to apply the shaking effect to
 * @param duration - Duration of the shake effect in seconds (default: 0.25)
 * @param intensity - Initial intensity/frequency of the shake (default: 1)
 * @param distanceInOffset - Maximum distance the object can move in pixels (default: 3)
 */
export default function(
    guiObject: GuiObject,
    duration: number = 0.25,
    intensity: number = 1,
    distanceInOffset: number = 3
) {
    let defaultPosition = guiObject.GetAttribute("ShakeOriginalPosition") as UDim2 | undefined;

    if (defaultPosition === undefined) {
        defaultPosition = guiObject.Position;
        guiObject.SetAttribute("ShakeOriginalPosition", defaultPosition);
    }

    const startTime = os.clock();
    const connection = RunService.PreSimulation.Connect(() => {
        const elapsed = os.clock() - startTime;
        const progress = elapsed / duration;

        // Calculate fadeout multiplier (1 at start, 0 at end)
        const fadeMultiplier = math.max(0, 1 - progress);

        // Apply fadeout to both intensity and distance
        const currentIntensity = intensity * fadeMultiplier;
        const currentDistance = distanceInOffset * fadeMultiplier;

        const bobbleX = math.cos(os.clock() * currentIntensity) * currentDistance;
        const bobbleY =
            math.abs(math.sin(os.clock() * currentIntensity)) * currentDistance;

        guiObject.Position = defaultPosition.add(
            UDim2.fromOffset(bobbleX * 0.001, bobbleY * 0.001)
        );
    });

    task.delay(duration, () => {
        connection.Disconnect();
        guiObject.Position = defaultPosition;
    });
}