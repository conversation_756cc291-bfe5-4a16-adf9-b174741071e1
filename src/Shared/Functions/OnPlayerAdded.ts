import {Janitor} from "@rbxts/janitor";
import {Players} from "@rbxts/services";

export function OnPlayerAdded(callback: (player: Player) => void) {
    const janitor = new Janitor();
    for (const player of Players.GetPlayers()) {
        callback(player);
    }

    const connection = Players.PlayerAdded.Connect((player) => callback(player));
    janitor.Add(() => connection.Disconnect());
    return () => janitor.Destroy();
}