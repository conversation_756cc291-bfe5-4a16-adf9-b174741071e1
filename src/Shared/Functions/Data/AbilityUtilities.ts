import { GetAbilityInfo } from "../../Resources/Combat/Abilities";

export function GetComputedAbilityDamage(player: Player, abilityId: string) {
  const abilityInfo = GetAbilityInfo(abilityId);
  if (abilityInfo === undefined) {
    return 1;
  }

  const statPoints = 1;
  const multiplier = 1 + statPoints * abilityInfo.StatScalingFactor;
  return abilityInfo.Damage * multiplier;
}

export function GetNpcComputedAbilityDamage(points: number, abilityId: string) {
  const abilityInfo = GetAbilityInfo(abilityId);
  if (abilityInfo === undefined) {
    return 1;
  }

  const multiplier = 1 + points * abilityInfo.StatScalingFactor;
  return abilityInfo.Damage * multiplier;
}
