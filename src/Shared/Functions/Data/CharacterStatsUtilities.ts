import {GetRaceInformation, TRaceId} from "../../Resources/Races";

export function GetScaledHealthFromDefense(defense: number): number {
    return 100 + math.abs(defense * 1.75);
}

export function GetBlockCapacityFromDefense(defense: number): number {
    return GetScaledHealthFromDefense(defense) * 0.5;
}

export function GetStaminaValueFromCharacterStaminaStat(staminaStat: number) {
    return 100 + math.abs(staminaStat * 2);
}

export function GetStaminaRegenFromTotalStamina(stamina: number) {
    return stamina * 0.01;
}

export function GetHealthRegenFromMaxHealth(maxHealth: number) {
    return maxHealth * 0.01;
}

export function GetHealthBuffFromRace(maxHealth: number, raceId: TRaceId) {
    const raceInformation = GetRaceInformation(raceId);
    return raceInformation.Buffs?.Base?.Health !== undefined
        ? raceInformation.Buffs.Base.Health.Raw + (maxHealth * raceInformation.Buffs.Base.Health.Multiplier)
        : 0;
}

export function GetStaminaBuffFromRace(maxStamina: number, raceId: TRaceId) {
    const raceInformation = GetRaceInformation(raceId);
    return raceInformation.Buffs?.Base?.Stamina !== undefined
        ? raceInformation.Buffs.Base.Stamina.Raw + (maxStamina * raceInformation.Buffs.Base.Stamina.Multiplier)
        : 0;
}