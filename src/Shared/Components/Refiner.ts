import { BaseComponent, Component } from "@flamework/components";
import { OnStart } from "@flamework/core";
import IRefinerData from "Shared/Data/Abstractions/Models/IRefinerData";
import { IRefiningQueueItem } from "Shared/Data/Abstractions/Models/IRefiningQueueItem";
import { HttpService, Players } from "@rbxts/services";
import { GetPlayerComponentFromPlayer } from "Shared/Extensions/PlayerExtensions";

interface IRefinerAttributes {
    /** Level of the refiner. */
    Level: number;
    /** Queue of items to refine. Serialized as JSON. */
    Queue: string;
    /** UserId of the player who owns this refiner. */
    Owner: number;
}

@Component({
    tag: "Refiner",
    defaults: {
        Level: 1,
        Queue: "[]",
        Owner: 0
    }
})
export class Refiner extends BaseComponent<IRefinerAttributes, Instance> implements OnStart {
    private player: Player | undefined = undefined;
    private data: IRefinerData | undefined = undefined;
    priv

    onStart(): void {
        this.onAttributeChanged("Owner", (newPlayerId, _) => {
            const newPlayer = Players.GetPlayerByUserId(newPlayerId);
            if (!newPlayer) {
                warn(`Could not find player with id ${newPlayerId} to load refiner data.`);
                return;
            }

            this.player = newPlayer;
            this.Initialize();
        });

        const player = Players.GetPlayerByUserId(this.attributes.Owner);
        if (!player) {
            if (this.attributes.Owner !== 0) {
                warn(`Could not find player with id '${this.attributes.Owner}' to load refiner data.`);
            }
            return;
        }

        this.player = player;
        this.Initialize();
    }

    /** Initializes the Refiner after a player has been set properly as its owner. */
    private Initialize() {
        if (this.player === undefined)
            return;

        this.player.Destroying.Once(() => this.Reset());
        const playerComponent = GetPlayerComponentFromPlayer(this.player);
        if (playerComponent === undefined)
            return;

        const playerData = playerComponent.WaitForData();
        if (playerData === undefined)
            return;

        this.data = playerData.Refiner;
    }

    /** Resets the Refiner when the player leaves. */
    private Reset() {
        this.data = undefined;
        this.player = undefined;
    }

    public GetQueue() {
        return this.data?.Queue ?? [];
    }

    public GetLevel() {
        return this.data?.Level ?? 1;
    }
}