import Maid from "@rbxts/maid";
import {HttpService} from "@rbxts/services";

import SimpleCast from "../InkLabs/SimpleCast";
import {GetAlliedCharacters, GetCharactersInRadius, GetMapPartsInRadius} from "../Libraries/SpatialLib";
import {CharacterModel} from "./CharacterModel";

export type TCastData = {
    Object: BasePart;
    InitVelocity: Vector3;
    InitPosition: Vector3;
    LifeTime: number;
    StartTime: number;
    Data: unknown;
};

export type TProjectileHitInfo = {
    Id: string;
    CFrame: CFrame;
    Characters: CharacterModel[];
    OtherParts: BasePart[];
}

type TOnUpdateCallback = (projectile: BasePart, id: string) => void;
type TOnCastCallback = (projectilePart: BasePart, castData: TCastData, id: string) => void;
type TOnHitCallback = (hitInfo?: TProjectileHitInfo) => void;
type TOnEndCallback = (id: string) => void;

type TProjectileProps = {
    Id?: string;
    Caster: CharacterModel;
    OriginPosition: Vector3;
    TargetPosition?: Vector3;
    TargetDirection?: Vector3;
    Params?: RaycastParams;
    Speed: number;
    Debug?: boolean;
    TerminateOnHit?: boolean;
    LifeTime?: number;

    Distance?: number;
    Size?: Vector3 | number;
    Gravity?: number;

    OnUpdate?: TOnUpdateCallback;
    OnCast?: TOnCastCallback;
    OnEnd?: TOnEndCallback;
    OnHit: TOnHitCallback;
};

const GetTimeFromDistanceAndSpeed = (distance: number, speed: number) => distance / speed;

export class Projectile {
    public readonly Id: string;
    private readonly maid: Maid;
    private hasHit: boolean;
    private readonly caster: CharacterModel;
    private position: Vector3;
    private readonly direction: Vector3;
    private readonly speed: number;
    private readonly gravity: number;
    private readonly distance: number;
    private readonly size: Vector3 | number;
    private readonly debug: boolean;
    private cast: SimpleCast;
    private terminateOnHit: boolean;
    private ended: boolean;

    private readonly onUpdateCallback: TOnUpdateCallback;
    private readonly onCastCallback: TOnCastCallback;
    private readonly onHitCallback: TOnHitCallback;
    private readonly onEndCallback: TOnEndCallback;

    constructor(props: TProjectileProps) {
        this.Id = props.Id ?? HttpService.GenerateGUID(false);
        this.maid = new Maid();
        this.caster = props.Caster;
        this.hasHit = false;
        this.position = props.OriginPosition;
        this.direction = props.TargetDirection
            ?? props.TargetPosition?.sub(props.OriginPosition).Unit
            ?? new Vector3(0, 0, 1);
        this.speed = props.Speed;
        this.gravity = props.Gravity !== undefined ? props.Gravity * -1 : 0;
        this.distance = props.Distance ?? 1000;
        this.size = props.Size ?? 1;
        this.debug = props.Debug ?? false;
        this.terminateOnHit = props.TerminateOnHit ?? true;
        this.ended = false;

        this.onEndCallback = (() => {
            props.OnEnd && props.OnEnd(this.Id);
            this.ended = true;
            this.maid.Destroy();
        });
        this.onCastCallback = props.OnCast ?? (() => {
        });
        this.onUpdateCallback = props.OnUpdate ?? (() => {
        });
        this.onHitCallback = props.OnHit ?? (() => {
        });

        const projectilePart = new Instance("Part");
        projectilePart.Size = typeIs(this.size, "Vector3")
            ? this.size : new Vector3(this.size, this.size, this.size);
        projectilePart.Anchored = true;
        projectilePart.CanCollide = false;

        if (this.debug) {
            projectilePart.BrickColor = BrickColor.random();
            projectilePart.Material = Enum.Material.Neon;
        } else {
            projectilePart.Transparency = 1;
        }

        const params: RaycastParams = new RaycastParams();
        if (!this.terminateOnHit) {
            // Ignores everything, so it wont end the cast on collision
            params.FilterType = Enum.RaycastFilterType.Include;
        } else {
            // Ignores only allies
            params.FilterDescendantsInstances = GetAlliedCharacters(this.caster);
            params.FilterDescendantsInstances.push(this.caster);
        }

        this.maid.GiveTask(projectilePart);

        // let hasReleased = false;
        this.cast = new SimpleCast(projectilePart, {
            Gravity: new Vector3(0, this.gravity, 0),
            RaycastParam: params,
            MaxLifeTime: props.LifeTime === undefined
                ? GetTimeFromDistanceAndSpeed(this.distance, this.speed)
                : props.LifeTime,
            CacheGrowthSize: 10,
            CacheSize: 50,
            OnCastHit: (settings, rayResult, castData, newPosition) => {
                !this.ended && this.Hit(newPosition, castData.Object);
            },
            OnCastMove: (settings, castData, newPosition, oldPosition) => {
                this.Update(newPosition, castData.Object);
            },
            OnCastTerminate: () => {
                this.terminateOnHit
                    ? !this.hasHit && this.onHitCallback()
                    : this.onEndCallback(this.Id);
            },
            CustomData: []
        });

        this.maid.GiveTask(() => this.cast.Destroy());
        projectilePart.Destroying.Once(() => !this.ended ? this.maid.Destroy() : undefined);
        // projectilePart.Parent = GetDebrisFolder();
        const castData = this.cast.FireCast(this.direction.mul(this.speed), this.position);
        this.onCastCallback(castData.Object, castData, this.Id);
    }

    public GetCharactersHitInCurrentFrame() {
        return GetCharactersInRadius(this.position, this.size, GetAlliedCharacters(this.caster));
    }

    public GetOtherPartsHitInCurrentFrame() {
        return GetMapPartsInRadius(this.position, this.size);
    }

    private Hit(currentPosition: Vector3, projectile: BasePart) {
        this.hasHit = true;
        this.onHitCallback({
            Id: this.Id,
            CFrame: projectile.CFrame,
            Characters: this.GetCharactersHitInCurrentFrame(),
            OtherParts: this.GetOtherPartsHitInCurrentFrame()
        });

        if (this.terminateOnHit)
            this.onEndCallback(this.Id);
    }

    private Update(newPosition: Vector3, projectile: BasePart) {
        if (this.ended)
            return;
        this.position = newPosition;
        this.onUpdateCallback(projectile, this.Id);

        if (this.size !== 1 && (this.GetCharactersHitInCurrentFrame()
            .size() > 0 || this.GetOtherPartsHitInCurrentFrame().size() > 0)) {
            this.Hit(newPosition, projectile);
        }
    }
}