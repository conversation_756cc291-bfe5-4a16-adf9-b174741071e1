import ISuperTween from "../InkLabs/SuperTween";

function applyKnockback(part: BasePart, direction: Vector3, power: number, duration: number = 0.15) {
    if (part.Anchored)
        return;

    const assemblyMass = part.Massless ? 15 : part.AssemblyMass;
    const adjustedPower = power * assemblyMass; // Adjust power to negate mass effect
    new ISuperTween(part, new TweenInfo(duration, Enum.EasingStyle.Exponential, Enum.EasingDirection.Out), {
        AssemblyLinearVelocity: direction.mul(adjustedPower)
    }).Play();
}

export function KnockbackFromOrigin(part: BasePart, origin: Vector3, power?: number) {
    if (origin.Y < part.Position.Y) {
        origin = origin.sub(new Vector3(0, part.Size.Y / 2, 0));
    }

    let direction = CFrame.lookAt(part.Position, origin).LookVector.mul(-1);
    applyKnockback(part, direction, power ?? 1);
}

export function KnockbackToDirection(part: BasePart, direction: Vector3, power?: number, duration?: number) {
    applyKnockback(part, direction, power ?? 1, duration ?? 0.15);
}