import {PhysicsService} from "@rbxts/services";

type TGroup = {
    Id: string;
    Exclusions: string[];
}

export const GROUPS: TGroup[] = [];

export const AddModelToCollisionGroup = (model: Model, groupId: string) => {
    if (!GROUPS.find(x => x.Id === groupId)) {
        RegisterCollisionGroup(groupId, []);
    }

    for (const obj of model.GetDescendants()) {
        if (obj.IsA("BasePart") || obj.IsA("UnionOperation") || obj.IsA("MeshPart")) {
            obj.CollisionGroup = groupId;
        }
    }
};

export const AddPartToCollisionGroup = (part: BasePart, groupId: string) => {
    if (!GROUPS.find(x => x.Id === groupId)) {
        RegisterCollisionGroup(groupId, []);
    }
    part.CollisionGroup = groupId;
};

export const RegisterCollisionGroup = (groupId: string, exclusions: string[]) => {
    if (GROUPS.find(x => x.Id === groupId)) {
        warn(`Collision group ${groupId} already exists!`);
        return;
    }
    GROUPS.push({Id: groupId, Exclusions: exclusions});

    if (!PhysicsService.IsCollisionGroupRegistered(groupId)) {
        PhysicsService.RegisterCollisionGroup(groupId);
    }

    exclusions.forEach((exclusion) => {
        if (!PhysicsService.IsCollisionGroupRegistered(exclusion)) {
            PhysicsService.RegisterCollisionGroup(exclusion);
        }

        PhysicsService.CollisionGroupSetCollidable(groupId, exclusion, false);
    });
};