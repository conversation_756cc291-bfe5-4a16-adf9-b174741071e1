import { BaseComponent, Component } from "@flamework/components";
import { OnStart } from "@flamework/core";
import { IPlayerData } from "Shared/Data/Abstractions/IPlayerData";
import { AddPlayerDataTagToPlayer } from "Shared/Extensions/PlayerExtensions";
import ProfileSync from "Shared/InkLabs/Data";

const DEFAULT_DATA_LOADING_TIMEOUT = 30;

@Component({
    tag: "Player",
})
export class PlayerComponent extends BaseComponent<{}, Player> implements OnStart {
    public readonly Id: string = tostring(this.instance.UserId);

    private data: IPlayerData | undefined = undefined;

    onStart(): void {
        warn(`PlayerComponent started for '${this.instance.Name}'.`);
        ProfileSync.GetProfileAsync(this.instance, 50).andThen((profile) => {
            if (profile === undefined)
                return;

            this.data = profile;
            warn(`Player data loaded for '${this.instance.Name}'.`, this.data);
            AddPlayerDataTagToPlayer(this.instance);
        }).catch(warn);
    }

    /**
     * @returns The player data, or undefined if it's not loaded yet.
     */
    public GetData(): IPlayerData | undefined {
        return this.data;
    }

    /**
     * @returns True if the player data is loaded, false otherwise.
     */
    public IsDataLoaded(): boolean {
        return this.data !== undefined;
    }

    /**
     * @param timeout Timeout in seconds. Defaults to 30 seconds.
     * @returns The player data, or undefined if the timeout was reached.
     */
    public WaitForData(timeout?: number): IPlayerData | undefined {
        const startTimestamp = os.clock();
        while (!this.IsDataLoaded() && (os.clock() - startTimestamp < (timeout ?? DEFAULT_DATA_LOADING_TIMEOUT))) {
            task.wait();
        }

        return this.data;
    }
}