import Iris from "@rbxts/iris";

export function IrisProgressBar(props: {
    title?: string;
    progress?: number;
    format?: string;
    onChange?: (progress: number) => void;
    onHover?: () => void;
    toolTip?: string;
}) {
    const progressBar = Iris.ProgressBar([props.title, props.format], {
        progress: props.progress
    });

    if (progressBar.hovered()) {
        props.onHover?.();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    if (progressBar.changed())
        props.onChange?.(progressBar.state.progress.get());

    return progressBar;
}