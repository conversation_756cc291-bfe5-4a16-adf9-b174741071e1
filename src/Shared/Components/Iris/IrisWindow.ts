import Iris from "@rbxts/iris";
import {Widget} from "@rbxts/iris/out/IrisDeclaration";
import {Source} from "@rbxts/vide";

export interface IIrisWindowProps {
    title: string;
    noTitleBar?: boolean;
    noBackground?: boolean;
    noCollapse?: boolean;
    noClose?: boolean;
    noMove?: boolean;
    noResize?: boolean;
    noNav?: boolean;
    noMenu?: boolean;
    size?: Vector2;
    position?: Vector2;
    openState?: Source<boolean>;
}

export const IrisWindow = (props: IIrisWindowProps, content?: () => void) => {
    const window = Iris.Window({
        1: props.title,
        2: props.noTitleBar,
        3: props.noBackground,
        4: props.noCollapse,
        5: props.noClose,
        6: props.noMove,
        7: props.noResize,
        8: props.noNav,
        9: props.noMenu
    }, {
        size: props.size !== undefined ? Iris.State(props.size) : undefined,
        position: props.position !== undefined ? Iris.State(props.position) : undefined,
        isUncollapsed: props.openState !== undefined
            ? props.openState() : undefined,
    });

    if (content !== undefined) {
        content();
    }

    if (props.openState !== undefined) {
        if (window.collapsed()) {
            props.openState(false);
        }

        if (window.uncollapsed()) {
            props.openState(true);
        }
    }

    Iris.End();
    return window;
};