import Iris from "@rbxts/iris";

export interface IIrisButtonProps {
    title: string;
    toolTip?: string;
    onClick?: () => void;
    onHover?: () => void;
    onRightClick?: () => void;
    onCtrlClick?: () => void;
    onDoubleClick?: () => void;
    noPadding?: boolean;
}

export const IrisButton = (props: IIrisButtonProps) => {
    const button = props.noPadding !== true
        ? Iris.Button([props.title])
        : Iris.SmallButton([props.title]);

    if (props.onClick !== undefined && button.clicked()) props.onClick();
    if (props.onHover !== undefined && button.hovered()) {
        props.onHover();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }
    if (props.onRightClick !== undefined && button.rightClicked()) props.onRightClick();
    if (props.onCtrlClick !== undefined && button.ctrlClicked()) props.onCtrlClick();
    if (props.onDoubleClick !== undefined && button.doubleClicked()) props.onDoubleClick();

    return button;
};