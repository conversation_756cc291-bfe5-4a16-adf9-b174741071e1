import Iris from "@rbxts/iris";

interface IIrisTextProps {
    text: string;
    wrapped?: boolean;
    color?: Color3;
    richText?: boolean;
    onHover?: () => void;
    toolTip?: string;
}

export function IrisText(props: IIrisTextProps) {
    const text = Iris.Text([props.text, props.wrapped, props.color, props.richText]);

    if (text.hovered()) {
        props.onHover?.();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return text;
}