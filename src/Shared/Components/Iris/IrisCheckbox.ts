import Iris from "@rbxts/iris";
import {Source} from "@rbxts/vide";

export interface IIrisCheckboxProps {
    title: string;
    value?: boolean;
    onChecked?: () => void;
    onUnchecked?: () => void;
    onHover?: () => void;
    toolTip?: string;
}

export function IrisCheckbox(props: IIrisCheckboxProps) {
    const checkbox = Iris.Checkbox([props.title], {
        isChecked: props.value !== undefined ? typeIs(props.value, "function") ? props.value() : props.value : undefined
    });
    if (props.onChecked !== undefined && checkbox.checked()) {
        props.onChecked();
    }

    if (props.onUnchecked !== undefined && checkbox.unchecked()) {
        props.onUnchecked();
    }

    if (props.onHover !== undefined && checkbox.hovered()) {
        props.onHover();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return checkbox;
}