import Iris from "@rbxts/iris";

export interface IIrisMenuItemProps {
    title: string;
    keyCode?: Enum.KeyCode;
    modifierKey?: Enum.ModifierKey;
    onActivated?: () => void;
    onHover?: () => void;
    toolTip?: string;
}

export const IrisMenuItem = (props: IIrisMenuItemProps) => {
    const item = Iris.MenuItem([props.title, props.keyCode, props.modifierKey]);
    if (props.onActivated !== undefined && item.clicked()) {
        props.onActivated();
    }

    if (props.onHover !== undefined && item.hovered()) {
        props.onHover();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return item;
};