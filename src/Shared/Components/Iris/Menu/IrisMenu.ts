import Iris from "@rbxts/iris";
import {Widget} from "@rbxts/iris/out/IrisDeclaration";
import {ButtonCreation} from "@rbxts/iris/out/widgetsTypes/Button";
import {MenuItemCreation, MenuToggleCreation} from "@rbxts/iris/out/widgetsTypes/Menu";
import {TextCreation} from "@rbxts/iris/out/widgetsTypes/Text";

export type TPossibleMenuItem =
    Widget<ButtonCreation>
    | Widget<TextCreation>
    | Widget<MenuItemCreation>
    | Widget<MenuToggleCreation>;

export const IrisMenu = (props: {
    title: string; onClick?: () => void; onHover?: () => void; toolTip?: string;
}, children?: () => TPossibleMenuItem[]) => {
    const menu = Iris.Menu([props.title]);
    if (children !== undefined)
        children();
    Iris.End();
    if (props.onClick !== undefined && menu.clicked()) {
        props.onClick();
    }

    if (props.onHover !== undefined && menu.hovered()) {
        props.onHover();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return menu;
};