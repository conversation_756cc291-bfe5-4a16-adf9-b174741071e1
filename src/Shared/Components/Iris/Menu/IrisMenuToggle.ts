import Iris from "@rbxts/iris";
import {State} from "@rbxts/iris/out/IrisDeclaration";

export interface IIrisMenuToggleProps {
    title: string;
    keycode?: Enum.KeyCode;
    modifier?: Enum.ModifierKey;
    onChecked?: () => void;
    onUnchecked?: () => void;
    onHover?: () => void;
    initialValue?: State<boolean>;
    toolTip?: string;
}

export const IrisMenuToggle = (props: IIrisMenuToggleProps) => {
    const toggle = Iris.MenuToggle([props.title, props.keycode, props.modifier], {
        isChecked: props.initialValue
    });
    if (props.onChecked !== undefined && toggle.checked()) {
        props.onChecked();
    }

    if (props.onUnchecked !== undefined && toggle.unchecked()) {
        props.onUnchecked();
    }

    if (props.onHover !== undefined && toggle.hovered()) {
        props.onHover();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }
    return toggle;
};