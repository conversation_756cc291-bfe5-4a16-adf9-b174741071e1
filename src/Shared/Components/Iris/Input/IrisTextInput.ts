import Iris from "@rbxts/iris";

interface IIrisTextInputProps {
    text?: string;
    placeholder?: string;
    readOnly?: boolean;
    multiLine?: boolean;
    onTextChanged?: (text: string) => void;
    onHover?: () => void;
    toolTip?: string;
}

export function IrisTextInput(props: IIrisTextInputProps) {
    const input = Iris.InputText([
        props.text, props.placeholder
    ]);

    if (input.textChanged())
        props.onTextChanged?.(input.state.text.get());
    
    if (input.hovered()) {
        props.onHover?.();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return input;
}