import Iris from "@rbxts/iris";

interface Props {
    color?: Color3;
    transparency?: number;
    text?: string;
    useTransparency?: boolean;
    useFloats?: boolean;
    useHsv?: boolean;
    format?: string | [string];
    onValueChanged?: (value: Color3) => void;
    onHover?: () => void;
    toolTip?: string;
}

export function IrisColor3Input(props: Props) {
    const input = props.useTransparency === undefined
        ? Iris.InputColor3([
            props.text,
            props.useFloats,
            props.useHsv,
            props.format
        ], {
            color: props.color
        }) : Iris.InputColor4([
            props.text,
            props.useFloats,
            props.useHsv,
            props.format
        ], {
            color: props.color,
            transparency: props.transparency
        });

    if (input.numberChanged())
        props.onValueChanged?.(input.state.color.get());

    if (input.hovered()) {
        props.onHover?.();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return input;
}