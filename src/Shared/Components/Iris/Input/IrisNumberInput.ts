import Iris from "@rbxts/iris";

interface Props {
    value?: number;
    text?: string;
    increment?: number;
    min?: number;
    max?: number;
    format?: string | [string];
    noButtons?: boolean;
    onValueChanged?: (value: number) => void;
    onHover?: () => void;
    toolTip?: string;
}

export function IrisNumberInput(props: Props) {
    const input = Iris.InputNum([
        props.text,
        props.increment,
        props.min,
        props.max,
        props.format,
        props.noButtons
    ], {
        number: props.value
    });

    if (input.numberChanged())
        props.onValueChanged?.(input.state.number.get());

    if (input.hovered()) {
        props.onHover?.();
        if (props.toolTip !== undefined)
            Iris.Tooltip([props.toolTip]);
    }

    return input;
}