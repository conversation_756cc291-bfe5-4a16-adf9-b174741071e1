import Iris from "@rbxts/iris";

interface IIrisTreeProps {
    title: string;
    expanded?: boolean;
    fullWidth?: boolean;
    noIndent?: boolean;
    onCollapsed?: () => void;
    onExpanded?: () => void;
    onHover?: () => void;
}

export function IrisTree(props: IIrisTreeProps, children?: () => void) {
    const tree = Iris.Tree([props.title, props.fullWidth, props.noIndent], {
        isUncollapsed: props.expanded
    });
    if (children !== undefined)
        children();

    if (props.onCollapsed !== undefined && tree.collapsed()) {
        props.onCollapsed();
    }

    if (props.onExpanded !== undefined && tree.uncollapsed()) {
        props.onExpanded();
    }

    if (props.onHover !== undefined && tree.hovered()) {
        props.onHover();
    }

    Iris.End();
    return tree;
}