import {TransformResult, <PERSON>Builder} from "@rbxts/centurion";
import {BoosterId, Boosters} from "../../../Resources/Boosters";

export const boosterIdType = TypeBuilder.create<string>("boosterId")
    .markForRegistration()
    .transform((text) => {
        const item = Boosters.includes(text as BoosterId);
        if (!item) {
            return TransformResult.err("Booster not found.");
        }

        return TransformResult.ok(text);
    }).suggestions(() => (Boosters as unknown) as string[])
    .build();