import {BaseComponent, Component} from "@flamework/components";
import {OnStart} from "@flamework/core";
import {Jan<PERSON>} from "@rbxts/janitor";
import {HttpService, RunService} from "@rbxts/services";
import {GetRootPart, WeldToPart} from "../Combat/Common";
import {GenericEffectsFolder} from "../Libraries/Directories";
import {CastRayOnMap} from "../Libraries/SpatialLib";

type VelocityParticlesPart = BasePart & {
    Lines: ParticleEmitter;
    Lines2: ParticleEmitter;
}

const VELOCITY_REQUIRED = 45;

@Component({
    tag: "Character"
})
export class VelocityTrackingComponent extends BaseComponent<{}, Model> implements OnStart {
    private janitor: Janitor = undefined!;
    private rootPart: BasePart = undefined!;
    private humanoid: Humanoid = undefined!;
    private velocityPart: VelocityParticlesPart = undefined!;

    onStart(): void {
        debug.setmemorycategory(script.Name);
        this.janitor = new Janitor();
        const rootPart = GetRootPart(this.instance, 40);
        const humanoid = rootPart !== undefined ? this.instance?.WaitForChild("Humanoid", 40) as Humanoid : undefined;
        if (rootPart === undefined || humanoid === undefined)
            return;

        this.humanoid = humanoid;
        this.rootPart = rootPart;

        const velocityPart = GenericEffectsFolder.WaitForChild("VelocityParticles", 20) as VelocityParticlesPart;
        if (velocityPart === undefined)
            return;

        this.velocityPart = velocityPart.Clone();
        this.velocityPart.Parent = rootPart;
        WeldToPart(this.velocityPart, rootPart);
        this.initClient();
    }

    destroy() {
        super.destroy();
        this.janitor.Destroy();
    }

    private initClient() {
        if (RunService.IsServer())
            return;

        const id = script.Name + HttpService.GenerateGUID(false);
        RunService.BindToRenderStep(id, Enum.RenderPriority.Last.Value, () => {
            debug.profilebegin(id);
            this.updateVelocityParticles();
            debug.profileend();
        })

        this.janitor.Add(() => {
            RunService.UnbindFromRenderStep(id)
        });
    }

    private updateVelocityParticles() {
        if (this.rootPart === undefined || this.humanoid === undefined)
            return;

        const velocity = this.rootPart.AssemblyLinearVelocity;
        const isGrounded = velocity.Magnitude < VELOCITY_REQUIRED
            ? true
            : CastRayOnMap(this.rootPart.Position, new Vector3(0, -1, 0), 5) !== undefined;

        if (velocity.Magnitude < VELOCITY_REQUIRED || isGrounded) {
            this.velocityPart.GetChildren().forEach(x =>
                x.IsA("ParticleEmitter") ? x.Enabled = false : undefined
            );
            return;
        }

        const rate = math.clamp(15 + velocity.Magnitude, 15, 50);
        const soundPitch = velocity.Magnitude / 40;
        const size = new NumberSequence([
            new NumberSequenceKeypoint(0, 0, 0),
            new NumberSequenceKeypoint(0.2, 1 + velocity.Magnitude / 100, 0),
            new NumberSequenceKeypoint(1, 0, 0)
        ]);

        this.velocityPart.GetChildren().forEach(x => {
            if (!x.IsA("ParticleEmitter")) return;
            x.Enabled = true;
            x.Rate = rate;
            x.Size = size;
        });
    }
}