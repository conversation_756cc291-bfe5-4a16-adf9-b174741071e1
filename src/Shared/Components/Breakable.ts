import { BaseComponent, Component } from "@flamework/components";
import { OnStart } from "@flamework/core";
import { HttpService, RunService } from "@rbxts/services";
import { IBreakableData } from "Shared/Data/Abstractions/Models/IBreakableData";
import { DamageContribution } from "Shared/Data/Components/DamageContribution";
import { GetBreakableById } from "Shared/Data/Items/Breakables";

interface IBreakableAttributes {
    Health: number;
    MaxHealth: number;
    /** Type is string, but it's a serialized JSON string table, like: [ "Shocked", "Cursed" ] */
    Modifiers: string;
}

@Component({
    tag: "Breakable",
    defaults: {
        Health: 100,
        MaxHealth: 100,
        Modifiers: "[]"
    }
})
export class Breakable extends BaseComponent<IBreakableAttributes, Instance> implements OnStart {
    /** The name of this breakable, which is also the id of the breakable data. */
    public readonly Name: string = this.instance.Name;

    /** The unique id of this breakable. */
    public readonly Id: string = HttpService.GenerateGUID(false);
    /** The data for this breakable. */
    public readonly Data: IBreakableData = GetBreakableById(this.Name) 
        ?? error(`Could not find breakable data for id '${this.Name}'.`);

    private readonly damageContribution = new DamageContribution();

    onStart(): void {
        this.damageContribution.SetDamageCap(this.attributes.MaxHealth);

        // For handling cases where the MaxHealth attribute was set, but not the Health.
        this.attributes.Health = this.attributes.MaxHealth;
    }

    public GetModifiers(): string[] {
        return HttpService.JSONDecode(this.attributes.Modifiers) as string[] ?? [];
    }

    public AddModifier(modifier: string) {
        if (RunService.IsClient())
            return;
        const modifiers = this.GetModifiers();
        if (modifiers.includes(modifier))
            return;

        modifiers.push(modifier);
        this.attributes.Modifiers = HttpService.JSONEncode(modifiers);
    }

    public RemoveModifier(modifier: string) {
        if (RunService.IsClient())
            return;
        const modifiers = this.GetModifiers();
        if (!modifiers.includes(modifier))
            return;

        modifiers.remove(modifiers.indexOf(modifier));
        this.attributes.Modifiers = HttpService.JSONEncode(modifiers);
    }

    public HasModifier(modifier: string) {
        return this.GetModifiers().includes(modifier);
    }

    public GetHealth(): number {
        return this.attributes.Health;
    }

    public GetMaxHealth(): number {
        return this.attributes.MaxHealth;
    }

    public IsDestroyed(): boolean {
        return this.attributes.Health <= 0;
    }

    public Damage(amount: number, contributorId: string) {
        if (RunService.IsClient())
            return;
        this.attributes.Health -= amount;
        this.damageContribution.AddDamageContributor(contributorId, amount);
        if (this.IsDestroyed()) {
            this.Destroy();
        }
    }

    public Destroy() {
        this.instance.Destroy();
    }

    public Heal(amount: number) {
        if (this.IsDestroyed() || RunService.IsClient())
            return;
        this.attributes.Health = math.min(this.attributes.Health + amount, this.attributes.MaxHealth);
    }

    public GetBreakableData(): IBreakableData {
        return this.Data;
    }

    public RewardContributors() {
        if (RunService.IsClient())
            return;
        // TODO: Reward contributors based on their damage contribution.
    }

    public GetCurrentContributionData() {
        return this.damageContribution.Contributors;
    }
}