import { RemoteEvent, Signal } from "./InkLabs/Network";
import { TChallengeId } from "./Resources/Challenges";
import { TItemType } from "./Resources/Inventory/Common/ItemTypes";
import { IInventoryItem } from "./Resources/Inventory/InventoryItem";
import { TRarityType } from "./Resources/Rarity";
import { Status } from "./Resources/StatusDatabase";

export interface IElementEventArgs {
  character: Model;
  statusId: string;
}

export interface IElementSignalArgs {
  character: Model;
  status: Status;
}

export type TCombatHitEventEventEnum =
  | "NormalHit"
  | "BlockHit"
  | "Parry"
  | "BlockBreak";

export const CameraShakeTriggeredSignal = new Signal<
  [intensity: number, duration: number]
>();

// Combat events
export const CooldownStartedEvent = new RemoteEvent<[string, number]>(
  "CooldownStartedEvent"
);
export const CombatHitEffectEvent = new RemoteEvent<
  [TCombatHitEventEventEnum, Model, string]
>("CombatHitEffectEvent", true);
export const FloatingDamageEvent = new RemoteEvent<
  [characterHit: Model, damage: number, critical: boolean]
>("FloatingDamageEvent");
export const ComboCounterEvent = new RemoteEvent<[damage: number]>(
  "ComboCounterEvent"
);

// Element events
export const ElementAppliedSignal = new Signal<[IElementSignalArgs]>();
export const ElementAppliedEvent = new RemoteEvent<[IElementEventArgs]>(
  "ElementApplied"
);
export const ElementRemovedEvent = new RemoteEvent<[IElementEventArgs]>(
  "ElementRemoved"
);
export const ElementProcEvent = new RemoteEvent<[IElementEventArgs]>(
  "ElementProc",
  true
);

// General events
export const CharacterAddedSignal = new Signal<[Player, Model]>();

// Inventory events
export const EquipItemEvent = new RemoteEvent<
  [itemId: string, equipmentId: string]
>("EquipItem");
export const UnequipItemEvent = new RemoteEvent<[itemId: string]>(
  "UnequipItem"
);
export const InventoryChangedSignal = new Signal<
  [itemType: TItemType, items: IInventoryItem[]]
>();
export const ItemEquippedEvent = new RemoteEvent<[itemId: string]>(
  "ItemEquippedEvent"
);
export const ItemUnequippedEvent = new RemoteEvent<[itemId: string]>(
  "ItemUnequippedEvent"
);
export const ToggleItemVanityEvent = new RemoteEvent<
  [itemId: string, enable: boolean | undefined]
>("ToggleItemVanity");

// Level events
export const PlayerLevelledUp = new RemoteEvent<
  [playerThatLevelledUp: Player, levels: number]
>("PlayerLevelledUp");

// Quest events
export const AcceptQuestEvent = new RemoteEvent<[questId: string]>(
  "AcceptQuest"
);
export const DismissQuestEvent = new RemoteEvent("DismissQuest");
export const AbandonQuestEvent = new RemoteEvent<[questId: string]>(
  "AbandonQuest"
);

// Confirmation prompt events
export const AskForConfirmationSignal = new Signal<
  [question: string, acceptCallback: () => void, dismissCallback: () => void]
>();

// Effects events
export const GeppoEffectEvent = new RemoteEvent<
  [originalPosition: Vector3, direction: Vector3]
>("GeppoEffect");
export const DashEffectEvent = new RemoteEvent<[character: Model]>(
  "DashEffect"
);
export const ExpOrbEffectEvent = new RemoteEvent<[origin: Vector3]>(
  "ExpOrbEffect"
);

// Customization events
export const ChangeHairIdEvent = new RemoteEvent<
  [slot: "Primary" | "Secondary", id: number]
>("ChangeHair");
export const ChangeHairColorEvent = new RemoteEvent<
  [slot: "Primary" | "Secondary", color: Color3]
>("ChangeHairColor");
export const ChangeSkinColorEvent = new RemoteEvent<[color: Color3]>(
  "ChangeSkinColor"
);
export const ChangeOutfitIdEvent = new RemoteEvent<
  [slot: "UpperBody" | "LowerBody" | "Shoes", id: number]
>("ChangeOutfitId");
export const ChangeOutfitColorEvent = new RemoteEvent<
  [
    slot: "UpperBody" | "LowerBody" | "Shoes",
    color: Color3,
    slotColor: "Primary" | "Secondary" | "Tertiary"
  ]
>("ChangeOutfitColor");

export const EndCustomizationEvent = new RemoteEvent("StopCustomizationEvent");
export const StartCustomizationEvent = new RemoteEvent(
  "StartCustomizationEvent"
);
export const CustomizationStartedEvent = new RemoteEvent(
  "CustomizationStartedEvent"
);
export const CustomizationFinishedEvent = new RemoteEvent(
  "CustomizationFinishedEvent"
);

export const ChangeTailEvent = new RemoteEvent<[id: number]>("ChangeTail");
export const ChangeEarsEvent = new RemoteEvent<[id: number]>("ChangeEars");

export const ChangeTailColorEvent = new RemoteEvent<[color: Color3]>(
  "ChangeTailColor"
);
export const ChangeEarsColorEvent = new RemoteEvent<[color: Color3]>(
  "ChangeEarsColor"
);

export const ChangeEyesEvent = new RemoteEvent<[id: number]>("ChangeEyes");
export const ChangeMouthEvent = new RemoteEvent<[id: number]>("ChangeMouth");

export const RerollPlayerRaceEvent = new RemoteEvent("RerollPlayerRace");

// Party events
export const CreatePartyEvent = new RemoteEvent("CreateParty");
export const JoinPartyEvent = new RemoteEvent<[partyId: string]>("JoinParty");
export const LeavePartyEvent = new RemoteEvent("LeaveParty");
export const DisbandPartyEvent = new RemoteEvent("DisbandParty");
export const InviteToPartyEvent = new RemoteEvent<[player: Player]>(
  "InviteToParty"
);
export const KickFromPartyEvent = new RemoteEvent<[player: Player]>(
  "KickFromParty"
);
export const PromotePartyLeaderEvent = new RemoteEvent<[player: Player]>(
  "PromotePartyLeader"
);

export const PartyCreatedEvent = new RemoteEvent<[partyId: string]>(
  "PartyCreated"
);
export const PartyInviteSentEvent = new RemoteEvent<
  [player: Player, partyId: string]
>("PartyInviteSent");
export const PartyMemberAddedEvent = new RemoteEvent<
  [player: Player, partyId: string]
>("PartyMemberAdded");
export const PartyMemberRemovedEvent = new RemoteEvent<
  [player: Player, partyId: string]
>("PartyMemberRemoved");
export const PartyLeaderChangedEvent = new RemoteEvent<
  [player: Player, partyId: string]
>("PartyLeaderChanged");
export const PartyDisbandedEvent = new RemoteEvent<[partyId: string]>(
  "PartyDisbanded"
);

// Challenge events
export const ChallengeStartedEvent = new RemoteEvent<
  [challengeId: TChallengeId]
>("ChallengeStartedEvent");
export const ChallengeFailedEvent = new RemoteEvent<
  [challengeId: TChallengeId]
>("ChallengeFailedEvent");
export const ChallengeCompletedEvent = new RemoteEvent<
  [challengeId: TChallengeId]
>("ChallengeCompletedEvent");

// Chests events
export const ClaimChestEvent = new RemoteEvent<[rewardId: string]>(
  "ClaimChest"
);
export const SpawnChestEvent = new RemoteEvent<
  [rewardId: string, chestId: string, cframe: CFrame, rarity: TRarityType]
>("SpawnChest");
export const ChestOpenedEvent = new RemoteEvent<[rewardId: string]>(
  "ChestOpened"
);
