import IPlotData from "./Models/IPlotData";
import IRefinerData from "./Models/IRefinerData";
import ITradeHistoryRegistry from "./Models/ITradeHistoryRegistry";

export interface IPlayerData {
  Coins: number;
  Shards: number;
  Refiner: IRefinerData;
  Plot: IPlotData;
  TradeHistory: ITradeHistoryRegistry[];
  Boosters: Map<string, number>;
  Passes: string[];
}

export type TPlayerDataField = keyof IPlayerData;
export type TPlayerDataValue<T extends TPlayerDataField> = IPlayerData[T];
