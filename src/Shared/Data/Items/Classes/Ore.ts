import { IOre } from "Shared/Data/Abstractions/Models/IOre";
import { TRarityType } from "Shared/Resources/Rarity";

export class Ore implements IOre {
    public readonly Id: string;
    public readonly Name: string;
    public readonly Rarity: TRarityType;
    public readonly Origins: string[];
    public readonly Description: string;
    public readonly Modifiers: string[];

    constructor(props: IOre) {
        this.Id = props.Id;
        this.Name = props.Name;
        this.Rarity = props.Rarity;
        this.Origins = props.Origins;
        this.Description = props.Description;
        this.Modifiers = props.Modifiers;
    }


}
