import { GetRandomItem } from "Shared/Resources/Rarity";
import { IBreakableData } from "../Abstractions/Models/IBreakableData";
import { GetPlayerLuck } from "Shared/Extensions/PlayerExtensions";

export const Breakables: IBreakableData[] = [];

export function GetBreakableById(id: string): IBreakableData | undefined {
    return Breakables.find((breakable) => breakable.Id === id);
}

export function GetBreakableByName(name: string): IBreakableData | undefined {
    return Breakables.find((breakable) => breakable.Name === name);
}

export function GetBreakablesWithDrop(oreId: string): IBreakableData[] {
    return Breakables.filter((breakable) => breakable.DropTable.has(oreId));
}

export function GetRandomDropFromBreakableForPlayer(breakableId: string, player: Player) {
    return GetRandomDropFromBreakable(breakableId, GetPlayerLuck(player));
}

export function GetRandomDropFromBreakable(breakableId: string, luckMultiplier: number = 0) {
    const breakable = GetBreakableById(breakableId);
    if (breakable === undefined)
        return;

    return GetRandomItem(breakable.DropTable, luckMultiplier);
}