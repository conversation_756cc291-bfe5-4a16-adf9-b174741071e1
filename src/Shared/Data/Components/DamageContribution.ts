/**
 * This class is used to track damage contributions from different sources (players, usually).
 */
export class DamageContribution {
    /** A map of Player Ids to the damage they contributed. */
    public readonly Contributors: Map<string, number> = new Map();
    public TotalDamage: number = 0;
    private totalDamageCap: number = math.huge;

    /**
     * Adds damage to the total damage, and to the contributor's damage.
     * @param contributorId The id of the contributor
     * @param damage The damage to add
     */
    public AddDamageContributor(contributorId: string, damage: number) {
        if (this.TotalDamage >= this.totalDamageCap) {
            return;
        }
        this.Contributors.set(contributorId, (this.GetDamageContributorDamage(contributorId)) + damage);
        this.TotalDamage += damage;
    }

    /**
     * @param contributorId The id of the contributor
     * @returns The percentage of damage contributed by the contributor
     */
    public GetDamageContributorPercentage(contributorId: string): number {
        return (this.Contributors.get(contributorId) ?? 0) / this.TotalDamage;
    }

    /**
     * @param contributorId The id of the contributor
     * @returns The damage contributed by the contributor
     */
    public GetDamageContributorDamage(contributorId: string): number {
        return this.Contributors.get(contributorId) ?? 0;
    }

    /**
     * @param damageCap The maximum damage that can be contributed
     */
    public SetDamageCap(damageCap: number) {
        this.totalDamageCap = damageCap;
    }
}