import Vide, { source } from "@rbxts/vide";
import { px } from "../Composables/usePx";

interface IGenericPanelProps {
    name: string;
    size: UDim2;
    anchorPoint: Vector2;
    position: UDim2;
    parent?: Vide.Derivable<Instance>;
    children?: Vide.Node;
    isOpen: Vide.Source<boolean>;
}

export const GenericPanel = ({ name, size, anchorPoint, position, parent, children, isOpen }: IGenericPanelProps) => {
    const topBarHeight = px(60);
    const closeButtonColor = source(Color3.fromRGB(0, 0, 0));

    return (
        <canvasgroup
            Size={size} BorderSizePixel={0} Name={name}
            AnchorPoint={anchorPoint} Position={position} Parent={parent}
            Visible={isOpen}
        >
            <uicorner CornerRadius={new UDim(0, px(10))} />
            <uipadding
                PaddingRight={new UDim(0, px(10))} PaddingLeft={new UDim(0, px(10))}
                PaddingTop={new UDim(0, px(10))} PaddingBottom={new UDim(0, px(20))}
            />
            <uilistlayout
                FillDirection={"Vertical"} VerticalAlignment={"Top"} VerticalFlex={"None"} Padding={new UDim(0, px(10))}
                HorizontalAlignment={"Center"} HorizontalFlex={"Fill"}
            />
            <frame Name={"TopBar"} Size={new UDim2(1, 0, 0, topBarHeight)}>
                <uipadding
                    PaddingRight={new UDim(0, px(20))} PaddingLeft={new UDim(0, px(20))}
                    PaddingTop={new UDim(0, px(5))} PaddingBottom={new UDim(0, px(5))}
                />
                <uicorner CornerRadius={new UDim(0, px(10))} />
                <uilistlayout
                    FillDirection={"Horizontal"} VerticalAlignment={"Top"} VerticalFlex={"Fill"} Padding={new UDim(0, px(10))}
                    HorizontalAlignment={"Left"} HorizontalFlex={"SpaceBetween"}
                />
                <frame BackgroundTransparency={1} Size={UDim2.fromScale(0.5, 1)}>
                    <uilistlayout
                        FillDirection={"Horizontal"} VerticalAlignment={"Top"} VerticalFlex={"Fill"} Padding={new UDim(0, px(5))}
                        HorizontalAlignment={"Left"} HorizontalFlex={"SpaceBetween"}
                    />
                    <textlabel BackgroundTransparency={1} Text={name} TextScaled={true} Size={UDim2.fromScale(0.5, 1)} />
                </frame>
                <frame BackgroundTransparency={1} Size={UDim2.fromScale(0.5, 1)}>
                    <uilistlayout
                        FillDirection={"Horizontal"} VerticalAlignment={"Top"} VerticalFlex={"Fill"} Padding={new UDim(0, px(5))}
                        HorizontalAlignment={"Right"} HorizontalFlex={"None"}
                    />
                    <textbutton
                        Text={"X"}
                        BackgroundTransparency={0.9}
                        BackgroundColor3={closeButtonColor}
                        TextScaled={true}
                        SizeConstraint={"RelativeYY"}
                        Size={UDim2.fromScale(1, 1)}
                        MouseButton1Down={() => {
                            print("Close button clicked");
                            isOpen(false);
                        }}
                        MouseEnter={() => closeButtonColor(Color3.fromRGB(255, 0, 0))}
                        MouseLeave={() => closeButtonColor(Color3.fromRGB(0, 0, 0))}
                    >
                        <uicorner CornerRadius={new UDim(0, px(10))} />
                    </textbutton>
                </frame>
            </frame>
            <frame Name={"Content"} Size={new UDim2(1, 0, 1, -topBarHeight)}>
                <uicorner CornerRadius={new UDim(0, px(10))} />
                {children}
            </frame>
        </canvasgroup>
    )
}