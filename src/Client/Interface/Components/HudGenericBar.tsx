import Vide, {Source} from "@rbxts/vide";
import { GetDarkerColor } from "../../../Shared/Functions/UIHelpers";

interface IProps {
    Capacity: Source<number>;
    Value: Source<number>;
    MainColor: Source<Color3>;
    FillColor?: Source<Color3>;
    Size?: Source<UDim2>;
    Position?: Source<UDim2>;
    AnchorPoint?: Source<Vector2>;
}

export function HudGenericBar(props: IProps) {
    return (
        (
            <frame
                AnchorPoint={props.AnchorPoint ?? new Vector2(0, 1)}
                BackgroundTransparency={1}
                Position={props.Position ?? UDim2.fromScale(0, 1)}
                Size={props.Size ?? UDim2.fromScale(1, 0.4)}
                Name={"Base"}
            >
                <uicorner
                    CornerRadius={new UDim(1, 0)}
                />

                <frame
                    Name={"Shadow"}
                    BackgroundColor3={Color3.fromRGB(47, 47, 47)}
                    BorderColor3={Color3.fromRGB(27, 42, 53)}
                    BorderSizePixel={0}
                    Position={UDim2.fromScale(0, 0.6)}
                    Size={UDim2.fromScale(1, 0.4)}
                    Visible={false}
                    ZIndex={2}
                />

                <imagelabel
                    Name={"Outline"}
                    BackgroundTransparency={1}
                    Image={"http://www.roblox.com/asset/?id=15589489991"}
                    ImageColor3={Color3.fromRGB(25, 25, 25)}
                    Size={UDim2.fromScale(1, 1)}
                    ZIndex={6}
                />

                <frame
                    Name={"Bar"}
                    AnchorPoint={new Vector2(0, 0.5)}
                    BackgroundColor3={Color3.fromRGB(50, 50, 50)}
                    BorderColor3={new Color3()}
                    BorderSizePixel={0}
                    Position={UDim2.fromScale(0.01, 0.5)}
                    Size={UDim2.fromScale(0.98, 0.6)}
                    ZIndex={0}
                >
                    <frame
                        Name={"Fill"}
                        AnchorPoint={new Vector2(0, 0.5)}
                        BackgroundColor3={props.MainColor}
                        BorderColor3={Color3.fromRGB(27, 42, 53)}
                        BorderSizePixel={0}
                        Position={UDim2.fromScale(0, 0.5)}
                        Size={() => UDim2.fromScale(math.clamp(props.Value() / props.Capacity(), 0, 1), 1)}
                        ZIndex={3}
                    >
                        <frame
                            Name={"Shadow"}
                            BackgroundColor3={() => GetDarkerColor(props.MainColor(), 0.15)}
                            BorderColor3={Color3.fromRGB(27, 42, 53)}
                            BorderSizePixel={0}
                            Position={UDim2.fromScale(0, 0.6)}
                            Size={UDim2.fromScale(1, 0.4)}
                            ZIndex={4}
                        />
                    </frame>

                    <frame
                        Name={"FillLag"}
                        AnchorPoint={new Vector2(0, 0.5)}
                        BackgroundColor3={new Color3(1, 1, 1)}
                        BorderColor3={Color3.fromRGB(27, 42, 53)}
                        BorderSizePixel={0}
                        Position={UDim2.fromScale(0, 0.5)}
                        Size={UDim2.fromScale(0.75, 1)}
                        ZIndex={0}
                    />
                </frame>
            </frame>
        )
    );
}