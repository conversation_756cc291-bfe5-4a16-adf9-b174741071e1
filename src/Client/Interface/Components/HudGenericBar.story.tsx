import {CreateVideStory} from "@rbxts/ui-labs";
import Vide, {source} from "@rbxts/vide";
import {HudGenericBar} from "./HudGenericBar";

const HudGenericBarStory = CreateVideStory({
    vide: Vide,
    name: "HudGenericBarStory",
    use: "vide"
}, () => {
    const value = source(75);
    const cap = source(100);
    const color = source(Color3.fromRGB(255, 135, 135));
    return (<frame
        BackgroundTransparency={1}
        Size={new UDim2(0, 1000, 0, 200)}
    >
        <HudGenericBar
            Value={value}
            Capacity={cap}
            MainColor={color}
        />
    </frame>);
});

export = HudGenericBarStory;