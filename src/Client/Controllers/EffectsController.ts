import {Controller, OnStart} from "@flamework/core";
import {Register, Start} from "@rbxts/refx";
import {Players} from "@rbxts/services";
import BasicHitEffect from "../../Shared/Assets/Effects/BasicHitEffect";
import BlockHitEffect from "../../Shared/Assets/Effects/BlockHitEffect";
import FloatingDamageEffect from "../../Shared/Assets/Effects/FloatingDamageEffect";
import {GenericCharacterEffect} from "../../Shared/Assets/Effects/GenericCharacterEffect";
import PerfectBlockScreenEffect from "../../Shared/Assets/Effects/PerfectBlockScreenEffect";

import {WCSDirectories} from "../../Shared/Libraries/Directories";
import { CombatHitEffectEvent, DashEffectEvent, ExpOrbEffectEvent, FloatingDamageEvent, GeppoEffectEvent, PlayerLevelledUp } from "../../Shared/Networking";
import GeppoEffect from "Shared/Assets/Effects/GeppoEffect";
import DashEffect from "Shared/Assets/Effects/DashEffect";
import ExpOrbEffect from "Shared/Assets/Effects/ExpOrbEffect";

@Controller()
export class EffectsController implements OnStart {
    onStart() {
        warn(`Starting ${script.Name}`);
        Register(WCSDirectories.Visuals);
        Start();

        CombatHitEffectEvent.OnClientEvent.Connect(
            (ctx, character, weaponId) => {
                if (ctx === "BlockBreak")
                    GenericCharacterEffect("BlockBreak", character);
                else if (ctx === "BlockHit")
                    BlockHitEffect(character);
                else if (ctx === "NormalHit")
                    BasicHitEffect(character, weaponId);
                else if (ctx === "Parry") {
                    GenericCharacterEffect("PerfectBlock", character);
                    if (Players.GetPlayerFromCharacter(character)?.UserId === Players.LocalPlayer.UserId) {
                        PerfectBlockScreenEffect();
                    }
                }
            });

        FloatingDamageEvent.OnClientEvent.Connect((character, damage, critical) => {
            FloatingDamageEffect(character, damage, critical);
        });

        PlayerLevelledUp.OnClientEvent.Connect((playerThatLevelledUp, levelsGained) => {
            if (playerThatLevelledUp.Character === undefined)
                return;
            GenericCharacterEffect("LevelUpFx", playerThatLevelledUp.Character);
        });

        GeppoEffectEvent.OnClientEvent.Connect((originalPosition, direction) => {
            GeppoEffect(originalPosition, direction);
        });

        DashEffectEvent.OnClientEvent.Connect((character) => {
            DashEffect(character);
        });

        ExpOrbEffectEvent.OnClientEvent.Connect((origin) => {
            ExpOrbEffect(origin);
        });

        warn(`${script.Name} started successfully.`);
    }
}