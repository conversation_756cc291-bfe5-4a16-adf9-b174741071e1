import {<PERSON>, OnStart} from "@flamework/core";
import {Centurion} from "@rbxts/centurion";
import {CenturionUI} from "@rbxts/centurion-ui";
import {Players, ReplicatedStorage} from "@rbxts/services";
import Shared from "../../Shared/Cmdr";

@Controller()
export class AdminController implements OnStart {
    onStart() {
        warn(`Starting ${script.Name}`);
        task.spawn(() => {
            const client = Centurion.client();

            const typesContainer = ReplicatedStorage
                .WaitForChild("TS")
                .WaitForChild("Components")
                .WaitForChild("Centurion")
                .WaitForChild("Types");
            client.registry.load(typesContainer, true);
            client.start().then(() => CenturionUI.start(client, {
                    activationKeys: Shared.Admins.includes(Players.LocalPlayer.UserId) ? [
                        Enum.KeyCode.RightAlt
                    ] : []
                }))
                .catch((err) => warn("Failed to start Centurion:", err));
        })
    }
}