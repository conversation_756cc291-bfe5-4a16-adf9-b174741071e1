import { <PERSON>, OnStart } from "@flamework/core";
import { Players } from "@rbxts/services";
import { InitRagdollVelocityTracking } from "../../Shared/Functions/InitRagdollVelocityTracking";
import ProfileSync from "../../Shared/InkLabs/Data";

import { SetFilterList } from "../../Shared/InkLabs/Mouse";
import { GetDebrisFolder } from "../../Shared/Libraries/Directories";

@Controller()
export class CharacterController implements OnStart {
  onStart() {
    warn(`Starting ${script.Name}`);
    SetFilterList([GetDebrisFolder()]);
    InitRagdollVelocityTracking();
    warn(`${script.Name} started successfully.`);

    const profile = ProfileSync.GetProfile(Players.LocalPlayer, 50);
    if (profile === undefined) return;
  }
}
