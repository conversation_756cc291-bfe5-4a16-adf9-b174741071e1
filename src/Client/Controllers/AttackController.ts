import {<PERSON>, OnStart} from "@flamework/core";
import {CreateClient} from "@rbxts/wcs";
import {WCSDirectories} from "Shared/Libraries/Directories";

@Controller()
export class AttackController implements OnStart {
    private wcs = CreateClient();

    onStart() {
        warn(`Starting ${script.Name}`);
        this.wcs.RegisterDirectory(WCSDirectories.Skills);
        this.wcs.RegisterDirectory(WCSDirectories.StatusEffects);
        this.wcs.Start();
        warn(`${script.Name} started successfully.`);
    }
}