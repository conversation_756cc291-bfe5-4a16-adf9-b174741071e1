import {Controller, OnTick} from "@flamework/core";
import {Players} from "@rbxts/services";
import {CharacterModel} from "../../Shared/Components/CharacterModel";
import {GetMouseHit, SetFilterList} from "../../Shared/InkLabs/Mouse";
import {GetMapFolder} from "../../Shared/Libraries/Directories";
import {GetEnemyCharacters} from "../../Shared/Libraries/SpatialLib";
import {Packets} from "../../Shared/Resources/Packets";

const UPDATE_COOLDOWN = 0.05;

@Controller()
export class MouseController implements OnTick {
    private lastUpdate = 0;
    private lastPosition = Vector3.zero;

    onTick(dt: number) {
        if (Players.LocalPlayer.Character === undefined)
            return;

        if (os.clock() - this.lastUpdate < UPDATE_COOLDOWN)
            return;

        SetFilterList(
            [...GetEnemyCharacters(Players.LocalPlayer.Character as CharacterModel), GetMapFolder()]
        );
        const currentPosition = GetMouseHit();
        if (this.lastPosition.sub(currentPosition).Magnitude < 1)
            return;

        this.lastPosition = currentPosition;
        this.lastUpdate = os.clock();
        Packets.Replicators.MousePosition.send(currentPosition);
    }
}