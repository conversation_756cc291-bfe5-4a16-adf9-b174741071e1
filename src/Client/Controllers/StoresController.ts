import { Controller, OnStart } from "@flamework/core";
import { Players } from "@rbxts/services";
import { source } from "@rbxts/vide";
import { OnPlayerAdded } from "../../Shared/Functions/OnPlayerAdded";
import { OnPlayerRemoving } from "../../Shared/Functions/OnPlayerRemoving";
import { Signal } from "../../Shared/InkLabs/Network";

// Global
export const PlayersInGame = source([] as Player[]);

// Party
export const PartyMembers = source([] as Player[]);
export const PartyId = source(undefined as string | undefined);
export const PartyLeader = source(undefined as Player | undefined);

@Controller()
export class StoresController implements OnStart {
  private readonly playersChangedSignal = new Signal<[Player[]]>();

  onStart() {
    this.InitGlobalStore();
  }

  private InitGlobalStore() {
    PlayersInGame(Players.GetPlayers());

    OnPlayerAdded(() => {
      this.playersChangedSignal.Fire(Players.GetPlayers());
    });

    OnPlayerRemoving(() => {
      this.playersChangedSignal.Fire(Players.GetPlayers());
    });

    this.playersChangedSignal.Connect((players) => PlayersInGame(players));
  }
}
