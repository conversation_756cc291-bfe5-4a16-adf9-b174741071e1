import {Controller, OnInit} from "@flamework/core";
import {Players, Workspace} from "@rbxts/services";
import Notify from "Shared/InkLabs/Notify";

import ProfileSync from "../../Shared/InkLabs/Data";
import {PlayerDataDefaults} from "../../Shared/Resources/DataAbstractions";

@Controller()
export class DataController implements OnInit {
    onInit() {
        warn(`Starting ${script.Name}`);
        ProfileSync.Init({
            Structure: PlayerDataDefaults,
            FilterStrictness: "Light",
            FilterType: "Blacklist"
        });
        
        const profileData = ProfileSync.GetProfile(Players.LocalPlayer, 50);
        if (profileData === undefined)
            Players.LocalPlayer.Kick("Could not load player data on the client.");

        // if (RunService.IsStudio()) {
        //     const tableViewer = new TableViewer(profileData);
        //     ProfileSync.BindAll(Players.LocalPlayer, () => {
        //         const updatedProfile = ProfileSync.GetProfile(Players.LocalPlayer);
        //         if (updatedProfile === undefined)
        //             return;
        //         tableViewer.Update(updatedProfile);
        //     });
        // }

        warn("Player data loaded on client:", profileData);
        Workspace.AddTag("DataLoaded");
        warn(`${script.Name} started successfully.`);

        Notify({
            Header: "Welcome to the game!",
            Description: "Welcome, " + Players.LocalPlayer.DisplayName + "!",
            Style: "Tip",
            Duration: 2
        });
    }
}