{
  "include": [
    "**/*.ts",
    "**/*.tsx",
    "**/*.d.ts"
  ],
  "exclude": [
    "node_modules",
    "out",
    "src/**/*.lua",
    "src/**/*.luau"
  ],
  "compilerOptions": {
    // required
    "allowSyntheticDefaultImports": true,
    "downlevelIteration": true,
    "jsx": "react",
    "jsxFactory": "Vide.jsx",
    "jsxFragmentFactory": "Vide.Fragment",
    "module": "commonjs",
    "moduleResolution": "Node",
    "noLib": true,
    "resolveJsonModule": true,
    "forceConsistentCasingInFileNames": true,
    "moduleDetection": "force",
    "strict": true,
    "target": "ESNext",
    "typeRoots": [
      "node_modules/@rbxts",
      "node_modules/@flamework"
    ],
    // configurable
    "rootDir": "src",
    "outDir": "out",
    "baseUrl": "src",
    "incremental": true,
    "tsBuildInfoFile": "out/tsconfig.tsbuildinfo",
    "experimentalDecorators": true,
    "plugins": [
      {
        "transform": "rbxts-transformer-flamework",
        "obfuscation": false
      }
    ],
    "allowArbitraryExtensions": true
  }
}