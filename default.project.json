{"name": "dungeon-game", "globIgnorePaths": ["**/package.json", "**/tsconfig.json"], "tree": {"$className": "DataModel", "ServerScriptService": {"$className": "ServerScriptService", "TS": {"$path": "out/Server"}}, "ReplicatedStorage": {"$className": "ReplicatedStorage", "rbxts_include": {"$path": "include", "node_modules": {"$className": "Folder", "@rbxts": {"$path": "node_modules/@rbxts"}, "@flamework": {"$path": "node_modules/@flamework"}}}, "TS": {"$path": "out/Shared"}}, "StarterPlayer": {"$className": "StarterPlayer", "StarterPlayerScripts": {"$className": "StarterPlayerScripts", "TS": {"$path": "out/Client"}}}, "Workspace": {"$className": "Workspace", "$properties": {"StreamingEnabled": false, "AllowThirdPartySales": true, "PlayerCharacterDestroyBehavior": "Enabled", "RenderingCacheOptimizations": "Enabled"}}, "HttpService": {"$className": "HttpService", "$properties": {"HttpEnabled": true}}}}